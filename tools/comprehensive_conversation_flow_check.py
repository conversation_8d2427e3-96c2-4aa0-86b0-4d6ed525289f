#!/usr/bin/env python3
"""
conversation_flow.py 核心模块全面检查

目标：
1. 验证AcceleratedIntentDecisionEngine与原IntentDecisionEngine的完全兼容性
2. 检查所有核心功能是否正常工作
3. 验证状态管理、消息处理、决策流程等关键功能
4. 确保修改不会破坏现有业务逻辑
5. 测试各种边界情况和异常处理
"""

import asyncio
import time
import logging
import sys
import os
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from backend.agents.conversation_flow import AutoGenConversationFlowAgent
    from backend.agents.accelerated_intent_decision_engine import AcceleratedIntentDecisionEngine
    from backend.agents.session_context import SessionContext, ConversationState
    print("✅ 成功导入核心模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


class ComprehensiveConversationFlowChecker:
    """conversation_flow.py 全面检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = {
            "compatibility_tests": [],
            "functionality_tests": [],
            "performance_tests": [],
            "edge_case_tests": [],
            "integration_tests": []
        }
    
    async def run_all_checks(self):
        """运行所有检查"""
        print("🔍 conversation_flow.py 核心模块全面检查")
        print("=" * 60)
        
        try:
            # 1. 兼容性检查
            await self.check_engine_compatibility()
            
            # 2. 核心功能检查
            await self.check_core_functionality()
            
            # 3. 状态管理检查
            await self.check_state_management()
            
            # 4. 消息处理检查
            await self.check_message_processing()
            
            # 5. 决策流程检查
            await self.check_decision_flow()
            
            # 6. 性能影响检查
            await self.check_performance_impact()
            
            # 7. 边界情况检查
            await self.check_edge_cases()
            
            # 8. 集成测试
            await self.check_integration()
            
            # 总结检查结果
            self.summarize_results()
            
        except Exception as e:
            print(f"\n❌ 检查过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def check_engine_compatibility(self):
        """检查引擎兼容性"""
        print("\n🔧 检查引擎兼容性...")
        
        try:
            # 创建模拟LLM服务
            mock_llm = Mock()
            mock_llm.config_manager = Mock()
            mock_llm.get_unified_config().get_model_config.return_value = {"temperature": 0.7}
            
            # 创建原版引擎
            original_engine = IntentDecisionEngine(mock_llm)
            
            # 创建加速版引擎
            accelerated_engine = AcceleratedIntentDecisionEngine(mock_llm)
            
            # 检查接口兼容性
            print("🔍 检查接口兼容性...")
            
            # 检查analyze方法签名
            original_analyze = getattr(original_engine, 'analyze', None)
            accelerated_analyze = getattr(accelerated_engine, 'analyze', None)
            
            if original_analyze and accelerated_analyze:
                print("   ✅ analyze方法存在于两个引擎中")
                self.test_results["compatibility_tests"].append({
                    "test": "analyze_method_exists",
                    "status": "PASS",
                    "details": "两个引擎都有analyze方法"
                })
            else:
                print("   ❌ analyze方法缺失")
                self.test_results["compatibility_tests"].append({
                    "test": "analyze_method_exists",
                    "status": "FAIL",
                    "details": "analyze方法缺失"
                })
            
            # 检查返回值格式兼容性
            print("🔍 检查返回值格式兼容性...")
            
            test_message = "你好"
            test_context = [{"current_state": "IDLE", "session_id": "test"}]
            
            try:
                # 模拟原版引擎的返回
                original_result = {
                    "intent": "greeting",
                    "emotion": "neutral",
                    "confidence": 0.95,
                    "entities": {},
                    "decision": {"action": "respond_with_greeting"}
                }
                
                # 测试加速版引擎
                accelerated_result = await accelerated_engine.analyze(test_message, test_context)
                
                # 检查关键字段
                required_fields = ["intent", "emotion", "confidence", "entities", "decision"]
                missing_fields = []
                
                for field in required_fields:
                    if field not in accelerated_result:
                        missing_fields.append(field)
                
                if not missing_fields:
                    print("   ✅ 返回值格式兼容")
                    self.test_results["compatibility_tests"].append({
                        "test": "return_format_compatibility",
                        "status": "PASS",
                        "details": "返回值包含所有必需字段"
                    })
                else:
                    print(f"   ❌ 返回值格式不兼容，缺少字段: {missing_fields}")
                    self.test_results["compatibility_tests"].append({
                        "test": "return_format_compatibility",
                        "status": "FAIL",
                        "details": f"缺少字段: {missing_fields}"
                    })
                
            except Exception as e:
                print(f"   ❌ 返回值格式检查失败: {e}")
                self.test_results["compatibility_tests"].append({
                    "test": "return_format_compatibility",
                    "status": "FAIL",
                    "details": str(e)
                })
            
            print("✅ 引擎兼容性检查完成")
            
        except Exception as e:
            print(f"❌ 引擎兼容性检查失败: {e}")
            self.test_results["compatibility_tests"].append({
                "test": "engine_compatibility",
                "status": "FAIL",
                "details": str(e)
            })
    
    async def check_core_functionality(self):
        """检查核心功能"""
        print("\n🎯 检查核心功能...")
        
        try:
            # 创建模拟组件
            mock_llm = self._create_mock_llm()
            
            # 创建conversation_flow实例
            conversation_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 检查关键组件是否正确初始化
            print("🔍 检查组件初始化...")
            
            components_to_check = [
                ("intent_decision_engine", "意图决策引擎"),
                ("db_manager", "数据库管理器"),
                ("session_context_manager", "会话上下文管理器"),
                ("message_manager", "消息管理器"),
                ("focus_point_manager", "关注点管理器")
            ]
            
            for component_name, display_name in components_to_check:
                component = getattr(conversation_agent, component_name, None)
                if component:
                    print(f"   ✅ {display_name}初始化成功")
                    self.test_results["functionality_tests"].append({
                        "test": f"{component_name}_initialization",
                        "status": "PASS",
                        "details": f"{display_name}正确初始化"
                    })
                else:
                    print(f"   ❌ {display_name}初始化失败")
                    self.test_results["functionality_tests"].append({
                        "test": f"{component_name}_initialization",
                        "status": "FAIL",
                        "details": f"{display_name}未正确初始化"
                    })
            
            # 检查意图决策引擎类型
            engine_type = type(conversation_agent.intent_decision_engine).__name__
            print(f"🔍 意图决策引擎类型: {engine_type}")
            
            if engine_type == "AcceleratedIntentDecisionEngine":
                print("   ✅ 使用加速版意图决策引擎")
                self.test_results["functionality_tests"].append({
                    "test": "engine_type_check",
                    "status": "PASS",
                    "details": "正确使用AcceleratedIntentDecisionEngine"
                })
            else:
                print("   ⚠️ 未使用加速版意图决策引擎")
                self.test_results["functionality_tests"].append({
                    "test": "engine_type_check",
                    "status": "WARNING",
                    "details": f"使用的是{engine_type}而不是AcceleratedIntentDecisionEngine"
                })
            
            print("✅ 核心功能检查完成")
            
        except Exception as e:
            print(f"❌ 核心功能检查失败: {e}")
            self.test_results["functionality_tests"].append({
                "test": "core_functionality",
                "status": "FAIL",
                "details": str(e)
            })
    
    async def check_state_management(self):
        """检查状态管理"""
        print("\n📊 检查状态管理...")
        
        try:
            mock_llm = self._create_mock_llm()
            conversation_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 测试状态转换
            print("🔍 测试状态转换...")
            
            test_states = [
                ConversationState.IDLE,
                ConversationState.GATHERING,
                ConversationState.DOCUMENTING
            ]
            
            for state in test_states:
                try:
                    # 创建测试会话上下文
                    session_context = SessionContext(
                        session_id="test_session",
                        user_id="test_user",
                        current_state=state
                    )
                    
                    print(f"   ✅ 状态 {state.name} 创建成功")
                    self.test_results["functionality_tests"].append({
                        "test": f"state_{state.name}_creation",
                        "status": "PASS",
                        "details": f"状态{state.name}正确创建"
                    })
                    
                except Exception as e:
                    print(f"   ❌ 状态 {state.name} 创建失败: {e}")
                    self.test_results["functionality_tests"].append({
                        "test": f"state_{state.name}_creation",
                        "status": "FAIL",
                        "details": str(e)
                    })
            
            print("✅ 状态管理检查完成")
            
        except Exception as e:
            print(f"❌ 状态管理检查失败: {e}")
    
    async def check_message_processing(self):
        """检查消息处理"""
        print("\n💬 检查消息处理...")
        
        try:
            mock_llm = self._create_mock_llm()
            conversation_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 测试不同类型的消息处理
            test_messages = [
                {"message": "你好", "expected_intent": "greeting", "description": "问候消息"},
                {"message": "你的功能", "expected_intent": "capabilities", "description": "功能查询"},
                {"message": "新聊天", "expected_intent": "restart", "description": "重启请求"},
                {"message": "确认", "expected_intent": "confirm", "description": "确认消息"},
            ]
            
            print("🔍 测试消息处理...")
            
            for test_case in test_messages:
                try:
                    # 创建测试上下文
                    context = [{
                        "session_id": "test_session",
                        "user_id": "test_user",
                        "current_state": "IDLE"
                    }]
                    
                    # 调用意图决策引擎
                    result = await conversation_agent.intent_decision_engine.analyze(
                        test_case["message"], context
                    )
                    
                    if result and "intent" in result:
                        print(f"   ✅ {test_case['description']}: {test_case['message']} -> {result['intent']}")
                        self.test_results["functionality_tests"].append({
                            "test": f"message_processing_{test_case['description']}",
                            "status": "PASS",
                            "details": f"消息'{test_case['message']}'正确处理为{result['intent']}"
                        })
                    else:
                        print(f"   ❌ {test_case['description']}: {test_case['message']} -> 处理失败")
                        self.test_results["functionality_tests"].append({
                            "test": f"message_processing_{test_case['description']}",
                            "status": "FAIL",
                            "details": f"消息'{test_case['message']}'处理失败"
                        })
                        
                except Exception as e:
                    print(f"   ❌ {test_case['description']}: {test_case['message']} -> 异常: {e}")
                    self.test_results["functionality_tests"].append({
                        "test": f"message_processing_{test_case['description']}",
                        "status": "FAIL",
                        "details": str(e)
                    })
            
            print("✅ 消息处理检查完成")
            
        except Exception as e:
            print(f"❌ 消息处理检查失败: {e}")
    
    async def check_decision_flow(self):
        """检查决策流程"""
        print("\n🎯 检查决策流程...")
        
        try:
            mock_llm = self._create_mock_llm()
            conversation_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 测试决策流程的完整性
            print("🔍 测试决策流程完整性...")
            
            test_message = "你好"
            context = [{
                "session_id": "test_session",
                "user_id": "test_user", 
                "current_state": "IDLE"
            }]
            
            # 执行完整的决策流程
            result = await conversation_agent.intent_decision_engine.analyze(test_message, context)
            
            # 检查决策结果的完整性
            required_components = ["intent", "emotion", "confidence", "decision"]
            missing_components = []
            
            for component in required_components:
                if component not in result:
                    missing_components.append(component)
            
            if not missing_components:
                print("   ✅ 决策流程完整")
                self.test_results["functionality_tests"].append({
                    "test": "decision_flow_completeness",
                    "status": "PASS",
                    "details": "决策流程包含所有必需组件"
                })
            else:
                print(f"   ❌ 决策流程不完整，缺少: {missing_components}")
                self.test_results["functionality_tests"].append({
                    "test": "decision_flow_completeness",
                    "status": "FAIL",
                    "details": f"缺少组件: {missing_components}"
                })
            
            # 检查决策结果的合理性
            if result.get("decision") and "action" in result["decision"]:
                print("   ✅ 决策结果包含可执行动作")
                self.test_results["functionality_tests"].append({
                    "test": "decision_actionability",
                    "status": "PASS",
                    "details": "决策结果包含可执行动作"
                })
            else:
                print("   ❌ 决策结果缺少可执行动作")
                self.test_results["functionality_tests"].append({
                    "test": "decision_actionability",
                    "status": "FAIL",
                    "details": "决策结果缺少可执行动作"
                })
            
            print("✅ 决策流程检查完成")
            
        except Exception as e:
            print(f"❌ 决策流程检查失败: {e}")
    
    async def check_performance_impact(self):
        """检查性能影响"""
        print("\n⚡ 检查性能影响...")
        
        try:
            mock_llm = self._create_mock_llm()
            
            # 创建加速版（默认已使用AcceleratedIntentDecisionEngine）
            accelerated_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 性能测试消息
            test_messages = ["你好", "你的功能", "新聊天", "确认"] * 5  # 20个消息
            
            print("🔍 测试性能对比...")
            
            # 测试加速版性能
            start_time = time.time()
            accelerated_results = []
            
            for message in test_messages:
                context = [{"current_state": "IDLE", "session_id": "test"}]
                try:
                    result = await accelerated_agent.intent_decision_engine.analyze(message, context)
                    accelerated_results.append(result)
                except:
                    pass  # 忽略错误，专注于性能测试
            
            accelerated_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            print(f"   加速版处理时间: {accelerated_time:.2f}ms")
            print(f"   平均每消息: {accelerated_time/len(test_messages):.2f}ms")
            
            # 性能评估
            if accelerated_time < 100:  # 100ms内完成20个消息
                print("   ✅ 性能表现优秀")
                self.test_results["performance_tests"].append({
                    "test": "performance_benchmark",
                    "status": "PASS",
                    "details": f"处理{len(test_messages)}个消息用时{accelerated_time:.2f}ms"
                })
            else:
                print("   ⚠️ 性能需要优化")
                self.test_results["performance_tests"].append({
                    "test": "performance_benchmark",
                    "status": "WARNING",
                    "details": f"处理{len(test_messages)}个消息用时{accelerated_time:.2f}ms，可能需要优化"
                })
            
            print("✅ 性能影响检查完成")
            
        except Exception as e:
            print(f"❌ 性能影响检查失败: {e}")
    
    async def check_edge_cases(self):
        """检查边界情况"""
        print("\n🔬 检查边界情况...")
        
        try:
            mock_llm = self._create_mock_llm()
            conversation_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 边界情况测试
            edge_cases = [
                {"input": "", "description": "空输入"},
                {"input": "   ", "description": "空白输入"},
                {"input": "a" * 1000, "description": "超长输入"},
                {"input": "!@#$%^&*()", "description": "特殊字符"},
                {"input": None, "description": "None输入"},
            ]
            
            print("🔍 测试边界情况...")
            
            for case in edge_cases:
                try:
                    context = [{"current_state": "IDLE", "session_id": "test"}]
                    
                    if case["input"] is None:
                        # 跳过None输入测试，因为这会导致类型错误
                        print(f"   ⚠️ {case['description']}: 跳过（预期会失败）")
                        continue
                    
                    result = await conversation_agent.intent_decision_engine.analyze(
                        case["input"], context
                    )
                    
                    if result:
                        print(f"   ✅ {case['description']}: 正确处理")
                        self.test_results["edge_case_tests"].append({
                            "test": f"edge_case_{case['description']}",
                            "status": "PASS",
                            "details": "边界情况正确处理"
                        })
                    else:
                        print(f"   ⚠️ {case['description']}: 返回空结果")
                        self.test_results["edge_case_tests"].append({
                            "test": f"edge_case_{case['description']}",
                            "status": "WARNING",
                            "details": "返回空结果"
                        })
                        
                except Exception as e:
                    print(f"   ❌ {case['description']}: 异常 - {e}")
                    self.test_results["edge_case_tests"].append({
                        "test": f"edge_case_{case['description']}",
                        "status": "FAIL",
                        "details": str(e)
                    })
            
            print("✅ 边界情况检查完成")
            
        except Exception as e:
            print(f"❌ 边界情况检查失败: {e}")
    
    async def check_integration(self):
        """检查集成测试"""
        print("\n🔗 检查集成测试...")
        
        try:
            mock_llm = self._create_mock_llm()
            conversation_agent = AutoGenConversationFlowAgent(llm_client=mock_llm)
            
            # 模拟完整的对话流程
            print("🔍 模拟完整对话流程...")
            
            conversation_flow = [
                {"message": "你好", "expected_state": "IDLE", "description": "初始问候"},
                {"message": "你的功能", "expected_state": "IDLE", "description": "功能查询"},
                {"message": "我想开发一个网站", "expected_state": "GATHERING", "description": "需求表达"},
            ]
            
            current_state = "IDLE"
            
            for step in conversation_flow:
                try:
                    context = [{
                        "current_state": current_state,
                        "session_id": "integration_test",
                        "user_id": "test_user"
                    }]
                    
                    result = await conversation_agent.intent_decision_engine.analyze(
                        step["message"], context
                    )
                    
                    if result:
                        print(f"   ✅ {step['description']}: {step['message']} -> {result.get('intent', 'unknown')}")
                        self.test_results["integration_tests"].append({
                            "test": f"integration_{step['description']}",
                            "status": "PASS",
                            "details": f"对话步骤正确处理: {step['message']}"
                        })
                    else:
                        print(f"   ❌ {step['description']}: 处理失败")
                        self.test_results["integration_tests"].append({
                            "test": f"integration_{step['description']}",
                            "status": "FAIL",
                            "details": f"对话步骤处理失败: {step['message']}"
                        })
                        
                except Exception as e:
                    print(f"   ❌ {step['description']}: 异常 - {e}")
                    self.test_results["integration_tests"].append({
                        "test": f"integration_{step['description']}",
                        "status": "FAIL",
                        "details": str(e)
                    })
            
            print("✅ 集成测试检查完成")
            
        except Exception as e:
            print(f"❌ 集成测试检查失败: {e}")
    
    def _create_mock_llm(self):
        """创建模拟LLM服务"""
        mock_llm = Mock()
        mock_llm.config_manager = Mock()
        mock_llm.get_unified_config().get_model_config.return_value = {
            "temperature": 0.7,
            "max_tokens": 1000
        }
        return mock_llm
    
    def summarize_results(self):
        """总结检查结果"""
        print("\n" + "=" * 60)
        print("🎯 conversation_flow.py 核心模块检查总结")
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        warning_tests = 0
        
        for category, tests in self.test_results.items():
            if not tests:
                continue
                
            print(f"\n📊 {category.replace('_', ' ').title()}:")
            
            for test in tests:
                total_tests += 1
                status = test["status"]
                
                if status == "PASS":
                    passed_tests += 1
                    print(f"   ✅ {test['test']}: {test['details']}")
                elif status == "FAIL":
                    failed_tests += 1
                    print(f"   ❌ {test['test']}: {test['details']}")
                elif status == "WARNING":
                    warning_tests += 1
                    print(f"   ⚠️ {test['test']}: {test['details']}")
        
        print(f"\n📈 总体统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"   失败: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"   警告: {warning_tests} ({warning_tests/total_tests*100:.1f}%)")
        
        # 评估整体状态
        if failed_tests == 0:
            if warning_tests == 0:
                print(f"\n🎉 检查结果: 完美通过！")
                print(f"   conversation_flow.py 核心模块完全兼容，可以安全使用")
            else:
                print(f"\n✅ 检查结果: 基本通过")
                print(f"   conversation_flow.py 核心模块基本兼容，有少量需要关注的点")
        elif failed_tests <= total_tests * 0.2:  # 失败率 <= 20%
            print(f"\n⚠️ 检查结果: 需要注意")
            print(f"   conversation_flow.py 核心模块大部分兼容，但有一些问题需要解决")
        else:
            print(f"\n❌ 检查结果: 需要修复")
            print(f"   conversation_flow.py 核心模块存在较多兼容性问题，建议进一步调试")
        
        return failed_tests == 0


async def main():
    """主检查函数"""
    checker = ComprehensiveConversationFlowChecker()
    success = await checker.run_all_checks()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
