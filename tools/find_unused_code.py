#!/usr/bin/env python3
"""
项目代码分析工具 - 未使用代码检测器

功能：
1. 检测未被引用的函数和类
2. 识别未使用的文件
3. 分析import但未使用的模块
4. 生成清理建议
"""

import os
import ast
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict


class UnusedCodeDetector:
    """未使用代码检测器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backend_root = self.project_root / "backend"
        
        # 定义的符号
        self.defined_symbols = defaultdict(set)  # file -> {symbols}
        self.defined_classes = defaultdict(set)
        self.defined_functions = defaultdict(set)
        
        # 使用的符号
        self.used_symbols = defaultdict(set)
        self.imported_symbols = defaultdict(set)
        self.import_usage = defaultdict(set)
        
        # 文件信息
        self.all_files = set()
        self.imported_files = set()
        
    def analyze_unused_code(self) -> Dict:
        """分析未使用的代码"""
        print("🔍 开始检测未使用的代码...")
        
        # 收集所有Python文件
        python_files = list(self.backend_root.glob("**/*.py"))
        print(f"发现 {len(python_files)} 个Python文件")
        
        # 第一遍：收集所有定义
        for py_file in python_files:
            self._collect_definitions(py_file)
        
        # 第二遍：收集所有使用
        for py_file in python_files:
            self._collect_usage(py_file)
        
        # 生成报告
        report = self._generate_unused_report()
        
        print("✅ 未使用代码检测完成")
        return report
    
    def _collect_definitions(self, file_path: Path):
        """收集文件中定义的符号"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            relative_path = file_path.relative_to(self.backend_root)
            module_name = str(relative_path).replace('/', '.').replace('.py', '')
            
            self.all_files.add(module_name)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    self.defined_functions[module_name].add(node.name)
                    self.defined_symbols[module_name].add(node.name)
                
                elif isinstance(node, ast.ClassDef):
                    self.defined_classes[module_name].add(node.name)
                    self.defined_symbols[module_name].add(node.name)
                
                elif isinstance(node, ast.Assign):
                    # 收集模块级变量
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            # 只收集大写的常量（通常是配置或常量）
                            if target.id.isupper():
                                self.defined_symbols[module_name].add(target.id)
        
        except Exception as e:
            print(f"⚠️  收集定义失败: {file_path} - {e}")
    
    def _collect_usage(self, file_path: Path):
        """收集文件中使用的符号"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            relative_path = file_path.relative_to(self.backend_root)
            module_name = str(relative_path).replace('/', '.').replace('.py', '')
            
            for node in ast.walk(tree):
                # 收集import语句
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imported_module = self._normalize_import(alias.name)
                        if self._is_internal_module(imported_module):
                            self.imported_files.add(imported_module)
                            self.imported_symbols[module_name].add(alias.asname or alias.name)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imported_module = self._normalize_import(node.module)
                        if self._is_internal_module(imported_module):
                            self.imported_files.add(imported_module)
                            for alias in node.names:
                                self.imported_symbols[module_name].add(alias.asname or alias.name)
                
                # 收集符号使用
                elif isinstance(node, ast.Name):
                    if isinstance(node.ctx, ast.Load):
                        self.used_symbols[module_name].add(node.id)
                
                # 收集属性访问
                elif isinstance(node, ast.Attribute):
                    if isinstance(node.value, ast.Name):
                        self.used_symbols[module_name].add(node.value.id)
        
        except Exception as e:
            print(f"⚠️  收集使用失败: {file_path} - {e}")
    
    def _normalize_import(self, import_name: str) -> str:
        """标准化import名称"""
        if import_name.startswith('backend.'):
            return import_name[8:]
        return import_name
    
    def _is_internal_module(self, module_name: str) -> bool:
        """判断是否为内部模块"""
        external_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'typing', 'logging',
            'asyncio', 'pathlib', 'collections', 'dataclasses', 'threading',
            'sqlite3', 'yaml', 'fastapi', 'pydantic', 'uvicorn', 'requests',
            'autogen', 'numpy', 'pandas', 'torch', 'transformers'
        }
        
        root_module = module_name.split('.')[0]
        return root_module not in external_modules and not root_module.startswith('_')
    
    def _generate_unused_report(self) -> Dict:
        """生成未使用代码报告"""
        # 未使用的文件
        unused_files = self.all_files - self.imported_files
        # 排除特殊文件
        special_files = {'__init__', 'main', 'run_aip', 'config'}
        unused_files = {f for f in unused_files 
                       if not any(special in f for special in special_files)}
        
        # 未使用的函数和类
        unused_functions = defaultdict(set)
        unused_classes = defaultdict(set)
        unused_imports = defaultdict(set)
        
        for module in self.all_files:
            # 检查函数使用
            for func in self.defined_functions[module]:
                if not self._is_symbol_used(func, module):
                    unused_functions[module].add(func)
            
            # 检查类使用
            for cls in self.defined_classes[module]:
                if not self._is_symbol_used(cls, module):
                    unused_classes[module].add(cls)
            
            # 检查import使用
            for imported in self.imported_symbols[module]:
                if imported not in self.used_symbols[module]:
                    unused_imports[module].add(imported)
        
        # 计算清理收益
        cleanup_benefit = self._calculate_cleanup_benefit(
            unused_files, unused_functions, unused_classes
        )
        
        return {
            "unused_files": list(unused_files),
            "unused_functions": {k: list(v) for k, v in unused_functions.items()},
            "unused_classes": {k: list(v) for k, v in unused_classes.items()},
            "unused_imports": {k: list(v) for k, v in unused_imports.items()},
            "cleanup_benefit": cleanup_benefit,
            "statistics": {
                "total_files": len(self.all_files),
                "unused_files_count": len(unused_files),
                "total_functions": sum(len(funcs) for funcs in self.defined_functions.values()),
                "unused_functions_count": sum(len(funcs) for funcs in unused_functions.values()),
                "total_classes": sum(len(classes) for classes in self.defined_classes.values()),
                "unused_classes_count": sum(len(classes) for classes in unused_classes.values())
            }
        }
    
    def _is_symbol_used(self, symbol: str, defining_module: str) -> bool:
        """检查符号是否在其他模块中被使用"""
        # 检查是否在其他模块中被使用
        for module, used in self.used_symbols.items():
            if module != defining_module and symbol in used:
                return True
        
        # 检查是否被直接导入
        for module, imported in self.imported_symbols.items():
            if symbol in imported:
                return True
        
        # 特殊符号（通常不应该删除）
        special_symbols = {'main', '__init__', 'run', 'start', 'setup', 'config'}
        if symbol.lower() in special_symbols:
            return True
        
        # 私有符号在本模块内使用也算使用
        if symbol.startswith('_') and symbol in self.used_symbols[defining_module]:
            return True
        
        return False
    
    def _calculate_cleanup_benefit(self, unused_files, unused_functions, unused_classes) -> Dict:
        """计算清理收益"""
        # 估算可以删除的代码行数
        estimated_lines_saved = 0
        estimated_files_saved = len(unused_files)
        
        # 估算每个未使用函数/类的平均代码行数
        avg_function_lines = 20
        avg_class_lines = 50
        
        for module, functions in unused_functions.items():
            estimated_lines_saved += len(functions) * avg_function_lines
        
        for module, classes in unused_classes.items():
            estimated_lines_saved += len(classes) * avg_class_lines
        
        # 估算文件大小（假设每个文件平均200行）
        estimated_lines_saved += estimated_files_saved * 200
        
        return {
            "estimated_files_saved": estimated_files_saved,
            "estimated_lines_saved": estimated_lines_saved,
            "estimated_size_reduction_percent": min(30, estimated_lines_saved / 1000)  # 假设总代码量1000行
        }


def detect_unused_code(project_root: str) -> Dict:
    """检测未使用代码的主函数"""
    detector = UnusedCodeDetector(project_root)
    return detector.analyze_unused_code()


if __name__ == "__main__":
    # 分析当前项目
    project_root = "/Users/<USER>/由己ai项目/需求采集项目"
    
    print("🗑️  未使用代码检测器")
    print("=" * 50)
    
    # 执行检测
    report = detect_unused_code(project_root)
    
    # 输出报告
    stats = report['statistics']
    print(f"\n📊 检测摘要:")
    print(f"总文件数: {stats['total_files']}")
    print(f"未使用文件: {stats['unused_files_count']}")
    print(f"总函数数: {stats['total_functions']}")
    print(f"未使用函数: {stats['unused_functions_count']}")
    print(f"总类数: {stats['total_classes']}")
    print(f"未使用类: {stats['unused_classes_count']}")
    
    print(f"\n📁 未使用的文件:")
    for file in report['unused_files'][:10]:
        print(f"  {file}")
    
    print(f"\n🔧 未使用的函数 (前10个模块):")
    for module, functions in list(report['unused_functions'].items())[:10]:
        if functions:
            print(f"  {module}: {', '.join(list(functions)[:5])}")
    
    print(f"\n🏗️  未使用的类 (前10个模块):")
    for module, classes in list(report['unused_classes'].items())[:10]:
        if classes:
            print(f"  {module}: {', '.join(list(classes)[:5])}")
    
    benefit = report['cleanup_benefit']
    print(f"\n💰 清理收益估算:")
    print(f"可删除文件: {benefit['estimated_files_saved']}")
    print(f"可减少代码行: {benefit['estimated_lines_saved']}")
    print(f"预计代码量减少: {benefit['estimated_size_reduction_percent']:.1f}%")
    
    # 保存详细报告
    output_file = f"{project_root}/docs/unused_code_report.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 详细报告已保存到: {output_file}")
    print("=" * 50)