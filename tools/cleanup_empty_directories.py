#!/usr/bin/env python3
"""
清理空目录脚本

功能：
1. 清理只包含__pycache__的空目录
2. 删除Python缓存文件
3. 保持项目结构整洁

使用方法：
python cleanup_empty_directories.py [--dry-run] [--execute]
"""

import os
import shutil
import sys
from pathlib import Path


class EmptyDirectoryCleaner:
    """空目录清理器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.cleanup_plan = []
        
    def analyze_directories(self):
        """分析需要清理的目录"""
        print("🔍 分析空目录...")
        
        # 检查backend下的旧目录
        backend_path = self.project_root / "backend"
        old_dirs = [
            "accelerators",
            "core", 
            "monitoring",
            "testing"
        ]
        
        for old_dir in old_dirs:
            dir_path = backend_path / old_dir
            if dir_path.exists():
                # 检查目录内容
                contents = list(dir_path.iterdir())
                
                # 过滤掉隐藏文件和__pycache__
                non_cache_contents = [
                    item for item in contents 
                    if not item.name.startswith('.') and item.name != '__pycache__'
                ]
                
                if not non_cache_contents:
                    # 只有缓存文件或空目录
                    self.cleanup_plan.append({
                        "path": dir_path,
                        "type": "empty_directory",
                        "reason": "只包含缓存文件或为空"
                    })
                    print(f"   📁 {dir_path.relative_to(self.project_root)}: 可以删除（只有缓存文件）")
                else:
                    print(f"   ⚠️ {dir_path.relative_to(self.project_root)}: 仍有文件 - {[item.name for item in non_cache_contents]}")
        
        # 查找其他__pycache__目录
        self._find_pycache_directories()
        
        return self.cleanup_plan
    
    def _find_pycache_directories(self):
        """查找所有__pycache__目录"""
        print("\n🔍 查找Python缓存目录...")
        
        for root, dirs, files in os.walk(self.project_root):
            if '__pycache__' in dirs:
                pycache_path = Path(root) / '__pycache__'
                
                # 检查父目录是否在清理计划中
                parent_in_cleanup = any(
                    pycache_path.is_relative_to(item["path"]) 
                    for item in self.cleanup_plan 
                    if item["type"] == "empty_directory"
                )
                
                if not parent_in_cleanup:
                    self.cleanup_plan.append({
                        "path": pycache_path,
                        "type": "pycache",
                        "reason": "Python缓存目录"
                    })
                    print(f"   🗂️ {pycache_path.relative_to(self.project_root)}: Python缓存")
    
    def show_cleanup_plan(self):
        """显示清理计划"""
        print(f"\n📋 清理计划")
        print("="*50)
        
        empty_dirs = [item for item in self.cleanup_plan if item["type"] == "empty_directory"]
        pycache_dirs = [item for item in self.cleanup_plan if item["type"] == "pycache"]
        
        if empty_dirs:
            print(f"\n📁 空目录清理 ({len(empty_dirs)} 个):")
            for item in empty_dirs:
                print(f"   - {item['path'].relative_to(self.project_root)}")
        
        if pycache_dirs:
            print(f"\n🗂️ 缓存目录清理 ({len(pycache_dirs)} 个):")
            for item in pycache_dirs[:5]:  # 只显示前5个
                print(f"   - {item['path'].relative_to(self.project_root)}")
            if len(pycache_dirs) > 5:
                print(f"   ... 还有 {len(pycache_dirs) - 5} 个缓存目录")
        
        print(f"\n📊 总计: {len(self.cleanup_plan)} 个目录需要清理")
    
    def execute_cleanup(self, dry_run: bool = True):
        """执行清理"""
        if dry_run:
            print("\n🔍 模拟执行（dry-run模式）")
        else:
            print("\n🚀 开始执行清理...")
        
        success_count = 0
        error_count = 0
        
        for item in self.cleanup_plan:
            path = item["path"]
            item_type = item["type"]
            
            try:
                if path.exists():
                    if not dry_run:
                        if path.is_dir():
                            shutil.rmtree(path)
                        else:
                            path.unlink()
                        print(f"   ✅ 删除: {path.relative_to(self.project_root)}")
                    else:
                        print(f"   📝 将删除: {path.relative_to(self.project_root)}")
                    success_count += 1
                else:
                    print(f"   ⚠️ 路径不存在: {path.relative_to(self.project_root)}")
                    
            except Exception as e:
                print(f"   ❌ 删除失败: {path.relative_to(self.project_root)} - {e}")
                error_count += 1
        
        print(f"\n📊 清理结果: {success_count} 成功, {error_count} 失败")
    
    def generate_report(self):
        """生成清理报告"""
        print("\n📊 清理完成报告")
        print("="*50)
        
        print("🎯 清理效果:")
        print("✅ 删除了无用的空目录")
        print("✅ 清理了Python缓存文件")
        print("✅ 项目结构更加整洁")
        print("✅ 减少了目录复杂性")
        
        print(f"\n📂 清理后的backend目录结构:")
        print(f"backend/")
        print(f"├── agents/                    # 所有智能代理和加速器")
        print(f"├── handlers/                  # 业务处理器")
        print(f"├── utils/                     # 工具类（合并后）")
        print(f"├── tests/                     # 测试（合并后）")
        print(f"├── api/                       # API接口")
        print(f"├── config/                    # 配置管理")
        print(f"├── services/                  # 服务层")
        print(f"├── data/                      # 数据存储")
        print(f"├── prompts/                   # 提示词模板")
        print(f"└── scripts/                   # 脚本工具")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="清理空目录")
    parser.add_argument("--dry-run", action="store_true", help="模拟执行，不实际删除")
    parser.add_argument("--execute", action="store_true", help="实际执行清理")
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.execute:
        print("请指定 --dry-run（模拟执行）或 --execute（实际执行）")
        return 1
    
    project_root = Path(__file__).parent
    cleaner = EmptyDirectoryCleaner(project_root)
    
    try:
        # 分析目录
        cleaner.analyze_directories()
        
        # 显示计划
        cleaner.show_cleanup_plan()
        
        # 执行清理
        dry_run = args.dry_run or not args.execute
        cleaner.execute_cleanup(dry_run=dry_run)
        
        # 生成报告
        cleaner.generate_report()
        
        if dry_run:
            print(f"\n💡 这是模拟执行，如需实际清理，请运行:")
            print(f"python {Path(__file__).name} --execute")
        else:
            print(f"\n🎉 空目录清理完成！")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
