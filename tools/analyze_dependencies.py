#!/usr/bin/env python3
"""
项目代码分析工具 - 组件依赖关系分析器

功能：
1. 分析Python文件的import依赖关系
2. 生成组件依赖图
3. 识别循环依赖
4. 找出孤立组件
"""

import os
import ast
import json
import networkx as nx
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict


class DependencyAnalyzer:
    """依赖关系分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backend_root = self.project_root / "backend"
        self.dependencies = defaultdict(set)
        self.reverse_dependencies = defaultdict(set)
        self.file_info = {}
        
    def analyze_project(self) -> Dict:
        """分析整个项目的依赖关系"""
        print("🔍 开始分析项目依赖关系...")
        
        # 收集所有Python文件
        python_files = list(self.backend_root.glob("**/*.py"))
        print(f"发现 {len(python_files)} 个Python文件")
        
        # 分析每个文件的依赖
        for py_file in python_files:
            self._analyze_file(py_file)
        
        # 生成分析报告
        report = self._generate_report()
        
        print("✅ 依赖关系分析完成")
        return report
    
    def _analyze_file(self, file_path: Path):
        """分析单个文件的依赖关系"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 获取相对路径作为模块名
            relative_path = file_path.relative_to(self.backend_root)
            module_name = str(relative_path).replace('/', '.').replace('.py', '')
            
            # 收集文件信息
            self.file_info[module_name] = {
                "path": str(file_path),
                "lines": len(content.splitlines()),
                "size": file_path.stat().st_size,
                "imports": []
            }
            
            # 分析import语句
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dep = self._normalize_import(alias.name)
                        if self._is_internal_module(dep):
                            self.dependencies[module_name].add(dep)
                            self.reverse_dependencies[dep].add(module_name)
                            self.file_info[module_name]["imports"].append(dep)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        dep = self._normalize_import(node.module)
                        if self._is_internal_module(dep):
                            self.dependencies[module_name].add(dep)
                            self.reverse_dependencies[dep].add(module_name)
                            self.file_info[module_name]["imports"].append(dep)
        
        except Exception as e:
            print(f"⚠️  分析文件失败: {file_path} - {e}")
    
    def _normalize_import(self, import_name: str) -> str:
        """标准化import名称"""
        # 移除backend前缀
        if import_name.startswith('backend.'):
            return import_name[8:]
        return import_name
    
    def _is_internal_module(self, module_name: str) -> bool:
        """判断是否为内部模块"""
        # 排除标准库和第三方库
        external_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'typing', 'logging',
            'asyncio', 'pathlib', 'collections', 'dataclasses', 'threading',
            'sqlite3', 'yaml', 'fastapi', 'pydantic', 'uvicorn', 'requests',
            'autogen', 'numpy', 'pandas', 'torch', 'transformers'
        }
        
        root_module = module_name.split('.')[0]
        return root_module not in external_modules and not root_module.startswith('_')
    
    def _generate_report(self) -> Dict:
        """生成分析报告"""
        # 基础统计
        total_modules = len(self.file_info)
        total_dependencies = sum(len(deps) for deps in self.dependencies.values())
        
        # 分析复杂模块
        complex_modules = [
            (name, info) for name, info in self.file_info.items()
            if info["lines"] > 500
        ]
        
        # 分析高依赖模块
        high_dependency_modules = [
            (name, len(deps)) for name, deps in self.dependencies.items()
            if len(deps) > 10
        ]
        
        # 分析被广泛依赖的模块
        widely_used_modules = [
            (name, len(deps)) for name, deps in self.reverse_dependencies.items()
            if len(deps) > 5
        ]
        
        # 检测循环依赖
        cycles = self._detect_cycles()
        
        # 识别孤立模块
        isolated_modules = [
            name for name, info in self.file_info.items()
            if len(self.dependencies[name]) == 0 and len(self.reverse_dependencies[name]) == 0
        ]
        
        return {
            "summary": {
                "total_modules": total_modules,
                "total_dependencies": total_dependencies,
                "avg_dependencies_per_module": total_dependencies / total_modules if total_modules > 0 else 0
            },
            "complex_modules": sorted(complex_modules, key=lambda x: x[1]["lines"], reverse=True),
            "high_dependency_modules": sorted(high_dependency_modules, key=lambda x: x[1], reverse=True),
            "widely_used_modules": sorted(widely_used_modules, key=lambda x: x[1], reverse=True),
            "circular_dependencies": cycles,
            "isolated_modules": isolated_modules,
            "dependencies": {k: list(v) for k, v in self.dependencies.items()},
            "reverse_dependencies": {k: list(v) for k, v in self.reverse_dependencies.items()},
            "file_info": self.file_info
        }
    
    def _detect_cycles(self) -> List[List[str]]:
        """检测循环依赖"""
        # 构建有向图
        G = nx.DiGraph()
        for module, deps in self.dependencies.items():
            for dep in deps:
                G.add_edge(module, dep)
        
        # 查找所有强连通分量
        try:
            cycles = []
            for cycle in nx.simple_cycles(G):
                if len(cycle) > 1:  # 忽略自循环
                    cycles.append(cycle)
            return cycles
        except:
            return []


def analyze_project_dependencies(project_root: str) -> Dict:
    """分析项目依赖关系的主函数"""
    analyzer = DependencyAnalyzer(project_root)
    return analyzer.analyze_project()


if __name__ == "__main__":
    # 分析当前项目
    project_root = "/Users/<USER>/由己ai项目/需求采集项目"
    
    print("🔍 项目依赖关系分析器")
    print("=" * 50)
    
    # 执行分析
    report = analyze_project_dependencies(project_root)
    
    # 输出报告
    print(f"\n📊 分析摘要:")
    print(f"总模块数: {report['summary']['total_modules']}")
    print(f"总依赖关系: {report['summary']['total_dependencies']}")
    print(f"平均每模块依赖数: {report['summary']['avg_dependencies_per_module']:.2f}")
    
    print(f"\n📈 复杂模块 (>500行):")
    for name, info in report['complex_modules'][:10]:
        print(f"  {name}: {info['lines']} 行")
    
    print(f"\n🔗 高依赖模块 (>10个依赖):")
    for name, count in report['high_dependency_modules'][:10]:
        print(f"  {name}: {count} 个依赖")
    
    print(f"\n⭐ 广泛使用模块 (被>5个模块依赖):")
    for name, count in report['widely_used_modules'][:10]:
        print(f"  {name}: 被 {count} 个模块依赖")
    
    if report['circular_dependencies']:
        print(f"\n🔄 循环依赖:")
        for i, cycle in enumerate(report['circular_dependencies'][:5]):
            print(f"  循环 {i+1}: {' -> '.join(cycle + [cycle[0]])}")
    
    if report['isolated_modules']:
        print(f"\n🏝️  孤立模块:")
        for module in report['isolated_modules'][:10]:
            print(f"  {module}")
    
    # 保存详细报告
    output_file = f"{project_root}/docs/dependency_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 详细报告已保存到: {output_file}")
    print("=" * 50)