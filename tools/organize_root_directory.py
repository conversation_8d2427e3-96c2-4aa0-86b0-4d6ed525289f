#!/usr/bin/env python3
"""
根目录文件整理脚本

功能：
1. 分析根目录下的文件
2. 按类型整理到合适的目录
3. 保持项目结构清晰
4. 生成整理报告

使用方法：
python organize_root_directory.py [--dry-run] [--execute]
"""

import os
import shutil
import sys
from pathlib import Path
from typing import Dict, List


class RootDirectoryOrganizer:
    """根目录整理器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.organization_plan = {
            "monitoring": [],      # 监控相关文件
            "reports": [],         # 分析报告文件
            "temp": [],           # 临时和测试文件
            "docs": [],           # 文档文件
            "tools": [],          # 工具脚本
            "keep_root": [],      # 保留在根目录
            "backend/data": []    # 配置存储
        }
        
    def analyze_files(self):
        """分析根目录文件"""
        print("🔍 分析根目录文件...")
        
        # 获取根目录下的所有文件
        root_files = [f for f in self.project_root.iterdir() 
                     if f.is_file() and not f.name.startswith('.')]
        
        for file_path in root_files:
            filename = file_path.name
            
            # 分类规则
            if self._is_monitoring_file(filename):
                self.organization_plan["monitoring"].append(filename)
            elif self._is_report_file(filename):
                self.organization_plan["reports"].append(filename)
            elif self._is_temp_test_file(filename):
                self.organization_plan["temp"].append(filename)
            elif self._is_doc_file(filename):
                self.organization_plan["docs"].append(filename)
            elif self._is_tool_file(filename):
                self.organization_plan["tools"].append(filename)
            elif self._should_keep_in_root(filename):
                self.organization_plan["keep_root"].append(filename)
            else:
                # 默认放到temp目录
                self.organization_plan["temp"].append(filename)
        
        # 处理config_storage目录
        config_storage = self.project_root / "config_storage"
        if config_storage.exists():
            self.organization_plan["backend/data"].append("config_storage")
        
        return self.organization_plan
    
    def _is_monitoring_file(self, filename: str) -> bool:
        """判断是否为监控相关文件"""
        monitoring_patterns = [
            "monitoring_",
            "start_monitoring",
            "simple_monitoring_dashboard"
        ]
        return any(pattern in filename for pattern in monitoring_patterns)
    
    def _is_report_file(self, filename: str) -> bool:
        """判断是否为报告文件"""
        report_patterns = [
            "_report.md",
            "_analysis_report.md",
            "business_logic_",
            "business_rules_",
            "integration_test_",
            "accelerator_integration_guide",
            "project_final_report"
        ]
        return any(pattern in filename for pattern in report_patterns)
    
    def _is_temp_test_file(self, filename: str) -> bool:
        """判断是否为临时/测试文件"""
        temp_patterns = [
            "test_",
            "debug_",
            "check_",
            "verify_",
            "performance_test"
        ]
        return any(pattern in filename for pattern in temp_patterns)
    
    def _is_doc_file(self, filename: str) -> bool:
        """判断是否为文档文件"""
        # 只有特定的文档文件移动到docs，README.md保留在根目录
        doc_patterns = [
            "guide.md",
            "documentation.md"
        ]
        return any(pattern in filename for pattern in doc_patterns) and filename != "README.md"
    
    def _is_tool_file(self, filename: str) -> bool:
        """判断是否为工具文件"""
        tool_patterns = [
            "analyze_",
            "comprehensive_",
            "organize_"
        ]
        return any(pattern in filename for pattern in tool_patterns)
    
    def _should_keep_in_root(self, filename: str) -> bool:
        """判断是否应该保留在根目录"""
        keep_patterns = [
            "README.md",
            "requirements.txt",
            "package.json",
            "package-lock.json",
            "setup.py",
            "setup.cfg",
            "run_",
            ".gitignore",
            ".env"
        ]
        return any(filename.startswith(pattern) or filename == pattern for pattern in keep_patterns)
    
    def show_organization_plan(self):
        """显示整理计划"""
        print("\n📋 文件整理计划")
        print("="*50)
        
        for target_dir, files in self.organization_plan.items():
            if files:
                print(f"\n📁 {target_dir}/ ({len(files)} 个文件)")
                for file in files[:5]:  # 只显示前5个
                    print(f"   - {file}")
                if len(files) > 5:
                    print(f"   ... 还有 {len(files) - 5} 个文件")
        
        total_files = sum(len(files) for files in self.organization_plan.values())
        print(f"\n📊 总计: {total_files} 个文件/目录需要整理")
    
    def execute_organization(self, dry_run: bool = True):
        """执行整理"""
        if dry_run:
            print("\n🔍 模拟执行（dry-run模式）")
        else:
            print("\n🚀 开始执行整理...")
        
        for target_dir, files in self.organization_plan.items():
            if not files:
                continue
            
            # 创建目标目录
            if target_dir == "keep_root":
                continue  # 保留在根目录，不需要移动
            
            target_path = self.project_root / target_dir
            
            if not dry_run:
                target_path.mkdir(parents=True, exist_ok=True)
                print(f"📁 创建目录: {target_dir}/")
            else:
                print(f"📁 将创建目录: {target_dir}/")
            
            # 移动文件
            for filename in files:
                source_path = self.project_root / filename
                dest_path = target_path / filename
                
                if source_path.exists():
                    if not dry_run:
                        if source_path.is_dir():
                            shutil.move(str(source_path), str(dest_path))
                        else:
                            shutil.move(str(source_path), str(dest_path))
                        print(f"   ✅ 移动: {filename} → {target_dir}/")
                    else:
                        print(f"   📝 将移动: {filename} → {target_dir}/")
                else:
                    print(f"   ⚠️ 文件不存在: {filename}")
    
    def generate_report(self):
        """生成整理报告"""
        print("\n📊 整理完成报告")
        print("="*50)
        
        print("📂 整理后的根目录结构:")
        print("项目根目录/")
        print("├── README.md                    # 项目主文档")
        print("├── requirements.txt             # Python依赖")
        print("├── package.json                 # Node.js依赖")
        print("├── setup.py                     # Python包配置")
        print("├── run_*.py                     # 启动脚本")
        print("├── backend/                     # 后端代码")
        print("├── frontend/                    # 前端代码")
        print("├── docs/                        # 文档")
        print("├── tests/                       # 测试")
        print("├── tools/                       # 工具脚本")
        print("├── scripts/                     # 脚本")
        print("├── examples/                    # 示例")
        print("├── logs/                        # 日志")
        print("├── monitoring/                  # 监控相关文件")
        print("├── reports/                     # 分析报告")
        print("└── temp/                        # 临时和测试文件")
        
        print("\n🎯 整理效果:")
        print("✅ 根目录更加清晰")
        print("✅ 相关文件集中管理")
        print("✅ 保留了重要的配置文件")
        print("✅ 便于项目维护和导航")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="根目录文件整理")
    parser.add_argument("--dry-run", action="store_true", help="模拟执行，不实际移动文件")
    parser.add_argument("--execute", action="store_true", help="实际执行整理")
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.execute:
        print("请指定 --dry-run（模拟执行）或 --execute（实际执行）")
        return 1
    
    project_root = Path(__file__).parent
    organizer = RootDirectoryOrganizer(project_root)
    
    try:
        # 分析文件
        organizer.analyze_files()
        
        # 显示计划
        organizer.show_organization_plan()
        
        # 执行整理
        dry_run = args.dry_run or not args.execute
        organizer.execute_organization(dry_run=dry_run)
        
        # 生成报告
        organizer.generate_report()
        
        if dry_run:
            print(f"\n💡 这是模拟执行，如需实际整理，请运行:")
            print(f"python {Path(__file__).name} --execute")
        else:
            print(f"\n🎉 根目录整理完成！")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 整理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
