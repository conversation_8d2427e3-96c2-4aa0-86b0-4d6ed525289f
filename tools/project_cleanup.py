#!/usr/bin/env python3
"""
项目健康清理工具

基于健康检查结果，安全地清理项目中的未使用代码和文件。
分阶段进行，确保不破坏项目功能。

使用方法：
python tools/project_cleanup.py --phase 1 --dry-run  # 预览第一阶段清理
python tools/project_cleanup.py --phase 1            # 执行第一阶段清理
"""

import os
import sys
import json
import shutil
import argparse
from pathlib import Path
from typing import List, Dict, Set
from datetime import datetime

class ProjectCleaner:
    """项目清理器"""
    
    def __init__(self, project_root: str, dry_run: bool = True):
        self.project_root = Path(project_root)
        self.dry_run = dry_run
        self.backup_dir = self.project_root / "cleanup_backup" / datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 加载未使用代码报告
        self.unused_report = self._load_unused_report()
        
        # 定义清理阶段
        self.cleanup_phases = {
            1: "安全清理：测试文件和明显未使用的工具",
            2: "中等风险：未使用的handler和service",
            3: "高风险：核心组件清理"
        }
        
    def _load_unused_report(self) -> Dict:
        """加载未使用代码报告"""
        report_path = self.project_root / "docs" / "unused_code_report.json"
        if report_path.exists():
            with open(report_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"unused_files": [], "unused_functions": {}, "unused_classes": {}}
    
    def _create_backup(self, file_path: Path):
        """创建文件备份"""
        if not self.dry_run:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            relative_path = file_path.relative_to(self.project_root)
            backup_path = self.backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(file_path, backup_path)
            print(f"  📦 备份: {relative_path}")
    
    def _safe_remove_file(self, file_path: Path, reason: str):
        """安全删除文件"""
        if file_path.exists():
            self._create_backup(file_path)
            if not self.dry_run:
                file_path.unlink()
                print(f"  🗑️  删除: {file_path.relative_to(self.project_root)} ({reason})")
            else:
                print(f"  🔍 [预览] 将删除: {file_path.relative_to(self.project_root)} ({reason})")
    
    def _get_phase_1_files(self) -> List[str]:
        """获取第一阶段要清理的文件（低风险）"""
        phase_1_patterns = [
            # 测试文件
            "tests.agents.test_hybrid_intent_recognition_engine",
            "tests.functional.test_core_modules", 
            "tests.business_logic_regression_tests",
            "tests.agents.test_hybrid_conversation_router_fix",
            "tests.agents.test_rag_knowledge_base_agent",
            "tests.test_data_generator",
            "tests.integration.test_hybrid_agent",
            "tests.test_keyword_accelerator",
            "tests.test_component_pooling_integration",
            "tests.autogen_agents.test_llm_service",
            "tests.run_business_logic_tests",
            "tests.performance.test_hybrid_intent_performance",
            
            # 工具和脚本
            "scripts.init_focus_point_tables",
            "utils.check_log_duplicates",
            "utils.log_components.filters",
            "utils.log_components.handlers", 
            "utils.log_components.formatters",
            "utils.sensitive_data_masker",
            "utils.agent_creation_benchmark",
            "utils.startup_optimizer",
            "utils.minimal_benchmark",
            "utils.simple_benchmark",
            
            # 监控相关
            "monitoring.performance_monitor",
        ]
        
        return [f for f in self.unused_report["unused_files"] if any(pattern in f for pattern in phase_1_patterns)]
    
    def _get_phase_2_files(self) -> List[str]:
        """获取第二阶段要清理的文件（中等风险）"""
        phase_2_patterns = [
            # 注意：handlers下的文件大多被action_executor动态加载，不应删除
            # 只清理确认未使用的API相关文件
            "api.admin.dependencies",
            "api.admin.auth",

            # Service文件（需要确认）
            "services.resource_manager",

            # 其他确认未使用的模块
            "agents.conversation_flow_reply_mixin",
            "agents.conversation_flow_message_mixin",
        ]

        return [f for f in self.unused_report["unused_files"] if any(pattern in f for pattern in phase_2_patterns)]
    
    def _get_phase_3_files(self) -> List[str]:
        """获取第三阶段要清理的文件（高风险）"""
        phase_3_patterns = [
            # 核心组件（需要仔细检查）
            "agents.conversation_flow_reply_mixin",
            "agents.conversation_flow_message_mixin", 
            "agents.conversation_state_machine",
            "agents.conversation_flow.core",
            "agents.conversation_flow.utils",
            "agents.unified_llm_client_factory",
            "agents.decision_engine",
            "agents.keyword_accelerator",
            "data.db.conversation_manager",
        ]
        
        return [f for f in self.unused_report["unused_files"] if any(pattern in f for pattern in phase_3_patterns)]
    
    def _module_path_to_file_path(self, module_path: str) -> Path:
        """将模块路径转换为文件路径"""
        parts = module_path.split('.')
        file_path = self.project_root / "backend" / "/".join(parts[:-1]) / f"{parts[-1]}.py"
        return file_path
    
    def cleanup_phase_1(self):
        """执行第一阶段清理：安全清理测试文件和工具"""
        print("🧹 第一阶段清理：安全清理测试文件和工具")
        print("=" * 60)
        
        files_to_clean = self._get_phase_1_files()
        
        if not files_to_clean:
            print("  ✅ 没有发现需要清理的第一阶段文件")
            return
        
        print(f"  📋 发现 {len(files_to_clean)} 个文件需要清理")
        
        for module_path in files_to_clean:
            file_path = self._module_path_to_file_path(module_path)
            if file_path.exists():
                self._safe_remove_file(file_path, "未使用的测试/工具文件")
        
        print(f"  ✅ 第一阶段清理完成")
    
    def cleanup_phase_2(self):
        """执行第二阶段清理：中等风险文件"""
        print("🧹 第二阶段清理：中等风险文件")
        print("=" * 60)
        
        files_to_clean = self._get_phase_2_files()
        
        if not files_to_clean:
            print("  ✅ 没有发现需要清理的第二阶段文件")
            return
        
        print(f"  📋 发现 {len(files_to_clean)} 个文件需要清理")
        print("  ⚠️  注意：这些文件可能被动态导入，请仔细检查")
        
        for module_path in files_to_clean:
            file_path = self._module_path_to_file_path(module_path)
            if file_path.exists():
                self._safe_remove_file(file_path, "未使用的handler/service文件")
        
        print(f"  ✅ 第二阶段清理完成")
    
    def cleanup_phase_3(self):
        """执行第三阶段清理：高风险核心组件"""
        print("🧹 第三阶段清理：高风险核心组件")
        print("=" * 60)
        print("  ⚠️  警告：这些是核心组件，清理前请确保充分测试")
        
        files_to_clean = self._get_phase_3_files()
        
        if not files_to_clean:
            print("  ✅ 没有发现需要清理的第三阶段文件")
            return
        
        print(f"  📋 发现 {len(files_to_clean)} 个文件需要清理")
        
        for module_path in files_to_clean:
            file_path = self._module_path_to_file_path(module_path)
            if file_path.exists():
                self._safe_remove_file(file_path, "未使用的核心组件")
        
        print(f"  ✅ 第三阶段清理完成")
    
    def cleanup_empty_directories(self):
        """清理空目录"""
        print("🧹 清理空目录")
        print("=" * 60)
        
        empty_dirs = []
        for root, dirs, files in os.walk(self.project_root / "backend"):
            # 跳过特殊目录
            if any(skip in root for skip in ["__pycache__", ".git", "node_modules"]):
                continue
                
            # 检查是否为空目录（只包含__pycache__或__init__.py）
            real_files = [f for f in files if not f.startswith('.') and f != '__init__.py']
            real_dirs = [d for d in dirs if d != '__pycache__']
            
            if not real_files and not real_dirs:
                empty_dirs.append(Path(root))
        
        for empty_dir in empty_dirs:
            if empty_dir.exists():
                relative_path = empty_dir.relative_to(self.project_root)
                if not self.dry_run:
                    shutil.rmtree(empty_dir)
                    print(f"  🗑️  删除空目录: {relative_path}")
                else:
                    print(f"  🔍 [预览] 将删除空目录: {relative_path}")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report = {
            "cleanup_time": datetime.now().isoformat(),
            "dry_run": self.dry_run,
            "backup_location": str(self.backup_dir) if not self.dry_run else None,
            "phases": self.cleanup_phases,
            "phase_1_files": self._get_phase_1_files(),
            "phase_2_files": self._get_phase_2_files(), 
            "phase_3_files": self._get_phase_3_files(),
            "total_files_to_clean": len(self._get_phase_1_files()) + len(self._get_phase_2_files()) + len(self._get_phase_3_files())
        }
        
        report_path = self.project_root / "cleanup_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 清理报告已保存到: {report_path}")
        return report


def main():
    parser = argparse.ArgumentParser(description="项目健康清理工具")
    parser.add_argument("--phase", type=int, choices=[1, 2, 3], help="清理阶段 (1=安全, 2=中等风险, 3=高风险)")
    parser.add_argument("--all-phases", action="store_true", help="执行所有阶段的清理")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际删除文件")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    
    args = parser.parse_args()
    
    cleaner = ProjectCleaner(args.project_root, args.dry_run)
    
    print("🧹 项目健康清理工具")
    print("=" * 60)
    print(f"项目根目录: {cleaner.project_root}")
    print(f"运行模式: {'预览模式' if args.dry_run else '执行模式'}")
    print()
    
    if args.all_phases:
        cleaner.cleanup_phase_1()
        print()
        cleaner.cleanup_phase_2()
        print()
        cleaner.cleanup_phase_3()
        print()
        cleaner.cleanup_empty_directories()
    elif args.phase:
        if args.phase == 1:
            cleaner.cleanup_phase_1()
        elif args.phase == 2:
            cleaner.cleanup_phase_2()
        elif args.phase == 3:
            cleaner.cleanup_phase_3()
        print()
        cleaner.cleanup_empty_directories()
    else:
        # 默认只显示报告
        report = cleaner.generate_cleanup_report()
        print(f"📊 清理统计:")
        print(f"  第一阶段文件: {len(report['phase_1_files'])}")
        print(f"  第二阶段文件: {len(report['phase_2_files'])}")
        print(f"  第三阶段文件: {len(report['phase_3_files'])}")
        print(f"  总计: {report['total_files_to_clean']}")
        print()
        print("使用 --phase 1 --dry-run 预览第一阶段清理")
        print("使用 --phase 1 执行第一阶段清理")


if __name__ == "__main__":
    main()
