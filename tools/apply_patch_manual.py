#!/usr/bin/env python3
"""
手动应用优化补丁
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def manual_patch():
    """手动应用补丁"""
    print("🔧 手动应用优化补丁...")
    
    try:
        # 直接修改主要模块的导入
        import backend.config.config_manager as config_module
        import backend.agents.factory as factory_module
        
        # 使用统一配置系统
        from backend.config import config_service
        from backend.agents.factory import AgentFactory

        # 替换实例
        config_module.config_manager = config_service
        factory_module.agent_factory = AgentFactory()
        
        print("✅ 手动补丁应用成功")
        print("📝 现在请重启服务: python run_api.py")
        
    except Exception as e:
        print(f"❌ 手动补丁应用失败: {e}")

if __name__ == "__main__":
    manual_patch()