#!/usr/bin/env python3
"""
策略冲突分析工具

分析当前决策引擎中的策略冲突问题，包括：
1. 优先级冲突检测
2. 意图重叠分析
3. 状态转换冲突
4. 决策规则一致性检查
"""

import sys
import os
import json
from typing import Dict, List, Any, Set, Tuple
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.agents.unified_state_manager import ConversationState


class StrategyConflictAnalyzer:
    """策略冲突分析器"""
    
    def __init__(self):
        self.engine = SimplifiedDecisionEngine()
        self.conflicts = []
        self.warnings = []
        self.recommendations = []
    
    def analyze_all_conflicts(self) -> Dict[str, Any]:
        """分析所有类型的冲突"""
        print("🔍 开始策略冲突分析...")
        
        # 1. 分析优先级冲突
        priority_conflicts = self._analyze_priority_conflicts()
        
        # 2. 分析意图重叠
        intent_overlaps = self._analyze_intent_overlaps()
        
        # 3. 分析状态转换一致性
        state_consistency = self._analyze_state_consistency()
        
        # 4. 分析决策规则完整性
        rule_completeness = self._analyze_rule_completeness()
        
        # 5. 生成报告
        report = {
            "summary": {
                "total_conflicts": len(self.conflicts),
                "total_warnings": len(self.warnings),
                "total_recommendations": len(self.recommendations)
            },
            "priority_conflicts": priority_conflicts,
            "intent_overlaps": intent_overlaps,
            "state_consistency": state_consistency,
            "rule_completeness": rule_completeness,
            "conflicts": self.conflicts,
            "warnings": self.warnings,
            "recommendations": self.recommendations
        }
        
        return report
    
    def _analyze_priority_conflicts(self) -> Dict[str, Any]:
        """分析优先级冲突"""
        print("  📊 分析优先级冲突...")
        
        conflicts = []
        decision_rules = self.engine._decision_rules
        
        for state, rules in decision_rules.items():
            # 按优先级分组
            priority_groups = defaultdict(list)
            for intent, rule in rules.items():
                if intent != "default":  # 排除默认规则
                    priority = rule.get("priority", 0)
                    priority_groups[priority].append((intent, rule))
            
            # 检查相同优先级的规则
            for priority, rule_list in priority_groups.items():
                if len(rule_list) > 1:
                    conflict = {
                        "type": "priority_conflict",
                        "state": state,
                        "priority": priority,
                        "conflicted_intents": [intent for intent, _ in rule_list],
                        "actions": [rule["action"] for _, rule in rule_list],
                        "severity": "high" if priority > 1 else "medium"
                    }
                    conflicts.append(conflict)
                    self.conflicts.append(conflict)
        
        return {
            "total_conflicts": len(conflicts),
            "conflicts_by_state": {state: [c for c in conflicts if c["state"] == state] 
                                 for state in decision_rules.keys()},
            "high_priority_conflicts": [c for c in conflicts if c["severity"] == "high"]
        }
    
    def _analyze_intent_overlaps(self) -> Dict[str, Any]:
        """分析意图重叠"""
        print("  🎯 分析意图重叠...")

        # 分析意图优先级列表中的重叠
        intent_priority = self.engine.intent_priority
        overlaps = []

        # 由于SimplifiedDecisionEngine没有keyword_rules属性，
        # 我们分析硬编码在_extract_intent_from_message中的关键词
        # 这里我们分析优先级列表中的潜在冲突

        # 检查意图优先级是否合理
        priority_issues = []

        # 检查是否有重复的意图
        if len(intent_priority) != len(set(intent_priority)):
            duplicates = []
            seen = set()
            for intent in intent_priority:
                if intent in seen:
                    duplicates.append(intent)
                seen.add(intent)

            if duplicates:
                priority_issues.append({
                    "type": "duplicate_intents_in_priority",
                    "duplicates": duplicates,
                    "severity": "high"
                })

        # 检查优先级顺序的合理性
        # 知识库查询应该有高优先级，这是合理的
        high_priority_intents = intent_priority[:3]  # 前3个高优先级意图
        if "search_knowledge_base" not in high_priority_intents:
            priority_issues.append({
                "type": "knowledge_base_priority_issue",
                "current_priority": intent_priority.index("search_knowledge_base") if "search_knowledge_base" in intent_priority else -1,
                "recommendation": "knowledge_base queries should have high priority",
                "severity": "medium"
            })

        # 检查业务需求的优先级
        if "business_requirement" not in intent_priority[:5]:  # 应该在前5个
            priority_issues.append({
                "type": "business_requirement_priority_issue",
                "current_priority": intent_priority.index("business_requirement") if "business_requirement" in intent_priority else -1,
                "recommendation": "business requirements should have high priority",
                "severity": "medium"
            })

        self.warnings.extend(priority_issues)

        return {
            "total_overlaps": len(overlaps),
            "keyword_overlaps": overlaps,
            "priority_issues": priority_issues,
            "intent_priority_order": intent_priority,
            "analysis_note": "关键词分析受限于硬编码实现，建议重构为配置驱动"
        }
    
    def _analyze_state_consistency(self) -> Dict[str, Any]:
        """分析状态转换一致性"""
        print("  📍 分析状态转换一致性...")
        
        inconsistencies = []
        decision_rules = self.engine._decision_rules
        
        # 检查状态转换的合理性
        valid_transitions = {
            ConversationState.IDLE: [ConversationState.IDLE, ConversationState.COLLECTING_INFO],
            ConversationState.COLLECTING_INFO: [ConversationState.COLLECTING_INFO, ConversationState.DOCUMENTING, ConversationState.IDLE],
            ConversationState.DOCUMENTING: [ConversationState.DOCUMENTING, ConversationState.COMPLETED, ConversationState.IDLE],
            ConversationState.COMPLETED: [ConversationState.IDLE, ConversationState.COLLECTING_INFO]
        }
        
        for state_name, rules in decision_rules.items():
            current_state = ConversationState(state_name)
            valid_next_states = valid_transitions.get(current_state, [])
            
            for intent, rule in rules.items():
                next_state = rule.get("next_state")
                if next_state and next_state not in valid_next_states:
                    inconsistency = {
                        "type": "invalid_state_transition",
                        "current_state": state_name,
                        "intent": intent,
                        "next_state": next_state.value if hasattr(next_state, 'value') else str(next_state),
                        "valid_transitions": [s.value for s in valid_next_states],
                        "severity": "high"
                    }
                    inconsistencies.append(inconsistency)
                    self.conflicts.append(inconsistency)
        
        return {
            "total_inconsistencies": len(inconsistencies),
            "invalid_transitions": inconsistencies,
            "valid_transition_map": {k.value: [v.value for v in vs] for k, vs in valid_transitions.items()}
        }
    
    def _analyze_rule_completeness(self) -> Dict[str, Any]:
        """分析决策规则完整性"""
        print("  📋 分析决策规则完整性...")
        
        missing_rules = []
        decision_rules = self.engine._decision_rules
        intent_priority = self.engine.intent_priority
        
        # 检查每个状态是否有足够的规则覆盖
        for state_name, rules in decision_rules.items():
            available_intents = set(rules.keys()) - {"default"}
            missing_intents = set(intent_priority) - available_intents
            
            if missing_intents:
                missing = {
                    "type": "missing_intent_rules",
                    "state": state_name,
                    "missing_intents": list(missing_intents),
                    "available_intents": list(available_intents),
                    "coverage_rate": len(available_intents) / len(intent_priority),
                    "severity": "medium" if len(missing_intents) < 3 else "high"
                }
                missing_rules.append(missing)
                self.warnings.append(missing)
        
        # 检查是否有默认规则
        states_without_default = []
        for state_name, rules in decision_rules.items():
            if "default" not in rules:
                states_without_default.append(state_name)
                self.warnings.append({
                    "type": "missing_default_rule",
                    "state": state_name,
                    "severity": "high"
                })
        
        return {
            "missing_intent_rules": missing_rules,
            "states_without_default": states_without_default,
            "coverage_analysis": {
                state: {
                    "total_intents": len(intent_priority),
                    "covered_intents": len(set(rules.keys()) - {"default"}),
                    "coverage_rate": len(set(rules.keys()) - {"default"}) / len(intent_priority)
                }
                for state, rules in decision_rules.items()
            }
        }
    
    def generate_recommendations(self) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        
        # 基于冲突分析生成建议
        if self.conflicts:
            recommendations.append({
                "type": "conflict_resolution",
                "priority": "high",
                "description": "发现策略冲突，建议实施统一决策引擎架构",
                "actions": [
                    "实现策略冲突检测机制",
                    "建立优先级解决策略",
                    "添加运行时冲突监控"
                ]
            })
        
        if self.warnings:
            recommendations.append({
                "type": "rule_completeness",
                "priority": "medium", 
                "description": "决策规则覆盖不完整，建议补充缺失规则",
                "actions": [
                    "为每个状态添加完整的意图处理规则",
                    "确保所有状态都有默认处理规则",
                    "建立规则完整性检查机制"
                ]
            })
        
        self.recommendations.extend(recommendations)
        return recommendations
    
    def print_report(self, report: Dict[str, Any]):
        """打印分析报告"""
        print("\n" + "="*60)
        print("📊 策略冲突分析报告")
        print("="*60)
        
        # 摘要
        summary = report["summary"]
        print(f"\n📋 分析摘要:")
        print(f"  🔥 发现冲突: {summary['total_conflicts']} 个")
        print(f"  ⚠️  警告信息: {summary['total_warnings']} 个")
        print(f"  💡 优化建议: {summary['total_recommendations']} 个")
        
        # 优先级冲突
        priority_conflicts = report["priority_conflicts"]
        if priority_conflicts["total_conflicts"] > 0:
            print(f"\n🔥 优先级冲突 ({priority_conflicts['total_conflicts']} 个):")
            for conflict in priority_conflicts["high_priority_conflicts"]:
                print(f"  - 状态 {conflict['state']}: 优先级 {conflict['priority']} 冲突")
                print(f"    意图: {', '.join(conflict['conflicted_intents'])}")
                print(f"    动作: {', '.join(conflict['actions'])}")
        
        # 意图重叠
        intent_overlaps = report["intent_overlaps"]
        if intent_overlaps["total_overlaps"] > 0:
            print(f"\n🎯 意图重叠 ({intent_overlaps['total_overlaps']} 个):")
            for overlap in intent_overlaps["keyword_overlaps"][:3]:  # 只显示前3个
                print(f"  - {' vs '.join(overlap['intents'])}")
                print(f"    重叠关键词: {', '.join(overlap['common_keywords'])}")
        
        # 生成建议
        recommendations = self.generate_recommendations()
        if recommendations:
            print(f"\n💡 优化建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec['description']}")
                for action in rec['actions']:
                    print(f"     - {action}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    analyzer = StrategyConflictAnalyzer()
    
    try:
        # 执行分析
        report = analyzer.analyze_all_conflicts()
        
        # 打印报告
        analyzer.print_report(report)
        
        # 保存详细报告
        output_file = "docs/策略冲突分析报告.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 详细报告已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
