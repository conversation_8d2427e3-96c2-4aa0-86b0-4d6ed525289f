#!/usr/bin/env python3
"""
代码分析脚本 - 检查未使用的导入、注释代码、未调用函数等
"""

import os
import re
import ast
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict

class CodeAnalyzer:
    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.issues = defaultdict(list)
        
    def analyze_file(self, file_path: Path) -> Dict:
        """分析单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            issues = {
                'unused_imports': [],
                'commented_code': [],
                'unused_functions': [],
                'duplicate_imports': [],
                'can_merge_imports': []
            }
            
            # 检查导入
            imports = self._extract_imports(tree)
            used_names = self._extract_used_names(tree, content)
            
            # 检查未使用的导入
            for imp in imports:
                if imp['name'] not in used_names and imp['name'] not in ['logging', 'sys', 'os']:
                    issues['unused_imports'].append({
                        'line': imp['line'],
                        'import': imp['import_str'],
                        'name': imp['name']
                    })
            
            # 检查重复导入
            import_names = [imp['name'] for imp in imports]
            seen = set()
            for imp in imports:
                if imp['name'] in seen:
                    issues['duplicate_imports'].append({
                        'line': imp['line'],
                        'import': imp['import_str'],
                        'name': imp['name']
                    })
                seen.add(imp['name'])
            
            # 检查可以合并的导入
            issues['can_merge_imports'] = self._find_mergeable_imports(imports)
            
            # 检查注释掉的代码
            issues['commented_code'] = self._find_commented_code(content)
            
            # 检查未使用的函数
            issues['unused_functions'] = self._find_unused_functions(tree, content)
            
            return issues
            
        except Exception as e:
            return {'error': str(e)}
    
    def _extract_imports(self, tree: ast.AST) -> List[Dict]:
        """提取所有导入语句"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append({
                        'line': node.lineno,
                        'name': alias.asname or alias.name,
                        'module': alias.name,
                        'import_str': f"import {alias.name}" + (f" as {alias.asname}" if alias.asname else ""),
                        'type': 'import'
                    })
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    name = alias.asname or alias.name
                    imports.append({
                        'line': node.lineno,
                        'name': name,
                        'module': module,
                        'import_str': f"from {module} import {alias.name}" + (f" as {alias.asname}" if alias.asname else ""),
                        'type': 'from_import'
                    })
        
        return imports
    
    def _extract_used_names(self, tree: ast.AST, content: str) -> Set[str]:
        """提取代码中使用的名称"""
        used_names = set()
        
        # 使用AST提取名称
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                used_names.add(node.id)
            elif isinstance(node, ast.Attribute):
                # 处理属性访问，如 module.function
                if isinstance(node.value, ast.Name):
                    used_names.add(node.value.id)
        
        # 使用正则表达式补充检查（处理一些AST可能遗漏的情况）
        # 检查字符串中的使用情况
        string_usage = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', content)
        used_names.update(string_usage)
        
        return used_names
    
    def _find_mergeable_imports(self, imports: List[Dict]) -> List[Dict]:
        """查找可以合并的导入"""
        from_imports = defaultdict(list)
        
        for imp in imports:
            if imp['type'] == 'from_import':
                from_imports[imp['module']].append(imp)
        
        mergeable = []
        for module, import_list in from_imports.items():
            if len(import_list) > 1:
                mergeable.append({
                    'module': module,
                    'imports': import_list,
                    'suggestion': f"from {module} import " + ", ".join([imp['name'] for imp in import_list])
                })
        
        return mergeable
    
    def _find_commented_code(self, content: str) -> List[Dict]:
        """查找注释掉的代码"""
        commented_code = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('#'):
                # 移除注释符号
                potential_code = stripped[1:].strip()
                
                # 检查是否像代码（包含常见的代码模式）
                code_patterns = [
                    r'^\s*(def|class|if|for|while|try|except|finally|with|import|from)\s',
                    r'^\s*[a-zA-Z_][a-zA-Z0-9_]*\s*=',  # 赋值语句
                    r'^\s*[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)',  # 函数调用
                    r'^\s*return\s',
                    r'^\s*raise\s',
                    r'^\s*pass\s*$',
                    r'^\s*break\s*$',
                    r'^\s*continue\s*$'
                ]
                
                for pattern in code_patterns:
                    if re.match(pattern, potential_code):
                        # 排除明显的注释文本
                        comment_patterns = [
                            r'^\s*(TODO|FIXME|NOTE|BUG)',
                            r'^\s*[\u4e00-\u9fff]',  # 中文字符
                            r'^\s*[A-Z][a-z]+.*[.!?]$'  # 英文句子
                        ]
                        
                        is_comment = any(re.match(cp, potential_code) for cp in comment_patterns)
                        
                        if not is_comment:
                            commented_code.append({
                                'line': i,
                                'content': line.strip(),
                                'potential_code': potential_code
                            })
                        break
        
        return commented_code
    
    def _find_unused_functions(self, tree: ast.AST, content: str) -> List[Dict]:
        """查找未使用的函数"""
        defined_functions = []
        called_functions = set()
        
        # 提取定义的函数
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 排除特殊方法和私有方法
                if not (node.name.startswith('_') or node.name in ['__init__', '__str__', '__repr__']):
                    defined_functions.append({
                        'name': node.name,
                        'line': node.lineno
                    })
            
            # 提取函数调用
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    called_functions.add(node.func.id)
                elif isinstance(node.func, ast.Attribute):
                    called_functions.add(node.func.attr)
        
        # 查找未调用的函数
        unused_functions = []
        for func in defined_functions:
            if func['name'] not in called_functions:
                # 进一步检查是否在字符串或注释中被引用
                if func['name'] not in content:
                    unused_functions.append(func)
        
        return unused_functions
    
    def analyze_directory(self, directories: List[str]) -> Dict:
        """分析目录中的所有Python文件"""
        all_issues = {}
        
        for directory in directories:
            dir_path = self.base_path / directory
            if not dir_path.exists():
                continue
                
            for py_file in dir_path.glob('**/*.py'):
                if py_file.name == '__init__.py':
                    continue
                    
                relative_path = py_file.relative_to(self.base_path)
                print(f"分析文件: {relative_path}")
                
                issues = self.analyze_file(py_file)
                if any(issues.values()) and 'error' not in issues:
                    all_issues[str(relative_path)] = issues
        
        return all_issues
    
    def generate_report(self, issues: Dict) -> str:
        """生成分析报告"""
        report = ["# 代码清理分析报告\n"]
        
        total_files = len(issues)
        total_unused_imports = sum(len(file_issues.get('unused_imports', [])) for file_issues in issues.values())
        total_commented_code = sum(len(file_issues.get('commented_code', [])) for file_issues in issues.values())
        total_unused_functions = sum(len(file_issues.get('unused_functions', [])) for file_issues in issues.values())
        total_duplicate_imports = sum(len(file_issues.get('duplicate_imports', [])) for file_issues in issues.values())
        
        report.append(f"## 总结")
        report.append(f"- 分析文件数: {total_files}")
        report.append(f"- 未使用的导入: {total_unused_imports}")
        report.append(f"- 注释掉的代码: {total_commented_code}")
        report.append(f"- 未使用的函数: {total_unused_functions}")
        report.append(f"- 重复的导入: {total_duplicate_imports}")
        report.append("")
        
        for file_path, file_issues in issues.items():
            report.append(f"## {file_path}")
            
            # 未使用的导入
            if file_issues.get('unused_imports'):
                report.append("### 未使用的导入语句")
                for imp in file_issues['unused_imports']:
                    report.append(f"- 行 {imp['line']}: `{imp['import']}` (未使用: {imp['name']})")
                report.append("")
            
            # 注释掉的代码
            if file_issues.get('commented_code'):
                report.append("### 注释掉但未删除的代码")
                for code in file_issues['commented_code']:
                    report.append(f"- 行 {code['line']}: `{code['content']}`")
                report.append("")
            
            # 未使用的函数
            if file_issues.get('unused_functions'):
                report.append("### 定义了但未被调用的函数")
                for func in file_issues['unused_functions']:
                    report.append(f"- 行 {func['line']}: `{func['name']}()`")
                report.append("")
            
            # 重复的导入
            if file_issues.get('duplicate_imports'):
                report.append("### 重复的导入语句")
                for imp in file_issues['duplicate_imports']:
                    report.append(f"- 行 {imp['line']}: `{imp['import']}` (重复导入: {imp['name']})")
                report.append("")
            
            # 可合并的导入
            if file_issues.get('can_merge_imports'):
                report.append("### 可以合并的导入语句")
                for merge in file_issues['can_merge_imports']:
                    lines = [str(imp['line']) for imp in merge['imports']]
                    report.append(f"- 模块 `{merge['module']}` (行 {', '.join(lines)})")
                    report.append(f"  建议合并为: `{merge['suggestion']}`")
                report.append("")
        
        return "\n".join(report)

def main():
    base_path = "/Users/<USER>/由己ai项目/需求采集项目"
    directories = ["backend/agents", "backend/handlers", "backend/data/db", "backend/config", "backend/api"]
    
    analyzer = CodeAnalyzer(base_path)
    issues = analyzer.analyze_directory(directories)
    
    report = analyzer.generate_report(issues)
    
    # 保存报告
    with open(os.path.join(base_path, "code_analysis_report.md"), 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"分析完成！报告已保存到: {base_path}/code_analysis_report.md")
    print(f"发现问题的文件数: {len(issues)}")

if __name__ == "__main__":
    main()