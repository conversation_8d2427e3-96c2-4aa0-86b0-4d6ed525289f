#!/usr/bin/env python3
"""
执行模板更新 - 将意图识别模板与配置文件同步

目标：更新 intent_recognition.md 模板，确保与配置文件完全同步
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def main():
    """执行模板更新"""
    print("🚀 开始执行模板更新\n")
    
    try:
        from backend.utils.template_synchronizer import TemplateSynchronizer
        
        # 创建模板同步器
        synchronizer = TemplateSynchronizer()
        print("✅ 模板同步器创建成功")
        
        # 检查当前同步状态
        print("\n🔍 检查当前同步状态:")
        sync_status = synchronizer.get_sync_status()
        
        print(f"   模板存在: {sync_status.get('template_exists')}")
        print(f"   当前同步状态: {'同步' if sync_status.get('is_synced') else '不同步'}")
        print(f"   配置版本: {sync_status.get('config_version')}")
        print(f"   意图数量: {sync_status.get('intent_count')}")
        
        if sync_status.get('differences'):
            print("   发现的差异:")
            for diff in sync_status.get('differences', []):
                print(f"   - {diff}")
        
        # 询问是否继续更新
        if sync_status.get('is_synced'):
            print("\n✅ 模板已经与配置同步，无需更新")
            return True
        
        print(f"\n⚠️ 模板与配置不同步，准备更新...")
        
        # 显示即将更新的内容预览
        print("\n🔍 生成新模板内容预览:")
        new_content = synchronizer.generate_template_content()
        
        if new_content:
            print(f"   新模板长度: {len(new_content)} 字符")
            
            # 显示意图定义部分的预览
            intent_section = synchronizer.generate_intent_section()
            lines = intent_section.split('\n')
            preview_lines = lines[:15]  # 显示前15行
            
            print("   意图定义预览:")
            for line in preview_lines:
                if line.strip():
                    print(f"   {line}")
            
            if len(lines) > 15:
                print(f"   ... (还有 {len(lines) - 15} 行)")
        
        # 执行更新
        print(f"\n🔄 执行模板更新...")
        success = synchronizer.update_template()
        
        if success:
            print("✅ 模板更新成功！")
            
            # 验证更新后的同步状态
            print("\n🔍 验证更新后的同步状态:")
            new_sync_status = synchronizer.get_sync_status()
            
            if new_sync_status.get('is_synced'):
                print("✅ 更新后模板与配置完全同步")
            else:
                print("⚠️ 更新后仍存在不同步问题:")
                for diff in new_sync_status.get('differences', []):
                    print(f"   - {diff}")
            
            return success
        else:
            print("❌ 模板更新失败")
            return False
        
    except Exception as e:
        print(f"\n❌ 模板更新过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 模板更新完成！")
        print("\n📋 更新总结:")
        print("- [x] 模板已与配置文件同步")
        print("- [x] 所有意图定义已更新")
        print("- [x] 原模板已备份")
        print("\n🎯 现在可以测试LLM意图识别功能")
    else:
        print("\n⚠️ 模板更新失败，请检查错误信息并重试")
