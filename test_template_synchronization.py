#!/usr/bin/env python3
"""
阶段3模板同步测试：验证模板与配置的同步功能

目标：验证模板同步器能够正确生成和更新意图识别模板
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_template_synchronizer_basic():
    """测试模板同步器基础功能"""
    print("🧪 开始测试模板同步器基础功能\n")
    
    try:
        # 导入模板同步器
        from backend.utils.template_synchronizer import TemplateSynchronizer
        from backend.utils.intent_manager import IntentManager
        print("✅ 成功导入 TemplateSynchronizer")
        
        # 创建意图管理器和模板同步器
        intent_manager = IntentManager()
        synchronizer = TemplateSynchronizer(intent_manager)
        print("✅ 成功创建 TemplateSynchronizer 实例")
        
        # 测试意图定义部分生成
        print("\n🔍 测试意图定义生成:")
        intent_section = synchronizer.generate_intent_section()
        
        if intent_section and "## 基础意图类型" in intent_section:
            print("✅ 意图定义部分生成成功")
            
            # 检查是否包含关键意图
            key_intents = ["business_requirement", "greeting", "domain_specific_query"]
            for intent in key_intents:
                if intent in intent_section:
                    print(f"   ✅ 包含关键意图: {intent}")
                else:
                    print(f"   ❌ 缺少关键意图: {intent}")
        else:
            print("❌ 意图定义部分生成失败")
            return False
        
        # 测试完整模板生成
        print("\n🔍 测试完整模板生成:")
        full_template = synchronizer.generate_template_content()
        
        if full_template and len(full_template) > 1000:  # 模板应该比较长
            print("✅ 完整模板生成成功")
            print(f"   模板长度: {len(full_template)} 字符")
            
            # 检查模板结构
            required_sections = [
                "# 意图识别提示词模板",
                "## 基础意图类型",
                "## 子意图类型",
                "## 情感识别",
                "## 置信度评估"
            ]
            
            for section in required_sections:
                if section in full_template:
                    print(f"   ✅ 包含必需部分: {section}")
                else:
                    print(f"   ❌ 缺少必需部分: {section}")
        else:
            print("❌ 完整模板生成失败")
            return False
        
        # 测试同步状态检查
        print("\n🔍 测试同步状态检查:")
        sync_status = synchronizer.get_sync_status()
        
        if sync_status:
            print("✅ 同步状态检查成功")
            print(f"   模板存在: {sync_status.get('template_exists')}")
            print(f"   配置版本: {sync_status.get('config_version')}")
            print(f"   意图数量: {sync_status.get('intent_count')}")
            
            if sync_status.get('differences'):
                print(f"   差异: {sync_status.get('differences')}")
            else:
                print("   ✅ 无差异发现")
        else:
            print("❌ 同步状态检查失败")
            return False
        
        print("\n🎉 模板同步器基础功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_validation():
    """测试模板验证功能"""
    print("🧪 测试模板验证功能\n")
    
    try:
        from backend.utils.template_synchronizer import TemplateSynchronizer
        synchronizer = TemplateSynchronizer()
        
        # 测试模板同步验证
        print("🔍 测试模板同步验证:")
        is_synced, differences = synchronizer.validate_template_sync()
        
        print(f"✅ 同步验证完成: {'同步' if is_synced else '不同步'}")
        
        if differences:
            print("   发现的差异:")
            for diff in differences:
                print(f"   - {diff}")
        else:
            print("   ✅ 模板与配置完全同步")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板验证测试失败: {e}")
        return False

def test_template_update():
    """测试模板更新功能"""
    print("🧪 测试模板更新功能\n")
    
    try:
        from backend.utils.template_synchronizer import TemplateSynchronizer
        synchronizer = TemplateSynchronizer()
        
        # 检查原始模板状态
        print("🔍 检查原始模板状态:")
        original_sync_status = synchronizer.get_sync_status()
        print(f"   原始同步状态: {'同步' if original_sync_status.get('is_synced') else '不同步'}")
        
        if original_sync_status.get('differences'):
            print("   原始差异:")
            for diff in original_sync_status.get('differences', []):
                print(f"   - {diff}")
        
        # 测试模板更新（但不实际执行，避免覆盖现有模板）
        print("\n🔍 测试模板更新准备:")
        
        # 生成新模板内容
        new_content = synchronizer.generate_template_content()
        if new_content and len(new_content) > 1000:
            print("✅ 新模板内容生成成功")
            print(f"   新模板长度: {len(new_content)} 字符")
            
            # 检查新模板是否包含所有配置中的意图
            config_intents = synchronizer.intent_manager.get_valid_intents()
            missing_intents = []
            
            for intent in config_intents:
                if intent not in new_content:
                    missing_intents.append(intent)
            
            if missing_intents:
                print(f"   ⚠️ 新模板中缺少意图: {missing_intents}")
            else:
                print("   ✅ 新模板包含所有配置中的意图")
                
            return True
        else:
            print("❌ 新模板内容生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 模板更新测试失败: {e}")
        return False

def test_intent_extraction():
    """测试从模板中提取意图的功能"""
    print("🧪 测试意图提取功能\n")
    
    try:
        from backend.utils.template_synchronizer import TemplateSynchronizer
        synchronizer = TemplateSynchronizer()
        
        # 测试样本模板内容
        sample_template = """
## 基础意图类型（intent，必选其一）

- **business_requirement**: 用户描述具体的业务需求
- **greeting**: 用户的问候或打招呼
- domain_specific_query: 用户询问特定领域知识
- request_clarification: 用户请求澄清或解释
"""
        
        print("🔍 测试从样本模板提取意图:")
        extracted_intents = synchronizer._extract_intents_from_template(sample_template)
        
        expected_intents = {"business_requirement", "greeting", "domain_specific_query", "request_clarification"}
        
        print(f"   提取到的意图: {sorted(extracted_intents)}")
        print(f"   期望的意图: {sorted(expected_intents)}")
        
        if extracted_intents == expected_intents:
            print("✅ 意图提取功能正常")
            return True
        else:
            print("❌ 意图提取结果不匹配")
            missing = expected_intents - extracted_intents
            extra = extracted_intents - expected_intents
            if missing:
                print(f"   缺少: {missing}")
            if extra:
                print(f"   多余: {extra}")
            return False
        
    except Exception as e:
        print(f"❌ 意图提取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始阶段3模板同步测试\n")
    
    # 测试1: 模板同步器基础功能
    print("=== 测试1: 模板同步器基础功能 ===")
    test1_result = test_template_synchronizer_basic()
    
    print("\n" + "="*50 + "\n")
    
    # 测试2: 模板验证功能
    print("=== 测试2: 模板验证功能 ===")
    test2_result = test_template_validation()
    
    print("\n" + "="*50 + "\n")
    
    # 测试3: 模板更新功能
    print("=== 测试3: 模板更新功能 ===")
    test3_result = test_template_update()
    
    print("\n" + "="*50 + "\n")
    
    # 测试4: 意图提取功能
    print("=== 测试4: 意图提取功能 ===")
    test4_result = test_intent_extraction()
    
    # 总结
    print("\n" + "="*50)
    print("🏁 阶段3模板同步测试总结")
    print(f"基础功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"模板验证测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"模板更新测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"意图提取测试: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result and test4_result
    
    if all_passed:
        print("🎉 阶段3模板同步功能测试通过！")
        print("\n📋 模板同步器功能验证:")
        print("- [x] 模板同步器基础功能正常")
        print("- [x] 模板验证功能正常")
        print("- [x] 模板更新准备功能正常")
        print("- [x] 意图提取功能正常")
        print("\n🎯 准备执行实际的模板更新")
    else:
        print("⚠️ 阶段3存在问题，需要修复后再继续")

if __name__ == "__main__":
    main()
