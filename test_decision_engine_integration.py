#!/usr/bin/env python3
"""
阶段2集成测试：验证 SimplifiedDecisionEngine 与 IntentManager 的集成

目标：验证意图验证逻辑已成功从硬编码改为配置驱动
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_decision_engine_integration():
    """测试决策引擎与意图管理器的集成"""
    print("🧪 开始测试决策引擎集成功能\n")
    
    try:
        # 导入决策引擎
        from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
        print("✅ 成功导入 SimplifiedDecisionEngine")
        
        # 创建决策引擎实例
        engine = SimplifiedDecisionEngine()
        print("✅ 成功创建 SimplifiedDecisionEngine 实例")
        
        # 检查意图管理器是否正确初始化
        if hasattr(engine, 'intent_manager') and engine.intent_manager:
            print("✅ IntentManager 成功集成到决策引擎")
            
            # 测试配置驱动的意图获取
            valid_intents = engine.intent_manager.get_valid_intents()
            print(f"✅ 从配置获取有效意图列表成功，共 {len(valid_intents)} 个意图")
            print(f"   前5个意图: {valid_intents[:5]}")
            
        else:
            print("⚠️ IntentManager 未正确集成，使用备用模式")
        
        # 测试意图识别功能是否正常工作
        print("\n🔍 测试意图识别功能:")
        
        # 模拟一些测试消息
        test_messages = [
            ("你好", "greeting"),
            ("我想做个网站", "business_requirement"),
            ("什么是UI设计", "domain_specific_query"),
            ("今天天气真好", "general_chat"),
            ("不太明白", "request_clarification")
        ]
        
        # 注意：这里我们只测试决策引擎的初始化和配置加载
        # 完整的意图识别需要LLM服务，我们在这里主要验证集成是否成功
        
        for message, expected_intent in test_messages:
            print(f"   测试消息: '{message}' (期望意图: {expected_intent})")
            
            # 验证期望的意图是否在有效意图列表中
            if engine.intent_manager:
                is_valid = engine.intent_manager.is_valid_intent(expected_intent)
                status = "✅" if is_valid else "❌"
                print(f"     {status} 意图 '{expected_intent}' 在配置中: {is_valid}")
            else:
                print("     ⚠️ 无法验证（使用备用模式）")
        
        # 测试配置完整性检查
        print("\n🔍 测试配置完整性检查:")
        if engine.intent_manager:
            config_info = engine.intent_manager.get_config_info()
            print(f"✅ 配置信息:")
            print(f"   - 版本: {config_info.get('version')}")
            print(f"   - 意图数量: {config_info.get('intent_count')}")
            print(f"   - 支持状态: {config_info.get('states')}")
        else:
            print("⚠️ 无法获取配置信息（使用备用模式）")
        
        # 测试决策规则获取
        print("\n🔍 测试决策规则获取:")
        if engine.intent_manager:
            decision_rules = engine.intent_manager.get_decision_rules()
            priority_order = engine.intent_manager.get_priority_order()
            default_action = engine.intent_manager.get_default_action()
            
            print(f"✅ 决策规则配置:")
            print(f"   - 优先级顺序: {priority_order[:3]}... (共{len(priority_order)}个)")
            print(f"   - 默认动作: {default_action}")
        else:
            print("⚠️ 无法获取决策规则（使用备用模式）")
        
        print("\n🎉 决策引擎集成测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("🧪 测试向后兼容性\n")
    
    try:
        # 测试在配置文件不存在的情况下是否能正常工作
        print("🔍 测试备用模式兼容性:")
        
        # 临时重命名配置文件来模拟配置加载失败
        config_path = Path("backend/config/intent_definitions.yaml")
        backup_path = Path("backend/config/intent_definitions.yaml.backup")
        
        config_exists = config_path.exists()
        if config_exists:
            config_path.rename(backup_path)
            print("   暂时移除配置文件以测试备用模式")
        
        try:
            from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
            engine = SimplifiedDecisionEngine()
            
            if not engine.intent_manager:
                print("✅ 备用模式正常工作：配置加载失败时使用硬编码列表")
            else:
                print("⚠️ 意外：配置文件不存在但仍然加载成功")
                
        finally:
            # 恢复配置文件
            if config_exists and backup_path.exists():
                backup_path.rename(config_path)
                print("   已恢复配置文件")
        
        print("✅ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        # 确保恢复配置文件
        if backup_path.exists():
            backup_path.rename(config_path)
        return False

def test_configuration_validation():
    """测试配置验证功能"""
    print("🧪 测试配置验证功能\n")
    
    try:
        from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
        engine = SimplifiedDecisionEngine()
        
        if engine.intent_manager:
            print("🔍 测试关键意图存在性:")
            
            # 检查关键意图
            required_intents = [
                "greeting", "business_requirement", "unknown", 
                "general_chat", "request_clarification"
            ]
            
            all_present = True
            for intent in required_intents:
                is_present = engine.intent_manager.is_valid_intent(intent)
                status = "✅" if is_present else "❌"
                print(f"   {status} {intent}: {is_present}")
                if not is_present:
                    all_present = False
            
            if all_present:
                print("✅ 所有关键意图都已正确配置")
            else:
                print("⚠️ 部分关键意图缺失")
                
            return all_present
        else:
            print("⚠️ 无法测试配置验证（使用备用模式）")
            return True
            
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始阶段2集成测试\n")
    
    # 测试1: 决策引擎集成
    print("=== 测试1: 决策引擎集成 ===")
    test1_result = test_decision_engine_integration()
    
    print("\n" + "="*50 + "\n")
    
    # 测试2: 向后兼容性
    print("=== 测试2: 向后兼容性 ===")
    test2_result = test_backward_compatibility()
    
    print("\n" + "="*50 + "\n")
    
    # 测试3: 配置验证
    print("=== 测试3: 配置验证 ===")
    test3_result = test_configuration_validation()
    
    # 总结
    print("\n" + "="*50)
    print("🏁 阶段2集成测试总结")
    print(f"决策引擎集成: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"向后兼容性: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"配置验证: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if test1_result and test2_result and test3_result:
        print("🎉 阶段2核心集成成功！")
        print("\n📋 阶段2完成状态:")
        print("- [x] SimplifiedDecisionEngine 成功集成 IntentManager")
        print("- [x] 意图验证逻辑改为配置驱动")
        print("- [x] 添加启动时配置完整性检查")
        print("- [x] 保持向后兼容性")
        print("\n🎯 准备进入阶段3: 模板同步")
    else:
        print("⚠️ 阶段2存在问题，需要修复后再继续")

if __name__ == "__main__":
    main()
