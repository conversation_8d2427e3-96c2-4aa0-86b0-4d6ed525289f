#!/usr/bin/env python3
"""
验证模板同步状态 - 检查修复后的模板同步情况

目标：验证模板与配置文件的同步状态
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def main():
    """验证模板同步状态"""
    print("🚀 验证模板同步状态\n")
    
    try:
        from backend.utils.template_synchronizer import TemplateSynchronizer
        
        # 创建模板同步器
        synchronizer = TemplateSynchronizer()
        print("✅ 模板同步器创建成功")
        
        # 检查同步状态
        print("\n🔍 检查模板同步状态:")
        is_synced, differences = synchronizer.validate_template_sync()
        
        print(f"   同步状态: {'✅ 同步' if is_synced else '❌ 不同步'}")
        
        if differences:
            print("   发现的差异:")
            for diff in differences:
                print(f"   - {diff}")
        else:
            print("   ✅ 模板与配置完全同步")
        
        # 显示详细的同步信息
        print("\n🔍 详细同步信息:")
        sync_status = synchronizer.get_sync_status()
        
        print(f"   模板存在: {sync_status.get('template_exists')}")
        print(f"   配置版本: {sync_status.get('config_version')}")
        print(f"   意图数量: {sync_status.get('intent_count')}")
        
        # 显示配置中的意图列表
        config_intents = synchronizer.intent_manager.get_valid_intents()
        print(f"\n📋 配置中的意图列表 (共{len(config_intents)}个):")
        for i, intent in enumerate(sorted(config_intents), 1):
            print(f"   {i:2d}. {intent}")
        
        # 显示模板中的意图列表
        if synchronizer.template_path.exists():
            with open(synchronizer.template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            template_intents = synchronizer._extract_intents_from_template(template_content)
            print(f"\n📋 模板中的意图列表 (共{len(template_intents)}个):")
            for i, intent in enumerate(sorted(template_intents), 1):
                print(f"   {i:2d}. {intent}")
            
            # 比较差异
            print(f"\n🔍 详细差异分析:")
            config_set = set(config_intents)
            template_set = set(template_intents)
            
            missing_in_template = config_set - template_set
            extra_in_template = template_set - config_set
            common_intents = config_set & template_set
            
            print(f"   共同意图: {len(common_intents)} 个")
            if common_intents:
                print(f"   {sorted(common_intents)}")
            
            if missing_in_template:
                print(f"   模板中缺少: {len(missing_in_template)} 个")
                print(f"   {sorted(missing_in_template)}")
            
            if extra_in_template:
                print(f"   模板中多余: {len(extra_in_template)} 个")
                print(f"   {sorted(extra_in_template)}")
        
        return is_synced
        
    except Exception as e:
        print(f"\n❌ 验证过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 模板与配置完全同步！")
        print("\n📋 同步验证完成:")
        print("- [x] 模板文件存在且可读")
        print("- [x] 配置文件加载正常")
        print("- [x] 意图定义完全匹配")
        print("- [x] 无多余或缺失的意图")
        print("\n🎯 可以进行LLM意图识别测试")
    else:
        print("\n⚠️ 模板与配置存在不同步问题")
        print("建议检查并修复差异后重新验证")
