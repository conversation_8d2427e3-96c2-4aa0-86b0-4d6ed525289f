"""
模板同步器 - 确保意图识别模板与配置文件保持同步

目标：
- 从配置文件自动生成意图识别模板
- 验证现有模板与配置的一致性
- 提供模板更新和同步功能

作者：AI Assistant
版本：1.0
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import re

from backend.utils.intent_manager import IntentManager

class TemplateSynchronizer:
    """
    模板同步器 - 管理意图识别模板与配置的同步
    
    功能：
    1. 从配置文件生成意图识别模板
    2. 验证现有模板与配置的一致性
    3. 更新模板内容保持同步
    4. 提供模板差异分析
    """
    
    def __init__(self, intent_manager: Optional[IntentManager] = None, template_path: Optional[str] = None):
        """
        初始化模板同步器
        
        Args:
            intent_manager: 意图管理器实例，默认创建新实例
            template_path: 模板文件路径，默认为 backend/prompts/intent_recognition.md
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化意图管理器
        if intent_manager:
            self.intent_manager = intent_manager
        else:
            self.intent_manager = IntentManager()
        
        # 设置模板文件路径
        if template_path is None:
            current_dir = Path(__file__).parent
            project_root = current_dir.parent
            template_path = project_root / "prompts" / "intent_recognition.md"
        
        self.template_path = Path(template_path)
        
        self.logger.info(f"模板同步器初始化完成，模板路径: {self.template_path}")
    
    def generate_intent_section(self) -> str:
        """
        🔄 [意图统一化] 从配置生成意图定义部分
        
        Returns:
            str: 生成的意图定义文本
        """
        try:
            valid_intents = self.intent_manager.get_valid_intents()
            intent_definitions = []
            
            # 生成意图定义列表
            intent_definitions.append("## 基础意图类型（intent，必选其一）")
            intent_definitions.append("")
            intent_definitions.append("**重要：优先识别业务需求！**")
            intent_definitions.append("")
            
            # 按优先级排序意图
            priority_order = self.intent_manager.get_priority_order()
            
            # 如果配置中有优先级顺序，按优先级排序；否则按字母顺序
            if priority_order:
                # 确保所有意图都包含在内
                sorted_intents = []
                for intent in priority_order:
                    if intent in valid_intents:
                        sorted_intents.append(intent)
                
                # 添加不在优先级列表中的意图
                for intent in valid_intents:
                    if intent not in sorted_intents:
                        sorted_intents.append(intent)
            else:
                sorted_intents = sorted(valid_intents)
            
            # 生成每个意图的定义
            for intent in sorted_intents:
                intent_config = self.intent_manager.get_intent_config(intent)
                if not intent_config:
                    continue
                
                description = intent_config.get('description', '无描述')
                examples = intent_config.get('examples', [])
                
                # 特殊处理 business_requirement（保持原有的详细说明）
                if intent == 'business_requirement':
                    intent_definitions.append(f"- **{intent}**: {description}")
                    if examples:
                        example_text = '、'.join([f'"{ex}"' for ex in examples[:4]])  # 最多显示4个例子
                        intent_definitions.append(f"  * 示例：{example_text}")
                    intent_definitions.append("  * 这是最重要的意图类型，当用户描述任何具体的业务目标时都应该选择此项")
                else:
                    # 其他意图的标准格式
                    if examples:
                        example_text = '、'.join([f'"{ex}"' for ex in examples[:3]])  # 最多显示3个例子
                        intent_definitions.append(f"- **{intent}**: {description}")
                        intent_definitions.append(f"  * 示例：{example_text}")
                    else:
                        intent_definitions.append(f"- **{intent}**: {description}")
                
                intent_definitions.append("")
            
            return '\n'.join(intent_definitions)
            
        except Exception as e:
            self.logger.error(f"生成意图定义部分失败: {e}")
            return "## 基础意图类型（intent，必选其一）\n\n生成失败，请检查配置文件。"
    
    def generate_template_content(self) -> str:
        """
        🔄 [意图统一化] 生成完整的模板内容
        
        Returns:
            str: 完整的模板内容
        """
        try:
            # 读取现有模板的固定部分（头部和尾部）
            if self.template_path.exists():
                with open(self.template_path, 'r', encoding='utf-8') as f:
                    current_content = f.read()
                
                # 提取头部（到意图定义部分之前）
                header_match = re.search(r'^(.*?)^## 基础意图类型', current_content, re.MULTILINE | re.DOTALL)
                if header_match:
                    header = header_match.group(1).rstrip()
                else:
                    header = self._get_default_header()
                
                # 提取尾部（从子意图类型开始）
                footer_match = re.search(r'^## 子意图类型.*$', current_content, re.MULTILINE | re.DOTALL)
                if footer_match:
                    footer = footer_match.group(0)
                else:
                    footer = self._get_default_footer()
            else:
                header = self._get_default_header()
                footer = self._get_default_footer()
            
            # 生成意图定义部分
            intent_section = self.generate_intent_section()
            
            # 组合完整内容
            full_content = f"{header}\n\n{intent_section}\n{footer}"
            
            return full_content
            
        except Exception as e:
            self.logger.error(f"生成模板内容失败: {e}")
            return self._get_fallback_template()
    
    def _get_default_header(self) -> str:
        """获取默认的模板头部"""
        return """# 意图识别提示词模板 (版本 v4)

## ！！！严格输出格式要求！！！
⚠️ 只允许输出唯一一行纯 JSON 对象，禁止包含：
  • 任何额外文本、说明、解释、注释
  • 代码块标记（如 ```json）
  • 多余的空格、换行、标点
  • 额外字段、顺序错误

✅ 唯一可接受格式：
{
  "intent": "provide_information",
  "sub_intent": "answer_question",
  "emotion": "neutral",
  "confidence": 0.92,
  "entities": {}
}

## 意图识别任务
请根据下面的用户输入，分析用户的意图、子意图和情感。

## 当前会话状态
{current_state}

## 用户输入
{user_input}

## 对话历史
{full_conversation}

## 🔥 状态感知规则（优先级最高）
**根据当前会话状态调整意图识别策略：**

### 当current_state为"COLLECTING_INFO"时：
- **优先识别为process_answer**：用户很可能在回答系统提出的问题
- 除非用户明确表示要重新开始（如"新聊天"、"重新开始"、"新需求"）
- 或者用户明确提出新的不相关问题

### 当current_state为"IDLE"时：
- 优先识别business_requirement：用户描述新的业务需求
- 正常应用所有意图识别规则

### 当current_state为"DOCUMENTING"时：
- 优先识别文档相关操作：confirm、modify、complete等"""
    
    def _get_default_footer(self) -> str:
        """获取默认的模板尾部"""
        return """## 子意图类型（sub_intent，根据主意图选择）

### 当intent为business_requirement时
- software_development: 软件开发需求（如网站、APP、系统开发等）
- design_requirement: 设计需求（如UI设计、品牌设计、产品设计等）
- marketing_requirement: 营销需求（如营销策划、推广活动、品牌推广等）
- business_process: 业务流程需求（如流程优化、管理制度等）
- consulting_requirement: 咨询需求（如战略咨询、技术咨询等）
- content_creation: 内容创作需求（如文案、视频、图片等）
- event_planning: 活动策划需求（如会议、培训、活动组织等）
- other_business: 其他业务需求

### 当intent为provide_information时
- answer_question: 回答系统提出的问题
- supplement_info: 主动补充额外信息
- correct_info: 纠正之前提供的信息
- elaborate_details: 详细阐述某个方面
- provide_example: 提供具体示例或案例
- provide_preference: 表达个人偏好或选择

### 当intent为ask_question时
- requirement_question: 关于需求收集过程的问题
- domain_knowledge_question: 关于特定领域知识的问题
- technical_question: 关于技术实现的问题
- scope_question: 关于项目范围的问题
- timeline_question: 关于时间线或进度的问题
- cost_question: 关于成本或资源的问题

### 当intent为request_clarification时
- term_clarification: 请求解释专业术语
- question_clarification: 请求解释系统提问的含义
- process_clarification: 请求解释流程或步骤
- requirement_clarification: 请求澄清需求相关内容

## 情感识别（emotion）
- positive: 积极、兴奋、满意
- negative: 消极、沮丧、不满
- neutral: 中性、平静
- confused: 困惑、不理解
- anxious: 焦虑、担心
- frustrated: 挫败、烦躁

## 置信度评估（confidence）
- 0.9-1.0: 非常确定
- 0.7-0.9: 比较确定
- 0.5-0.7: 一般确定
- 0.3-0.5: 不太确定
- 0.0-0.3: 很不确定

## 实体提取（entities）
根据意图类型提取相关实体信息（可选）：
- 时间相关：时间点、时间段、截止日期
- 预算相关：金额、预算范围
- 技术相关：技术栈、平台、工具
- 业务相关：行业、规模、目标用户"""
    
    def _get_fallback_template(self) -> str:
        """获取备用模板内容"""
        return f"{self._get_default_header()}\n\n{self.generate_intent_section()}\n{self._get_default_footer()}"
    
    def update_template(self) -> bool:
        """
        🔄 [意图统一化] 更新模板文件
        
        Returns:
            bool: 是否更新成功
        """
        try:
            # 生成新的模板内容
            new_content = self.generate_template_content()
            
            # 备份原文件（如果存在）
            if self.template_path.exists():
                backup_path = self.template_path.with_suffix('.md.backup')
                self.template_path.rename(backup_path)
                self.logger.info(f"已备份原模板文件到: {backup_path}")
            
            # 写入新内容
            with open(self.template_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            self.logger.info(f"模板文件更新成功: {self.template_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新模板文件失败: {e}")
            return False
    
    def validate_template_sync(self) -> Tuple[bool, List[str]]:
        """
        🔄 [意图统一化] 验证模板与配置的同步性
        
        Returns:
            Tuple[bool, List[str]]: (是否同步, 差异列表)
        """
        try:
            if not self.template_path.exists():
                return False, ["模板文件不存在"]
            
            # 读取当前模板
            with open(self.template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 从配置获取意图列表
            config_intents = set(self.intent_manager.get_valid_intents())
            
            # 从模板提取意图列表
            template_intents = self._extract_intents_from_template(template_content)
            
            # 比较差异
            differences = []
            
            # 检查配置中有但模板中没有的意图
            missing_in_template = config_intents - template_intents
            if missing_in_template:
                differences.append(f"模板中缺少意图: {sorted(missing_in_template)}")
            
            # 检查模板中有但配置中没有的意图
            extra_in_template = template_intents - config_intents
            if extra_in_template:
                differences.append(f"模板中多余意图: {sorted(extra_in_template)}")
            
            is_synced = len(differences) == 0
            
            if is_synced:
                self.logger.info("模板与配置同步检查通过")
            else:
                self.logger.warning(f"模板与配置不同步: {differences}")
            
            return is_synced, differences
            
        except Exception as e:
            self.logger.error(f"验证模板同步性失败: {e}")
            return False, [f"验证失败: {str(e)}"]
    
    def _extract_intents_from_template(self, template_content: str) -> set:
        """
        从模板内容中提取意图列表

        Args:
            template_content: 模板内容

        Returns:
            set: 意图名称集合
        """
        intents = set()

        # 只从"基础意图类型"部分提取意图
        # 找到基础意图类型部分的开始和结束
        lines = template_content.split('\n')
        in_intent_section = False

        for line in lines:
            line = line.strip()

            # 检查是否进入基础意图类型部分
            if line.startswith('## 基础意图类型'):
                in_intent_section = True
                continue

            # 检查是否离开基础意图类型部分
            if in_intent_section and line.startswith('## ') and '基础意图类型' not in line:
                break

            # 在意图部分内，提取意图名称
            if in_intent_section:
                # 匹配 "- **intent_name**:" 或 "- intent_name:" 格式
                intent_match = re.match(r'^- \*?\*?([a-z_]+)\*?\*?:', line)
                if intent_match:
                    intent_name = intent_match.group(1)
                    intents.add(intent_name)

        return intents
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        获取同步状态信息
        
        Returns:
            Dict[str, Any]: 同步状态信息
        """
        try:
            is_synced, differences = self.validate_template_sync()
            config_info = self.intent_manager.get_config_info()
            
            return {
                'is_synced': is_synced,
                'differences': differences,
                'template_path': str(self.template_path),
                'template_exists': self.template_path.exists(),
                'config_version': config_info.get('version', 'unknown'),
                'intent_count': config_info.get('intent_count', 0),
                'last_check': 'just_now'
            }
            
        except Exception as e:
            self.logger.error(f"获取同步状态失败: {e}")
            return {
                'is_synced': False,
                'differences': [f"状态检查失败: {str(e)}"],
                'error': str(e)
            }
