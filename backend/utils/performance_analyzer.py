#!/usr/bin/env python3
"""
性能分析工具 - 分析ConversationFlowAgent创建过程中的性能瓶颈

用于测量和分析系统启动过程中各个组件的初始化时间，
为组件池化优化提供数据支撑。
"""

import time
import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from datetime import datetime
import traceback
import psutil
import os

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标数据结构"""
    component_name: str
    operation: str
    start_time: float
    end_time: float
    duration: float
    memory_before: float
    memory_after: float
    memory_delta: float
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.session_start_time = time.time()
        self.process = psutil.Process(os.getpid())
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            return self.process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0

    @contextmanager
    def measure_component(self, component_name: str, operation: str, metadata: Dict[str, Any] = None):
        """测量组件性能的上下文管理器"""
        start_time = time.time()
        memory_before = self.get_memory_usage()
        success = True
        error_message = None

        try:
            self.logger.debug(f"开始测量: {component_name}.{operation}")
            yield

        except Exception as e:
            success = False
            error_message = str(e)
            self.logger.error(f"组件 {component_name}.{operation} 执行失败: {e}")
            raise

        finally:
            end_time = time.time()
            memory_after = self.get_memory_usage()
            duration = end_time - start_time
            memory_delta = memory_after - memory_before

            metric = PerformanceMetric(
                component_name=component_name,
                operation=operation,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                memory_before=memory_before,
                memory_after=memory_after,
                memory_delta=memory_delta,
                success=success,
                error_message=error_message,
                metadata=metadata or {}
            )

            self.metrics.append(metric)
            self.logger.info(f"测量完成: {component_name}.{operation} - {duration:.3f}s, 内存变化: {memory_delta:+.2f}MB")

    def get_summary(self) -> Dict[str, Any]:
        """获取性能分析摘要"""
        if not self.metrics:
            return {"error": "没有性能数据"}

        total_duration = sum(m.duration for m in self.metrics)
        total_memory_delta = sum(m.memory_delta for m in self.metrics)
        success_count = sum(1 for m in self.metrics if m.success)

        # 按组件分组统计
        component_stats = {}
        for metric in self.metrics:
            if metric.component_name not in component_stats:
                component_stats[metric.component_name] = {
                    "total_duration": 0,
                    "total_memory_delta": 0,
                    "operation_count": 0,
                    "operations": []
                }

            stats = component_stats[metric.component_name]
            stats["total_duration"] += metric.duration
            stats["total_memory_delta"] += metric.memory_delta
            stats["operation_count"] += 1
            stats["operations"].append({
                "operation": metric.operation,
                "duration": metric.duration,
                "memory_delta": metric.memory_delta,
                "success": metric.success
            })

        # 找出最耗时的组件
        slowest_components = sorted(
            component_stats.items(),
            key=lambda x: x[1]["total_duration"],
            reverse=True
        )[:5]

        # 找出内存消耗最大的组件
        memory_intensive_components = sorted(
            component_stats.items(),
            key=lambda x: x[1]["total_memory_delta"],
            reverse=True
        )[:5]

        return {
            "session_summary": {
                "total_metrics": len(self.metrics),
                "total_duration": total_duration,
                "total_memory_delta": total_memory_delta,
                "success_rate": success_count / len(self.metrics) * 100,
                "session_duration": time.time() - self.session_start_time
            },
            "component_stats": component_stats,
            "top_slowest_components": [
                {"name": name, "duration": stats["total_duration"], "operations": stats["operation_count"]}
                for name, stats in slowest_components
            ],
            "top_memory_intensive_components": [
                {"name": name, "memory_delta": stats["total_memory_delta"], "operations": stats["operation_count"]}
                for name, stats in memory_intensive_components
            ],
            "detailed_metrics": [asdict(m) for m in self.metrics]
        }

    def save_report(self, filepath: str = None) -> str:
        """保存性能分析报告"""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"logs/performance/agent_creation_analysis_{timestamp}.json"

        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        summary = self.get_summary()

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False, default=str)

        self.logger.info(f"性能分析报告已保存到: {filepath}")
        return filepath

    def print_summary(self):
        """打印性能分析摘要"""
        summary = self.get_summary()

        print("\n" + "="*80)
        print("ConversationFlowAgent 创建性能分析报告")
        print("="*80)

        session = summary["session_summary"]
        print(f"总测量指标数: {session['total_metrics']}")
        print(f"总耗时: {session['total_duration']:.3f}s")
        print(f"总内存变化: {session['total_memory_delta']:+.2f}MB")
        print(f"成功率: {session['success_rate']:.1f}%")

        print("\n最耗时的组件 (Top 5):")
        print("-" * 50)
        for comp in summary["top_slowest_components"]:
            print(f"  {comp['name']:<30} {comp['duration']:.3f}s ({comp['operations']} 操作)")

        print("\n内存消耗最大的组件 (Top 5):")
        print("-" * 50)
        for comp in summary["top_memory_intensive_components"]:
            print(f"  {comp['name']:<30} {comp['memory_delta']:+.2f}MB ({comp['operations']} 操作)")

        print("\n详细组件分析:")
        print("-" * 80)
        for name, stats in summary["component_stats"].items():
            print(f"{name}:")
            print(f"  总耗时: {stats['total_duration']:.3f}s")
            print(f"  内存变化: {stats['total_memory_delta']:+.2f}MB")
            print(f"  操作数: {stats['operation_count']}")

            # 显示最耗时的操作
            operations = sorted(stats["operations"], key=lambda x: x["duration"], reverse=True)
            if operations:
                print(f"  最耗时操作: {operations[0]['operation']} ({operations[0]['duration']:.3f}s)")
            print()


# 全局性能分析器实例
performance_analyzer = PerformanceAnalyzer()


# 导出主要接口
__all__ = [
    'PerformanceAnalyzer',
    'PerformanceMetric',
    'performance_analyzer'
]