#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用请求处理器

处理通用请求相关的actions
"""

from typing import List, Dict, Any
from .base_action_handler import <PERSON><PERSON>ction<PERSON><PERSON><PERSON>, ActionContext, ActionResult


class GeneralRequestHandler(BaseActionHandler):
    """通用请求处理器"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 初始化action处理器映射表
        self._action_handlers = self._build_action_handlers()

    def _build_action_handlers(self) -> dict:
        """构建action处理器映射表"""
        return {
            "handle_general_request": self._handle_general_request,
            "provide_self_introduction": self._handle_self_introduction,
            "explain_capabilities": self._handle_explain_capabilities,
            "explain_feature_capabilities": self._handle_feature_capabilities,
            "enthusiastic_feature_explanation": self._handle_enthusiastic_feature_explanation,
            "explain_system_limitations": self._handle_system_limitations,
            "reassure_about_limitations": self._handle_reassure_limitations,
            "explain_integration_capabilities": self._handle_integration_capabilities,
            "explain_system_performance": self._handle_system_performance,
            "explain_general_capabilities": self._handle_general_capabilities,
            "provide_help_guidance": self._handle_help_guidance,
            "respond_to_general_chat": self._handle_general_chat,
            "show_empathy_and_clarify": self._handle_empathy_clarify,
            "acknowledge_and_redirect": self._handle_acknowledge_redirect,
            "handle_unknown_situation": self._handle_unknown_situation,
            "handle_low_confidence_question": self._handle_low_confidence_question,
            "handle_business_domain_query": self._handle_business_domain_query,
            "clarify_business_domain_query": self._handle_clarify_business_domain_query,
            "provide_emotional_support": self._handle_emotional_support,
            "clarify_intent": self._handle_clarify_intent,
            "provide_guidance_and_continue": self._handle_provide_guidance_and_continue,
        }

    @property
    def supported_actions(self) -> List[str]:
        """返回支持的action列表"""
        return list(self._action_handlers.keys())

    def register_action_handler(self, action: str, handler_func):
        """
        动态注册action处理器

        Args:
            action: action名称
            handler_func: 处理函数，必须是async函数，接受ActionContext参数，返回ActionResult
        """
        if not callable(handler_func):
            raise ValueError(f"处理器必须是可调用对象: {handler_func}")

        self._action_handlers[action] = handler_func
        self.logger.info(f"已注册action处理器: {action}")

    def unregister_action_handler(self, action: str) -> bool:
        """
        取消注册action处理器

        Args:
            action: action名称

        Returns:
            bool: 是否成功取消注册
        """
        if action in self._action_handlers:
            del self._action_handlers[action]
            self.logger.info(f"已取消注册action处理器: {action}")
            return True
        return False

    def get_action_handler_info(self) -> dict:
        """
        获取action处理器信息

        Returns:
            dict: 包含处理器信息的字典
        """
        return {
            "total_handlers": len(self._action_handlers),
            "supported_actions": list(self._action_handlers.keys()),
            "handler_methods": {
                action: handler.__name__ if hasattr(handler, '__name__') else str(handler)
                for action, handler in self._action_handlers.items()
            }
        }
    
    async def can_handle(self, action: str, context: ActionContext) -> bool:
        """检查是否可以处理指定的action"""
        return action in self._action_handlers

    async def handle(self, context: ActionContext) -> ActionResult:
        """
        处理通用请求相关的action

        使用策略模式，通过字典映射分发到具体的处理方法
        """
        if not await self.validate_context(context):
            return self.create_error_result("上下文验证失败")

        try:
            # 从映射表中获取处理器
            handler = self._action_handlers.get(context.action)

            if handler is None:
                return self.create_error_result(f"不支持的action: {context.action}")

            # 调用对应的处理器
            return await handler(context)

        except Exception as e:
            self.logger.error(f"处理action {context.action} 失败: {e}", exc_info=True)
            return self.create_error_result(str(e))
    
    async def _handle_general_request(self, context: ActionContext) -> ActionResult:
        """处理通用请求"""
        try:
            # 优先使用reply_manager获取回复
            if hasattr(self, 'reply_manager') and self.reply_manager:
                result = await self.reply_manager.get_reply(
                    reply_key="general_request",
                    context={
                        "user_message": context.message,
                        "session_id": context.session_id,
                        "decision_result": context.decision_result,
                        "history": context.history,
                        "action_command": context.action_command
                    }
                )
            else:
                # 回退到conversation_flow处理
                result = await self.conversation_flow.handle_general_request(
                    message=context.message,
                    session_id=context.session_id,
                    decision_result=context.decision_result,
                    history=context.history,
                    action_command=context.action_command
                )
            
            return self.create_success_result(
                content=result,
                action_type="general_request"
            )
            
        except Exception as e:
            self.logger.error(f"处理通用请求失败: {e}")
            return self.create_error_result(f"处理通用请求失败: {e}")

    def _get_fallback_reply(self, context: ActionContext) -> str:
        """获取通用请求的回退回复"""
        return "我理解您的请求，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。"
    
    async def _handle_self_introduction(self, context: ActionContext) -> ActionResult:
        """处理自我介绍"""
        try:
            # 直接使用reply_manager获取回复
            if hasattr(self, 'reply_manager') and self.reply_manager:
                result = await self.reply_manager.get_reply(
                    reply_key="self_introduction",
                    context={
                        "user_message": context.message,
                        "session_id": context.session_id,
                        "decision_result": context.decision_result
                    }
                )
            else:
                # 回退逻辑
                result = "您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？"
            
            return self.create_success_result(
                content=result,
                action_type="self_introduction"
            )
            
        except Exception as e:
            self.logger.error(f"处理自我介绍失败: {e}")
            return self.create_error_result(f"处理自我介绍失败: {e}")
    
    async def _handle_explain_capabilities(self, context: ActionContext) -> ActionResult:
        """处理能力解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="explain_capabilities"
            )
            
        except Exception as e:
            self.logger.error(f"处理能力解释失败: {e}")
            return self.create_error_result(f"处理能力解释失败: {e}")
    
    async def _handle_help_guidance(self, context: ActionContext) -> ActionResult:
        """处理帮助指导"""
        try:
            result = await self.conversation_flow.handle_help_guidance(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="help_guidance"
            )
            
        except Exception as e:
            self.logger.error(f"处理帮助指导失败: {e}")
            return self.create_error_result(f"处理帮助指导失败: {e}")
    
    async def _handle_general_chat(self, context: ActionContext) -> ActionResult:
        """处理一般聊天"""
        try:
            result = await self.conversation_flow.handle_general_chat(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="general_chat"
            )
            
        except Exception as e:
            self.logger.error(f"处理一般聊天失败: {e}")
            return self.create_error_result(f"处理一般聊天失败: {e}")

    async def _handle_feature_capabilities(self, context: ActionContext) -> ActionResult:
        """处理功能能力解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="feature_capabilities"
            )

        except Exception as e:
            self.logger.error(f"处理功能能力解释失败: {e}")
            return self.create_error_result(f"处理功能能力解释失败: {e}")

    async def _handle_enthusiastic_feature_explanation(self, context: ActionContext) -> ActionResult:
        """处理热情的功能解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="enthusiastic_feature_explanation"
            )

        except Exception as e:
            self.logger.error(f"处理热情功能解释失败: {e}")
            return self.create_error_result(f"处理热情功能解释失败: {e}")

    async def _handle_system_limitations(self, context: ActionContext) -> ActionResult:
        """处理系统限制解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="system_limitations"
            )

        except Exception as e:
            self.logger.error(f"处理系统限制解释失败: {e}")
            return self.create_error_result(f"处理系统限制解释失败: {e}")

    async def _handle_reassure_limitations(self, context: ActionContext) -> ActionResult:
        """处理安抚限制担忧"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="reassure_limitations"
            )

        except Exception as e:
            self.logger.error(f"处理安抚限制担忧失败: {e}")
            return self.create_error_result(f"处理安抚限制担忧失败: {e}")

    async def _handle_integration_capabilities(self, context: ActionContext) -> ActionResult:
        """处理集成能力解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="integration_capabilities"
            )

        except Exception as e:
            self.logger.error(f"处理集成能力解释失败: {e}")
            return self.create_error_result(f"处理集成能力解释失败: {e}")

    async def _handle_system_performance(self, context: ActionContext) -> ActionResult:
        """处理系统性能解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="system_performance"
            )

        except Exception as e:
            self.logger.error(f"处理系统性能解释失败: {e}")
            return self.create_error_result(f"处理系统性能解释失败: {e}")

    async def _handle_general_capabilities(self, context: ActionContext) -> ActionResult:
        """处理一般能力解释"""
        try:
            result = await self.conversation_flow.handle_explain_capabilities(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )

            return self.create_success_result(
                content=result,
                action_type="general_capabilities"
            )

        except Exception as e:
            self.logger.error(f"处理一般能力解释失败: {e}")
            return self.create_error_result(f"处理一般能力解释失败: {e}")
    
    async def _handle_empathy_clarify(self, context: ActionContext) -> ActionResult:
        """处理共情并澄清"""
        try:
            result = await self.conversation_flow.handle_show_empathy_and_clarify(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="empathy_clarify"
            )
            
        except Exception as e:
            self.logger.error(f"处理共情并澄清失败: {e}")
            return self.create_error_result(f"处理共情并澄清失败: {e}")
    
    async def _handle_acknowledge_redirect(self, context: ActionContext) -> ActionResult:
        """处理确认并重定向"""
        try:
            result = await self.conversation_flow.handle_acknowledge_and_redirect(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="acknowledge_redirect"
            )
            
        except Exception as e:
            self.logger.error(f"处理确认并重定向失败: {e}")
            return self.create_error_result(f"处理确认并重定向失败: {e}")
    
    async def _handle_unknown_situation(self, context: ActionContext) -> ActionResult:
        """处理未知情况"""
        try:
            result = await self.conversation_flow.handle_unknown_situation(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="unknown_situation"
            )
            
        except Exception as e:
            self.logger.error(f"处理未知情况失败: {e}")
            return self.create_error_result(f"处理未知情况失败: {e}")

    async def _handle_low_confidence_question(self, context: ActionContext) -> ActionResult:
        """处理低置信度问题"""
        try:
            # 使用决策结果中的prompt_instruction来生成回复
            prompt_instruction = context.decision_result.get('decision', {}).get('prompt_instruction')

            if prompt_instruction and hasattr(self.conversation_flow, 'reply_factory') and self.conversation_flow.reply_factory:
                # 使用动态回复生成器
                result = await self.conversation_flow.reply_factory.generate_dynamic_reply(
                    prompt_instruction=prompt_instruction,
                    user_message=context.message,
                    session_id=context.session_id
                )
            else:
                # 回退到默认回复
                result = "我理解您的问题，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。"

            return self.create_success_result(
                content=result,
                action_type="low_confidence_question"
            )

        except Exception as e:
            self.logger.error(f"处理低置信度问题失败: {e}")
            return self.create_error_result(f"处理低置信度问题失败: {e}")

    async def _generate_dynamic_clarification_reply(self, context: ActionContext) -> str:
        """
        生成动态澄清回复
        
        Args:
            context: Action上下文，包含用户消息、会话ID等信息
            
        Returns:
            str: 生成的动态澄清回复
        """
        try:
            # 检查是否有动态回复生成器
            if not hasattr(self.conversation_flow, 'reply_factory') or not self.conversation_flow.reply_factory:
                self.logger.debug("缺少reply_factory，无法生成动态澄清回复")
                return None

            # 格式化对话历史，以便在提示词中使用
            formatted_history = self._format_conversation_history(context.history)

            # 构建澄清提示词，使用更适合解释概念的结构化模板，并利用对话历史
            prompt_instruction = f"""
根据对话历史，用户询问"{context.message}"，这是对之前提到的某个概念的疑问。
对话历史：
{formatted_history}

请提供一个清晰、有帮助的澄清回复，遵循以下结构：

📋 **简单来说**：
用一两句话简单解释这个概念是什么。

🎯 **具体来说**：
分点详细说明这个概念的具体内容：
1. 方面一：具体解释和示例
2. 方面二：具体解释和示例
3. 方面三：具体解释和示例

💡 **在我们的服务中**：
说明这个概念在我们需求采集服务中如何应用：
- 可以帮助用户做什么
- 在什么场景下会用到
- 对用户有什么价值

✨ **举例说明**：
给出一个具体的例子，帮助用户更好理解。

请根据"{context.message}"这个问题，结合对话历史提供有针对性的回复。
特别注意：如果问题中包含"是什么意思"等词语，重点应该放在解释概念上，语言要亲切自然，避免过于官方正式。
"""

            # 使用动态回复生成器生成澄清回复
            reply = await self.conversation_flow.reply_factory.generate_clarification_reply(
                prompt_instruction=prompt_instruction,
                user_message=context.message,
                session_id=context.session_id
            )
            
            self.logger.info("成功生成动态澄清回复")
            return reply
            
        except Exception as e:
            self.logger.error(f"生成动态澄清回复失败: {e}", exc_info=True)
            return None

    def _format_conversation_history(self, history: List[Dict[str, Any]]) -> str:
        """
        格式化对话历史，以便在提示词中使用
        
        Args:
            history: 对话历史列表
            
        Returns:
            str: 格式化后的对话历史字符串
        """
        if not history:
            return "无对话历史"
        
        formatted_lines = []
        for msg in history[-6:]:  # 只取最近6条消息（3轮对话）
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            if role == 'user':
                formatted_lines.append(f"用户：{content}")
            elif role == 'assistant':
                formatted_lines.append(f"助手：{content}")
        
        return "\n".join(formatted_lines)

    async def _handle_business_domain_query(self, context: ActionContext) -> ActionResult:
        """处理业务领域查询"""
        try:
            # 检查是否有整合回复系统
            if hasattr(self.conversation_flow, 'integrated_reply_system'):
                result = await self.conversation_flow.integrated_reply_system.generate_reply(
                    message_type="business_domain_query",
                    user_message=context.message,
                    session_id=context.session_id
                )
            else:
                # 回退到默认的业务领域介绍
                result = """您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。

🏢 **由己平台核心业务**：
• 智能需求采集系统 - 通过AI对话收集和分析项目需求
• 在线用工平台 - 连接企业与全球优秀人才
• 专业服务匹配 - 涵盖IT开发、设计创意、运营推广等领域

💡 **我可以帮您**：
• 分析和整理项目需求
• 提供业务流程建议
• 解答平台使用问题
• 匹配合适的服务类型

请告诉我您的具体需求，我会为您提供针对性的业务建议！"""

            return self.create_success_result(
                content=result,
                action_type="business_domain_query"
            )

        except Exception as e:
            self.logger.error(f"处理业务领域查询失败: {e}")
            return self.create_error_result(f"处理业务领域查询失败: {e}")

    async def _generate_improved_static_clarification(self, context: ActionContext) -> str:
        """
        生成改进的静态澄清回复 - 通用引导性回复

        Args:
            context: Action上下文

        Returns:
            str: 通用的引导性澄清回复
        """
        try:
            # 根据用户情绪调整回复语调
            if context.emotion == "confused":
                opening = "我理解您可能感到有些困惑，让我来帮您理清思路。"
            elif context.emotion == "anxious":
                opening = "请不用担心，我来帮您整理一下想法。"
            elif context.emotion == "negative":
                opening = "我理解您现在可能感到有些困扰，让我来帮您找到合适的解决方案。"
            else:
                opening = "我想更好地理解您的需求，这样能为您提供更准确的帮助。"

            # 通用的引导性澄清回复，不包含具体示例
            return f"""{opening}

🤔 **请告诉我**：
• 您具体想了解什么？
• 或者您有什么想法需要我帮忙分析？

💡 **我可以协助您**：
• 整理和分析各种项目想法
• 提供技术实现方案建议
• 匹配合适的服务提供商
• 解答相关问题和疑虑

请用您自己的话描述一下，我会根据您的具体情况提供针对性的建议。"""

        except Exception as e:
            self.logger.error(f"生成改进静态澄清回复失败: {e}")
            return "我想更好地理解您的需求。请告诉我您具体想了解什么，或者有什么想法需要我帮忙分析？"

    async def _handle_clarify_business_domain_query(self, context: ActionContext) -> ActionResult:
        """处理业务领域查询澄清"""
        try:
            # 检查是否有整合回复系统
            if hasattr(self.conversation_flow, 'integrated_reply_system'):
                result = await self.conversation_flow.integrated_reply_system.generate_reply(
                    message_type="clarify_business_domain_query",
                    user_message=context.message,
                    session_id=context.session_id
                )
            else:
                # 回退到默认的澄清回复
                result = """我需要更好地理解您的业务需求。请问您是想了解：

🔍 **平台功能相关**：
1. 需求采集系统的使用方法
2. 项目发布和管理流程
3. 人才匹配和筛选机制

💼 **业务服务相关**：
1. 具体服务类型和报价
2. 项目执行流程和时间
3. 质量保障和售后服务

📋 **需求分析相关**：
1. 项目需求整理和分析
2. 技术方案建议和评估
3. 实施计划制定和优化

请选择您感兴趣的方面，或者直接描述您的具体问题，我会为您提供详细的解答。"""

            return self.create_success_result(
                content=result,
                action_type="clarify_business_domain_query"
            )

        except Exception as e:
            self.logger.error(f"处理业务领域查询澄清失败: {e}")
            return self.create_error_result(f"处理业务领域查询澄清失败: {e}")

    async def _handle_emotional_support(self, context: ActionContext) -> ActionResult:
        """处理情感支持请求"""
        try:
            self.logger.info(f"处理情感支持请求: {context.message[:50]}..., 用户情绪: {context.emotion}")

            # 根据用户情绪调整情感支持回复
            if context.emotion == "negative":
                opening = "我深深理解您现在的感受，情绪低落的时候确实让人感到沉重。请相信，这种感受是完全正常的，您并不孤单。"
                encouragement = "虽然现在感觉困难，但请记住您内心的坚强。"
            elif context.emotion == "anxious":
                opening = "我能感受到您内心的焦虑和不安，这种担忧的感觉让人很不舒服。请深呼吸，让我们一起面对这些担忧。"
                encouragement = "焦虑往往来自对未知的担心，但您有能力应对这些挑战。"
            elif context.emotion == "confused":
                opening = "我理解您现在可能感到迷茫和困惑，不知道该如何前进。这种感觉很多人都经历过，您不是一个人。"
                encouragement = "困惑是成长路上的必经之路，它意味着您正在思考和寻找答案。"
            else:
                opening = "我理解您现在的感受，每个人都会有情绪波动的时候，这是很正常的。"
                encouragement = "请相信，您有能力处理好这些情况。"

            # 构建情绪感知的情感支持回复
            result = f"""{opening}

💝 **请记住**：
- {encouragement}
- 困难和挫折都是暂时的，您有能力度过这个阶段
- 每一次挑战都是成长的机会
- 您并不孤单，总有人愿意倾听和帮助

🌟 **温和的建议**：
- 可以尝试做一些让自己感到平静的事情
- 和信任的朋友或家人聊聊，分享您的感受
- 适当的运动和休息也很有帮助
- 给自己一些时间和耐心

如果您有具体的工作或项目需求，我也很乐意为您提供专业的帮助。有时候专注于有意义的事情也能帮助我们重新找到方向。

请告诉我，有什么我可以为您做的吗？"""

            return self.create_success_result(
                content=result,
                action_type="provide_emotional_support"
            )

        except Exception as e:
            self.logger.error(f"处理情感支持失败: {e}", exc_info=True)
            return self.create_error_result(f"处理情感支持时发生错误: {str(e)}")

    async def _handle_clarify_intent(self, context: ActionContext) -> ActionResult:
        """处理意图澄清请求"""
        try:
            self.logger.info(f"处理意图澄清请求: {context.message[:50]}..., 用户情绪: {context.emotion}")

            # 🔧 尝试使用动态回复生成，提供更个性化的澄清回复
            dynamic_reply = await self._generate_dynamic_clarification_reply(context)
            if dynamic_reply:
                self.logger.info("使用动态生成的澄清回复")
                return self.create_success_result(
                    content=dynamic_reply,
                    action_type="clarify_intent"
                )

            # 回退到改进的静态回复
            self.logger.info("使用改进的静态澄清回复")
            result = await self._generate_improved_static_clarification(context)

            return self.create_success_result(
                content=result,
                action_type="clarify_intent"
            )

        except Exception as e:
            self.logger.error(f"处理意图澄清失败: {e}", exc_info=True)
            return self.create_error_result(f"处理意图澄清时发生错误: {str(e)}")

    async def _handle_provide_guidance_and_continue(self, context: ActionContext) -> ActionResult:
        """处理提供引导并继续流程"""
        try:
            self.logger.info(f"处理提供引导并继续: {context.message[:50]}..., 用户情绪: {context.emotion}")

            # 分析上下文和用户问题，生成针对性回复
            context_analysis = self._analyze_conversation_context(context)
            enhanced_prompt = self._build_enhanced_prompt_instruction(context, context_analysis)

            # 优先使用增强的动态回复生成
            if hasattr(self.conversation_flow, 'reply_factory') and self.conversation_flow.reply_factory:
                try:
                    # 使用增强的prompt_instruction进行动态回复生成
                    result = await self.conversation_flow.reply_factory.generate_dynamic_reply(
                        prompt_instruction=enhanced_prompt,
                        user_message=context.message,
                        session_id=context.session_id,
                        emotion=context.emotion
                    )
                    self.logger.info("使用动态回复生成器成功生成上下文感知回复")
                except Exception as e:
                    self.logger.warning(f"动态回复生成失败，使用智能回退逻辑: {e}")
                    result = self._generate_intelligent_fallback_reply(context, context_analysis)
            else:
                # 使用智能回退逻辑
                result = self._generate_intelligent_fallback_reply(context, context_analysis)

            # 从上下文中获取当前的领域和类别信息，确保数据传递的连续性
            domain_result = None
            category_result = None

            # 尝试从对话历史或上下文中获取最新的分类结果
            if hasattr(context, 'current_domain') and context.current_domain:
                domain_result = {
                    "domain_id": context.current_domain,
                    "confidence": 0.8,
                    "status": "completed",
                    "reasoning": "从会话上下文中获取的领域信息"
                }

            if hasattr(context, 'current_category') and context.current_category:
                category_result = {
                    "category_id": context.current_category,
                    "confidence": 0.8,
                    "status": "completed",
                    "reasoning": "从会话上下文中获取的类别信息"
                }

            return self.create_success_result(
                content=result,
                action_type="provide_guidance_and_continue",
                domain_result=domain_result,
                category_result=category_result
            )

        except Exception as e:
            self.logger.error(f"处理提供引导并继续失败: {e}", exc_info=True)
            return self.create_error_result(f"处理提供引导并继续时发生错误: {str(e)}")

    def _analyze_conversation_context(self, context: ActionContext) -> dict:
        """分析对话上下文，提取关键信息"""
        try:
            analysis = {
                "project_type": "未知项目",
                "collected_info": [],
                "current_focus": "需求收集",
                "domain": context.current_domain,
                "category": context.current_category,
                "conversation_stage": "初期"
            }

            # 从对话历史中提取项目信息
            history = context.history
            if history:
                # 分析最近的对话内容，提取项目类型和已收集信息
                recent_messages = history[-6:]  # 分析最近6条消息

                project_keywords = {
                    "app": "App应用",
                    "界面": "界面设计",
                    "设计": "设计项目",
                    "网站": "网站开发",
                    "系统": "系统开发",
                    "软件": "软件开发",
                    "小程序": "小程序开发"
                }

                collected_items = []
                for msg in recent_messages:
                    content = msg.get('content', '').lower()

                    # 识别项目类型
                    for keyword, project_type in project_keywords.items():
                        if keyword in content:
                            analysis["project_type"] = project_type
                            break

                    # 提取已收集的信息
                    if '时间' in content or '周期' in content:
                        collected_items.append("时间安排")
                    if '页面' in content or '功能' in content:
                        collected_items.append("功能范围")
                    if '风格' in content or '设计' in content:
                        collected_items.append("设计风格")

                analysis["collected_info"] = list(set(collected_items))

                # 判断对话阶段
                if len(collected_items) >= 2:
                    analysis["conversation_stage"] = "深入阶段"
                elif len(collected_items) >= 1:
                    analysis["conversation_stage"] = "进展阶段"

            self.logger.debug(f"上下文分析结果: {analysis}")
            return analysis

        except Exception as e:
            self.logger.error(f"上下文分析失败: {e}")
            return {"project_type": "项目", "collected_info": [], "current_focus": "需求收集"}

    def _build_enhanced_prompt_instruction(self, context: ActionContext, analysis: dict) -> str:
        """构建增强的prompt指令，结合上下文信息"""
        try:
            # 获取原始的prompt_instruction
            original_prompt = context.decision_result.get('decision', {}).get('prompt_instruction', '')

            # 构建上下文信息
            project_info = f"项目类型：{analysis['project_type']}"
            progress_info = f"已收集信息：{', '.join(analysis['collected_info']) if analysis['collected_info'] else '基础信息'}"

            # 构建增强的prompt指令
            enhanced_prompt = f"""
用户正在进行{analysis['project_type']}的需求收集，当前处于{analysis['conversation_stage']}。
{progress_info}

用户现在询问："{context.message}"

请你作为专业的需求分析师：
1. 针对用户的具体问题，结合项目背景提供专业、实用的回答
2. 回答要具体、有价值，避免空泛的表述
3. 回答后自然地引导用户确认或补充相关信息，继续完善需求
4. 保持专业而友好的语调

用户情绪状态：{context.emotion}
请根据用户情绪调整回复语调。
"""

            self.logger.debug(f"构建增强prompt: {enhanced_prompt[:100]}...")
            return enhanced_prompt

        except Exception as e:
            self.logger.error(f"构建增强prompt失败: {e}")
            return context.decision_result.get('decision', {}).get('prompt_instruction',
                                                                 '请回答用户问题并继续需求收集')

    def _generate_intelligent_fallback_reply(self, context: ActionContext, analysis: dict) -> str:
        """生成智能回退回复，基于上下文分析"""
        try:
            # 根据用户情绪调整开场语调
            if context.emotion == "confused":
                opening = f"我理解您对{analysis['project_type']}的疑问。"
            elif context.emotion == "anxious":
                opening = f"请不用担心，关于{analysis['project_type']}的问题我来为您详细解答。"
            else:
                opening = f"好的，关于{analysis['project_type']}的问题，"

            # 分析用户问题类型，提供针对性建议
            question_analysis = self._analyze_question_type(context.message)
            specific_answer = self._generate_specific_answer(context.message, analysis, question_analysis)

            # 构建完整回复
            result = f"""{opening}

{specific_answer}

📋 **接下来我们继续完善需求**：
{self._generate_next_step_guidance(analysis, question_analysis)}"""

            return result

        except Exception as e:
            self.logger.error(f"生成智能回退回复失败: {e}")
            return self._generate_basic_fallback_reply(context)

    def _analyze_question_type(self, question: str) -> dict:
        """分析用户问题类型"""
        question_lower = question.lower()

        question_types = {
            "recommendation": ["推荐", "建议", "选择", "哪个好", "什么样"],
            "explanation": ["是什么", "怎么", "如何", "为什么", "什么意思"],
            "comparison": ["区别", "对比", "比较", "差异", "优缺点"],
            "process": ["流程", "步骤", "过程", "怎么做"],
            "cost": ["价格", "费用", "成本", "多少钱"],
            "time": ["时间", "周期", "多久", "什么时候"]
        }

        detected_types = []
        for qtype, keywords in question_types.items():
            if any(keyword in question_lower for keyword in keywords):
                detected_types.append(qtype)

        return {
            "types": detected_types if detected_types else ["general"],
            "is_recommendation": "recommendation" in detected_types,
            "is_explanation": "explanation" in detected_types,
            "is_comparison": "comparison" in detected_types
        }

    def _generate_specific_answer(self, question: str, analysis: dict, question_analysis: dict) -> str:
        """根据问题类型和项目背景生成具体回答"""
        project_type = analysis.get("project_type", "项目")

        if question_analysis["is_recommendation"]:
            return self._generate_recommendation_answer(question, project_type, analysis)
        elif question_analysis["is_explanation"]:
            return self._generate_explanation_answer(question, project_type)
        elif question_analysis["is_comparison"]:
            return self._generate_comparison_answer(question, project_type)
        else:
            return f"💡 **关于您的问题**：\n针对{project_type}的具体情况，我需要了解更多细节才能给您最准确的建议。"

    def _generate_recommendation_answer(self, question: str, project_type: str, analysis: dict) -> str:
        """生成推荐类回答"""
        if "风格" in question:
            return f"""💡 **关于{project_type}的设计风格推荐**：

基于当前市场趋势和用户体验考虑，我推荐以下几种热门风格：

🎨 **简约现代风格**：干净的界面、充足的留白、清晰的层次结构
• 适合：商务类、工具类应用
• 优势：用户认知负担低，易于维护

🌟 **卡片式设计**：信息模块化展示，视觉层次分明
• 适合：内容展示类、社交类应用
• 优势：信息组织清晰，交互友好

🎯 **渐变色彩风格**：现代感强，视觉冲击力好
• 适合：年轻化、创新类产品
• 优势：品牌识别度高，视觉吸引力强

考虑到您的{project_type}特点，建议我们先确定目标用户群体，这样能更精准地选择合适的设计风格。"""

        elif "技术" in question or "方案" in question:
            return f"💡 **关于{project_type}的技术方案推荐**：\n我会根据您的具体需求、预算和时间安排，为您推荐最适合的技术方案。"

        else:
            return f"💡 **关于{project_type}的专业建议**：\n基于您目前的需求情况，我来为您提供一些实用的建议和推荐。"

    def _generate_explanation_answer(self, question: str, project_type: str) -> str:
        """生成解释类回答"""
        return f"💡 **关于您询问的概念**：\n我来为您详细解释这个概念在{project_type}中的具体含义和应用。"

    def _generate_comparison_answer(self, question: str, project_type: str) -> str:
        """生成对比类回答"""
        return f"💡 **关于您询问的对比分析**：\n我来为您分析不同方案在{project_type}中的优缺点和适用场景。"

    def _generate_next_step_guidance(self, analysis: dict, question_analysis: dict) -> str:
        """生成下一步引导"""
        if question_analysis["is_recommendation"] and "风格" in analysis.get("current_focus", ""):
            return "请告诉我您比较倾向于哪种风格？或者您有其他偏好的参考案例吗？"
        else:
            collected = analysis.get("collected_info", [])
            if len(collected) < 3:
                return "在解答您的问题后，我们继续完善其他需求细节，确保为您提供最合适的解决方案。"
            else:
                return "基于我们已经收集的信息，让我们进一步细化具体的实施方案。"

    def _generate_basic_fallback_reply(self, context: ActionContext) -> str:
        """生成基础回退回复"""
        if context.emotion == "confused":
            opening = "我理解您的疑问，让我来为您解答。"
        elif context.emotion == "anxious":
            opening = "请不用担心，我会详细为您解答这个问题。"
        else:
            opening = "好的，我来回答您的问题。"

        return f"""{opening}

💡 **关于您的问题**：
我会根据您的具体情况提供相应的解答和建议。

📋 **接下来我们继续**：
在解答您的问题后，我们会继续收集您的项目需求信息，确保能为您提供最合适的解决方案。

请告诉我您具体想了解什么？我会详细为您解答。"""
