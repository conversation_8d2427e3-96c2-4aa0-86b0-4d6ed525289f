"""
混合意图识别引擎 - 已废弃

⚠️ 警告：此文件已被废弃，请使用 unified_decision_engine.py 和 decision_engine_adapter.py

原功能：基于简化决策引擎的重构版本，提供与原版本兼容的接口，但内部使用新的简化决策引擎

迁移说明：
- 新系统使用 UnifiedDecisionEngine 替代此类
- 通过 DecisionEngineAdapter 提供向后兼容
- 建议更新代码使用新的统一决策引擎
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

from backend.agents.simplified_decision_engine import get_simplified_decision_engine
from backend.agents.unified_state_manager import get_state_manager, ConversationState
from backend.config.knowledge_base_config import get_knowledge_base_config_manager


@dataclass
class HybridIntentResult:
    """混合意图识别结果"""
    intent: str
    sub_intent: Optional[str]
    confidence: float
    emotion: str
    entities: Dict[str, Any]
    knowledge_base_query: bool = False
    processing_mode: str = "requirement_collection"


class HybridIntentRecognitionEngine:
    """
    混合意图识别引擎 - 重构版本
    
    基于新的简化决策引擎，提供与原版本兼容的接口
    """
    
    def __init__(self, llm_service: Any = None, **kwargs):
        """初始化混合意图识别引擎"""
        self.logger = logging.getLogger(__name__)
        self.decision_engine = get_simplified_decision_engine()
        self.state_manager = get_state_manager()
        self.llm_service = llm_service
        
        # 知识库配置
        try:
            self.kb_config_manager = get_knowledge_base_config_manager()
            self.knowledge_base_enabled = self.kb_config_manager.is_knowledge_base_enabled()
        except Exception as e:
            self.logger.warning(f"知识库配置加载失败: {e}")
            self.knowledge_base_enabled = False
        
        self.logger.info("混合意图识别引擎初始化完成（重构版本）")
    
    async def analyze(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        分析用户输入的意图
        
        Args:
            message: 用户输入消息
            context: 上下文信息
            
        Returns:
            意图分析结果
        """
        try:
            # 获取当前状态
            session_id = context.get("session_id", "default") if context else "default"
            user_id = context.get("user_id", "default") if context else "default"
            
            current_state = await self.state_manager.get_conversation_state(session_id, user_id)
            
            # 简单的意图识别逻辑
            intent = self._classify_intent(message)
            
            # 使用简化决策引擎做决策
            decision_result = self.decision_engine.make_decision(intent, current_state, context)
            
            # 构造兼容的返回结果
            result = {
                "intent": intent,
                "sub_intent": None,
                "confidence": 0.8,  # 固定置信度
                "emotion": "neutral",
                "entities": {},
                "recommended_action": decision_result.action,
                "knowledge_base_query": self._is_knowledge_base_query(message),
                "processing_mode": "requirement_collection"
            }
            
            self.logger.debug(f"意图分析结果: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"意图分析失败: {e}", exc_info=True)
            return {
                "intent": "unknown",
                "sub_intent": None,
                "confidence": 0.1,
                "emotion": "neutral", 
                "entities": {},
                "recommended_action": "handle_general_request",
                "knowledge_base_query": False,
                "processing_mode": "requirement_collection"
            }
    
    def _classify_intent(self, message: str) -> str:
        """简单的意图分类"""
        message_lower = message.lower()
        
        # 问候语
        if any(word in message_lower for word in ["你好", "hello", "hi", "您好"]):
            return "greeting"
        
        # 业务需求
        if any(word in message_lower for word in ["我想", "我需要", "要做", "想要", "希望"]):
            return "business_requirement"
        
        # 询问问题
        if any(word in message_lower for word in ["什么是", "如何", "怎么", "能否", "可以"]):
            return "ask_question"
        
        # 确认
        if any(word in message_lower for word in ["确认", "好的", "是的", "对", "没错"]):
            return "confirm"
        
        # 重新开始
        if any(word in message_lower for word in ["重新开始", "重来", "全部重来"]):
            return "restart"
        
        # 修改
        if any(word in message_lower for word in ["修改", "改", "更改", "调整"]):
            return "modify"
        
        return "general_request"
    
    def _is_knowledge_base_query(self, message: str) -> bool:
        """判断是否为知识库查询"""
        if not self.knowledge_base_enabled:
            return False
        
        # 简单的知识库查询判断
        query_indicators = ["查询", "搜索", "找", "什么是", "如何", "怎么"]
        return any(indicator in message for indicator in query_indicators)
