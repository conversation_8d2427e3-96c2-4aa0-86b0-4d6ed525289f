
"""
决策引擎模块 - 已废弃

⚠️ 警告：此文件已被废弃，请使用 unified_decision_engine.py 和 decision_engine_adapter.py

原功能：根据用户意图、情感状态和对话上下文选择最佳响应策略。
从外部YAML文件加载策略，并采用级联回退逻辑进行决策。

迁移说明：
- 新系统使用 UnifiedDecisionEngine 替代此类
- 通过 DecisionEngineAdapter 提供向后兼容
- 建议更新代码使用新的统一决策引擎
"""

import yaml
import logging
from typing import Dict, Any, Optional
from backend.config.unified_config_loader import UnifiedConfigLoader

class DecisionEngine:

    def __init__(self, strategies_path: str = None):
        """
        初始化决策引擎并从统一配置文件加载策略。
        """
        self.logger = logging.getLogger(__name__)
        self.strategies_config = {}
        self.DEFAULT_STRATEGY = {
            "action": "handle_unknown_situation",
            "priority": 0,
            "prompt_instruction": "保持中性、专业的语气进行回应。"
        }

        try:
            # 使用统一配置加载器
            config_loader = UnifiedConfigLoader()

            # 从统一配置中获取策略配置
            self.strategies_config = config_loader.get_config_section('strategies')

            # 如果统一配置中没有详细的策略配置，使用默认的简化策略
            if not self.strategies_config or len(self.strategies_config) <= 4:  # 只有基本配置项
                self.strategies_config = self._get_default_strategies()
                self.logger.info("使用内置的默认策略配置。")
            else:
                self.logger.info("成功从统一配置文件加载决策策略。")

            # 设置默认策略
            self.DEFAULT_STRATEGY = self.strategies_config.get('DEFAULT_STRATEGY', self.DEFAULT_STRATEGY)

        except Exception as e:
            self.logger.error(f"加载策略配置时出错: {e}，将使用内置的默认策略。")
            self.strategies_config = self._get_default_strategies()

    def get_strategy(self, intent: str, emotion: str, context: Optional[Dict[str, Any]] = None, sub_intent: Optional[str] = None, message: str = "") -> Dict[str, Any]:
        """
        获取最适合当前对话状态的策略，采用级联回退逻辑。

        查找顺序（支持子意图）:
        1. 状态特定子意图策略: (state, intent, sub_intent, emotion)
        2. 状态特定子意图回退至neutral: (state, intent, sub_intent, 'neutral')
        3. 状态特定策略: (state, intent, emotion)
        4. 状态特定回退至neutral情感: (state, intent, 'neutral')
        5. 全局子意图策略: (GLOBAL, intent, sub_intent, emotion)
        6. 全局子意图回退至neutral: (GLOBAL, intent, sub_intent, 'neutral')
        7. 全局策略: (GLOBAL, intent, emotion)
        8. 全局回退至neutral情感: (GLOBAL, intent, 'neutral')
        9. 最终默认策略
        """
        context = context or {}

        # 提取状态信息，支持字符串和字典格式
        state = self._extract_state_from_context(context)

        # 🔥 添加调试日志：记录接收到的上下文信息
        self.logger.info(f"[决策引擎] 接收到的上下文: {context}")
        self.logger.info(f"[决策引擎] 当前状态: {state}, 意图: {intent}, 情感: {emotion}, 子意图: {sub_intent}")

        # 检查是否有简化逻辑配置
        state_config = self.strategies_config.get(state, {}).get("_state_config", {})
        if state_config.get("use_simplified_logic", False):
            return self._get_simplified_strategy(state, intent, emotion, state_config, sub_intent, message)

        # 构建查找键列表，避免重复查找
        keys_to_try = []

        # 如果有子意图，优先查找子意图策略
        if sub_intent:
            # 1. 状态特定子意图策略
            keys_to_try.append((state, intent, sub_intent, emotion))
            if emotion != 'neutral':
                keys_to_try.append((state, intent, sub_intent, 'neutral'))

        # 2. 状态特定策略（无子意图或子意图查找失败时的回退）
        keys_to_try.append((state, intent, emotion))
        if emotion != 'neutral':
            keys_to_try.append((state, intent, 'neutral'))

        # 如果有子意图，查找全局子意图策略
        if sub_intent:
            # 3. 全局子意图策略
            keys_to_try.append(("GLOBAL", intent, sub_intent, emotion))
            if emotion != 'neutral':
                keys_to_try.append(("GLOBAL", intent, sub_intent, 'neutral'))

        # 4. 全局策略
        keys_to_try.append(("GLOBAL", intent, emotion))
        if emotion != 'neutral':
            keys_to_try.append(("GLOBAL", intent, 'neutral'))

        for key in keys_to_try:
            try:
                strategy = None
                if len(key) == 4:  # 包含子意图的查找: (state, intent, sub_intent, emotion)
                    s, i, si, e = key
                    strategy = self.strategies_config.get(s, {}).get(i, {}).get(si, {}).get(e)
                    self.logger.info(f"策略查找: ({s}, {i}, {si}, {e}) -> {'找到' if strategy else '未找到'}")
                elif len(key) == 3:  # 传统查找: (state, intent, emotion)
                    s, i, e = key
                    strategy = self.strategies_config.get(s, {}).get(i, {}).get(e)
                    self.logger.info(f"策略查找: ({s}, {i}, {e}) -> {'找到' if strategy else '未找到'}")

                if strategy:
                    self.logger.info(f"策略匹配成功: {key} -> action: {strategy.get('action')}")
                    return strategy
            except (AttributeError, TypeError): # 如果配置的层级不存在，跳过
                self.logger.info(f"策略查找异常: {key} -> AttributeError/TypeError")
                continue
        
        self.logger.debug(f"所有查找均未命中，返回默认策略。")
        return self.DEFAULT_STRATEGY

    def _extract_state_from_context(self, context: Dict[str, Any]) -> str:
        """
        从上下文中提取状态信息，支持字符串和字典格式

        Args:
            context: 上下文字典

        Returns:
            str: 状态字符串
        """
        current_state = context.get("current_state", "GLOBAL")

        # 如果current_state是字典（来自hybrid_conversation_router），提取mode字段
        if isinstance(current_state, dict):
            mode = current_state.get("mode", "unknown")
            actual_state = current_state.get("actual_conversation_state")
            self.logger.info(f"[决策引擎] 从字典状态中提取模式: {mode}, 实际状态: {actual_state}")

            # 🔥 关键修复：优先使用实际的会话状态
            if actual_state:
                self.logger.info(f"[决策引擎] 使用实际会话状态: {actual_state}")
                return actual_state

            # 将模式映射到策略状态（回退逻辑）
            mode_to_state_mapping = {
                "knowledge_base": "GLOBAL",  # 知识库查询使用全局策略
                "requirement_collection": "COLLECTING_INFO",  # 需求采集映射到信息收集状态
                "clarification": "GLOBAL",  # 澄清使用全局策略
                "unknown": "GLOBAL"
            }

            mapped_state = mode_to_state_mapping.get(mode, "GLOBAL")
            self.logger.info(f"[决策引擎] 模式映射: {mode} -> {mapped_state}")
            return mapped_state

        # 如果是字符串，直接返回
        elif isinstance(current_state, str):
            return current_state

        # 其他情况返回默认值
        else:
            self.logger.warning(f"[决策引擎] 未知的状态格式: {type(current_state)}, 使用默认状态 GLOBAL")
            return "GLOBAL"

    def _get_simplified_strategy(self, state: str, intent: str, emotion: str, state_config: Dict[str, Any], sub_intent: Optional[str] = None, message: str = "") -> Dict[str, Any]:
        """
        处理简化逻辑状态的策略获取

        Args:
            state: 当前状态
            intent: 用户意图
            emotion: 情感
            state_config: 状态配置
            sub_intent: 子意图
            message: 用户原始消息（用于关键词匹配）

        Returns:
            Dict[str, Any]: 策略配置
        """
        # 获取优先级顺序，如果没有配置则使用默认顺序
        priority_order = state_config.get("priority_order", ["confirm", "restart", "modify"])
        fallback_action = state_config.get("fallback_action", "execute_document_modification")
        fallback_intent = state_config.get("fallback_intent", "modify")

        self.logger.info(f"使用简化逻辑处理状态 {state}，意图: {intent}，优先级顺序: {priority_order}")

        # 在DOCUMENTING状态下，进行关键词匹配以纠正意图识别错误
        if state == "DOCUMENTING" and message:
            corrected_intent = self._check_keyword_intent_correction(message, intent)
            if corrected_intent != intent:
                self.logger.info(f"关键词匹配纠正意图: {intent} -> {corrected_intent}")
                intent = corrected_intent

        # 按照优先级顺序检查意图
        for priority_intent in priority_order:
            if intent == priority_intent:
                # 尝试找到对应的策略
                strategy = self._find_strategy_for_intent(state, intent, emotion, sub_intent)
                if strategy:
                    self.logger.info(f"简化逻辑找到策略: {intent} -> {strategy.get('action')}")
                    return strategy

        # 如果没有找到匹配的意图，使用fallback
        self.logger.info(f"简化逻辑使用fallback: {intent} -> {fallback_action}")
        return {
            "action": fallback_action,
            "priority": 1,
            "prompt_instruction": f"用户在文档审查过程中提出了修改请求。请处理用户的修改需求。"
        }

    def _find_strategy_for_intent(self, state: str, intent: str, emotion: str, sub_intent: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        为特定意图查找策略
        """
        keys_to_try = []

        # 如果有子意图，优先查找子意图策略
        if sub_intent:
            keys_to_try.append((state, intent, sub_intent, emotion))
            if emotion != 'neutral':
                keys_to_try.append((state, intent, sub_intent, 'neutral'))

        # 查找普通策略
        keys_to_try.append((state, intent, emotion))
        if emotion != 'neutral':
            keys_to_try.append((state, intent, 'neutral'))

        for key in keys_to_try:
            try:
                strategy = None
                if len(key) == 4:  # 包含子意图的查找
                    s, i, si, e = key
                    strategy = self.strategies_config.get(s, {}).get(i, {}).get(si, {}).get(e)
                elif len(key) == 3:  # 传统查找
                    s, i, e = key
                    strategy = self.strategies_config.get(s, {}).get(i, {}).get(e)

                if strategy:
                    return strategy
            except (AttributeError, TypeError):
                continue

        return None

    def _check_keyword_intent_correction(self, message: str, original_intent: str) -> str:
        """
        基于关键词匹配纠正意图识别错误

        Args:
            message: 用户原始消息
            original_intent: LLM识别的原始意图

        Returns:
            str: 纠正后的意图
        """
        if not message:
            return original_intent

        # 导入配置管理器
        from backend.config.unified_config_loader import get_unified_config

        # 检查新聊天关键词
        new_chat_patterns = get_unified_config().get_business_rule("conversation.keyword_acceleration.rules.new_chat.keywords", [])
        text_lower = message.lower().strip()

        for pattern in new_chat_patterns:
            if pattern in text_lower:
                self.logger.info(f"关键词匹配检测到新聊天请求: '{pattern}' in '{message}'")
                return "restart"

        # 可以在这里添加其他关键词匹配逻辑
        # 比如确认关键词、修改关键词等

        return original_intent

    def get_prompt_instruction(self, intent: str, emotion: str, context: Optional[Dict[str, Any]] = None, sub_intent: Optional[str] = None) -> str:
        """
        辅助函数，直接获取最终策略中的 prompt_instruction。
        """
        strategy = self.get_strategy(intent, emotion, context, sub_intent)
        return strategy["prompt_instruction"]

    def _get_default_strategies(self) -> Dict[str, Any]:
        """
        获取默认的策略配置，当统一配置文件不可用时使用
        """
        return {
            "DEFAULT_STRATEGY": {
                "action": "handle_unknown_situation",
                "priority": 0,
                "prompt_instruction": "保持中性、专业的语气进行回应。"
            },
            "GLOBAL": {
                "greeting": {
                    "neutral": {
                        "action": "respond_with_greeting",
                        "priority": 1,
                        "prompt_instruction": "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。"
                    }
                },
                "ask_question": {
                    "neutral": {
                        "action": "handle_general_question",
                        "priority": 3,
                        "prompt_instruction": "用户提出了问题，请分析并提供合适的回应。"
                    }
                },
                "business_requirement": {
                    "neutral": {
                        "action": "start_requirement_gathering",
                        "priority": 5,
                        "prompt_instruction": "用户提出业务需求，开始系统性收集信息。"
                    }
                },
                "provide_information": {
                    "neutral": {
                        "action": "acknowledge_and_redirect",
                        "priority": 2,
                        "prompt_instruction": "用户提供信息，确认收到并引导下一步。"
                    }
                },
                "confirm": {
                    "neutral": {
                        "action": "process_confirmation",
                        "priority": 3,
                        "prompt_instruction": "用户确认信息，处理确认并继续流程。"
                    }
                },
                "unknown": {
                    "neutral": {
                        "action": "request_clarification",
                        "priority": 5,
                        "prompt_instruction": "无法理解用户意图，礼貌地请求重新描述。"
                    }
                }
            },
            "IDLE": {
                "business_requirement": {
                    "neutral": {
                        "action": "start_requirement_gathering",
                        "priority": 6,
                        "prompt_instruction": "用户提出需求，开始系统性收集信息。"
                    }
                },
                "greeting": {
                    "neutral": {
                        "action": "welcome_and_introduce",
                        "priority": 5,
                        "prompt_instruction": "欢迎用户并介绍需求收集流程。"
                    }
                }
            },
            "COLLECTING_INFO": {
                "provide_information": {
                    "neutral": {
                        "action": "process_answer_and_ask_next",
                        "priority": 5,
                        "prompt_instruction": "处理用户提供的信息，继续收集下一个关注点。"
                    }
                },
                "confirm": {
                    "neutral": {
                        "action": "start_document_generation",
                        "priority": 6,
                        "prompt_instruction": "用户确认信息收集完成，开始生成文档。"
                    }
                }
            },
            "DOCUMENTING": {
                "confirm": {
                    "neutral": {
                        "action": "finalize_and_reset",
                        "priority": 6,
                        "prompt_instruction": "用户确认文档，完成流程并重置状态。"
                    }
                },
                "modify": {
                    "neutral": {
                        "action": "execute_document_modification",
                        "priority": 7,
                        "prompt_instruction": "用户要求修改文档，执行修改操作。"
                    }
                },
                "restart": {
                    "neutral": {
                        "action": "restart_conversation",
                        "priority": 8,
                        "prompt_instruction": "用户要求重新开始，重置所有状态。"
                    }
                }
            }
        }
