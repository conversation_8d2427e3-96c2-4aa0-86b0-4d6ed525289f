"""
混合对话路由器

根据意图识别结果路由到相应的处理器，管理知识库问答和需求采集之间的切换，
维护对话上下文和状态管理。
"""

import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from backend.config.knowledge_base_config import get_knowledge_base_config_manager
from backend.agents.rag_knowledge_base_agent import RAGKnowledgeBaseAgent
from backend.agents.simplified_decision_engine import get_simplified_decision_engine


class ConversationMode(Enum):
    """对话模式枚举"""
    KNOWLEDGE_BASE = "knowledge_base"
    REQUIREMENT_COLLECTION = "requirement_collection"
    CLARIFICATION = "clarification"
    UNKNOWN = "unknown"


@dataclass
class RoutingResult:
    """路由结果"""
    success: bool
    content: str
    mode: ConversationMode
    context_updates: Dict[str, Any]
    processing_info: Dict[str, Any]
    error: Optional[str] = None


class HybridConversationRouter:
    """
    混合对话路由器

    职责：
    - 根据意图识别结果路由到相应的处理器
    - 管理知识库问答和需求采集之间的切换
    - 维护对话上下文和状态管理
    - 处理模式切换和状态保持
    """

    def __init__(self, rag_agent: RAGKnowledgeBaseAgent,
                 requirement_flow, intent_engine=None):
        """
        初始化混合对话路由器

        Args:
            rag_agent: RAG知识库代理
            requirement_flow: 需求采集流程处理器
            intent_engine: 混合意图识别引擎
        """
        self.logger = logging.getLogger(__name__)
        self.rag_agent = rag_agent
        self.requirement_flow = requirement_flow
        self.intent_engine = intent_engine
        self.kb_config_manager = get_knowledge_base_config_manager()

        # 路由统计
        self.routing_stats = {
            "total_requests": 0,
            "knowledge_base_requests": 0,
            "requirement_requests": 0,
            "clarification_requests": 0,
            "mode_switches": 0
        }

    async def route_message(self, message: str, session_context: Dict[str, Any]) -> RoutingResult:
        """
        路由消息到相应的处理器

        Args:
            message: 用户输入消息
            session_context: 会话上下文信息

        Returns:
            RoutingResult: 路由处理结果
        """
        start_time = time.time()
        self.routing_stats["total_requests"] += 1

        try:
            self.logger.info(f"开始路由消息: {message[:50]}...")

            # 1. 意图识别
            # SimplifiedDecisionEngine期望context是一个列表格式
            context_list = [session_context] if isinstance(session_context, dict) else session_context
            intent_result = await self.intent_engine.analyze(message, context_list)

            # 2. 检查是否需要模式切换
            current_mode = session_context.get("current_mode", ConversationMode.UNKNOWN.value)
            target_mode = self._determine_target_mode(intent_result)

            if current_mode != target_mode.value:
                self.logger.info(f"检测到模式切换: {current_mode} -> {target_mode.value}")
                self.routing_stats["mode_switches"] += 1

                # 处理模式切换
                switch_result = await self._handle_mode_switch(
                    current_mode, target_mode, session_context, message
                )
                if switch_result:
                    session_context["current_mode"] = target_mode.value

            # 3. 根据目标模式路由消息
            routing_result = await self._route_to_handler(
                target_mode, message, session_context, intent_result
            )

            # 4. 更新上下文
            routing_result.context_updates["current_mode"] = target_mode.value
            routing_result.context_updates["last_intent"] = intent_result

            # 5. 记录处理信息
            processing_time = time.time() - start_time
            routing_result.processing_info.update({
                "routing_time": processing_time,
                "intent_result": intent_result,
                "mode_switch": current_mode != target_mode.value,
                "target_mode": target_mode.value
            })

            self.logger.info(f"消息路由完成，模式: {target_mode.value}, 耗时: {processing_time:.3f}s")
            return routing_result

        except Exception as e:
            self.logger.error(f"消息路由失败: {e}", exc_info=True)
            return RoutingResult(
                success=False,
                content="抱歉，处理您的请求时出现了问题，请稍后再试。",
                mode=ConversationMode.UNKNOWN,
                context_updates={},
                processing_info={"error": str(e), "routing_time": time.time() - start_time},
                error=str(e)
            )

    def _determine_target_mode(self, intent_result: Dict[str, Any]) -> ConversationMode:
        """
        根据意图识别结果确定目标模式

        Args:
            intent_result: 意图识别结果

        Returns:
            ConversationMode: 目标对话模式
        """
        try:
            # 检查是否为知识库查询
            if intent_result.get("knowledge_base_query", False):
                self.logger.info("基于knowledge_base_query标志路由到知识库模式")
                return ConversationMode.KNOWLEDGE_BASE

            # 检查原有的意图识别结果
            decision = intent_result.get("decision", {})
            action = decision.get("action", "")

            self.logger.debug(f"分析动作映射: action='{action}', decision={decision}")

            # 知识库查询相关的action
            knowledge_base_actions = [
                "handle_domain_knowledge_question",
                "clarify_domain_knowledge_question",
                "provide_knowledge_base_answer",
                "search_knowledge_base",
                "answer_question",
                "handle_business_domain_query",
                "handle_technical_domain_query",
                "reassure_technical_domain_query",
                "handle_industry_standard_query",
                "handle_best_practice_query",
                "handle_domain_query",
                "clarify_domain_query",
                "handle_workflow_query",
                "reassure_workflow_query",
                "handle_methodology_query",
                "handle_step_sequence_query",
                "handle_milestone_query",
                "handle_process_query",
                "clarify_process_query",
                "explain_feature_capabilities",
                "enthusiastic_feature_explanation",
                "explain_system_limitations",
                "reassure_about_limitations",
                "explain_integration_capabilities",
                "explain_system_performance",
                "explain_general_capabilities",
                "handle_requirement_question",
                "handle_anxious_requirement_question",
                "handle_technical_question",
                "handle_confused_technical_question",
                "handle_scope_question",
                "reassure_scope_question",
                "handle_timeline_question",
                "handle_cost_question"
            ]

            if action in knowledge_base_actions:
                self.logger.info(f"基于动作 '{action}' 路由到知识库模式")
                return ConversationMode.KNOWLEDGE_BASE

            # 需求采集相关的action
            requirement_actions = [
                "start_requirement_gathering",
                "continue_requirement_gathering",
                "domain_classification",
                "category_selection",
                "focus_point_tracking",
                "start_focused_requirement_gathering",
                "start_gentle_requirement_gathering",
                "start_guided_requirement_gathering",
                "process_answer",
                "reassure_and_process_answer",
                "process_supplemental_info",
                "appreciate_supplemental_info",
                "process_correction",
                "handle_frustrated_correction",
                "process_detailed_info",
                "clarify_detailed_info",
                "process_example",
                "appreciate_example",
                "process_preference",
                "reassure_about_preference",
                "process_answer_and_ask_next",
                "clarify_and_process_answer",
                "provide_suggestions",
                "provide_reassuring_guidance",
                "provide_step_by_step_guidance",
                "skip_question_and_ask_next",
                "execute_document_modification",
                "finalize_and_reset",
                "restart_conversation",
                "reset_conversation",
                "apologize_and_request_refinement"
            ]

            if action in requirement_actions:
                self.logger.info(f"基于动作 '{action}' 路由到需求采集模式")
                return ConversationMode.REQUIREMENT_COLLECTION

            # 澄清相关的action
            clarification_actions = [
                "show_empathy_and_clarify",
                "ask_for_clarification",
                "respond_to_general_chat",
                "handle_low_confidence_question",
                "handle_anxious_question",
                "handle_confused_question",
                "handle_unknown_situation",
                "request_clarification",
                "gentle_clarification_request",
                "supportive_clarification_request",
                "explain_terminology",
                "simplify_terminology_explanation",
                "clarify_question_meaning",
                "reassure_and_clarify_question",
                "explain_process_steps",
                "explain_option_differences",
                "explain_choice_implications",
                "reassure_about_implications",
                "provide_clarification",
                "reassuring_clarification",
                "step_by_step_clarification",
                "respond_with_greeting",
                "respond_with_reassuring_greeting",
                "provide_self_introduction",
                "acknowledge_positive_feedback",
                "handle_negative_feedback",
                "handle_suggestion_feedback",
                "appreciate_suggestion_feedback",
                "handle_correction_feedback",
                "handle_general_feedback",
                "acknowledge_and_redirect",
                "acknowledge_and_reassure",
                "clarify_and_guide"
            ]

            if action in clarification_actions:
                self.logger.info(f"基于动作 '{action}' 路由到澄清模式")
                return ConversationMode.CLARIFICATION

            # 默认情况下，如果不确定，优先考虑澄清模式而不是需求采集
            self.logger.warning(f"未知动作 '{action}'，使用默认澄清模式")
            return ConversationMode.CLARIFICATION

        except Exception as e:
            self.logger.error(f"确定目标模式失败: {e}")
            return ConversationMode.UNKNOWN

    async def _handle_mode_switch(self, from_mode: str, to_mode: ConversationMode,
                                 context: Dict[str, Any], message: str) -> bool:
        """
        处理模式切换

        Args:
            from_mode: 源模式
            to_mode: 目标模式
            context: 会话上下文
            message: 用户消息

        Returns:
            bool: 切换是否成功
        """
        try:
            self.logger.info(f"处理模式切换: {from_mode} -> {to_mode.value}")

            # 保存当前模式的状态
            if from_mode != ConversationMode.UNKNOWN.value:
                context[f"{from_mode}_state"] = context.get("current_state", {})

            # 根据目标模式初始化状态
            if to_mode == ConversationMode.KNOWLEDGE_BASE:
                context["current_state"] = {
                    "mode": to_mode.value,
                    "query_history": context.get("knowledge_base_state", {}).get("query_history", []),
                    "last_sources": []
                }
            elif to_mode == ConversationMode.REQUIREMENT_COLLECTION:
                context["current_state"] = {
                    "mode": to_mode.value,
                    "collection_phase": context.get("requirement_collection_state", {}).get("collection_phase", "initial"),
                    "domain": None,
                    "category": None,
                    "focus_points": []
                }
            elif to_mode == ConversationMode.CLARIFICATION:
                context["current_state"] = {
                    "mode": to_mode.value,
                    "clarification_attempts": 0,
                    "unclear_aspects": []
                }

            # 记录切换历史
            switch_history = context.get("mode_switch_history", [])
            switch_history.append({
                "from": from_mode,
                "to": to_mode.value,
                "timestamp": time.time(),
                "trigger_message": message[:100]
            })
            context["mode_switch_history"] = switch_history[-10:]  # 保留最近10次切换

            return True

        except Exception as e:
            self.logger.error(f"模式切换失败: {e}", exc_info=True)
            return False

    async def _route_to_handler(self, mode: ConversationMode, message: str,
                               context: Dict[str, Any], intent_result: Dict[str, Any]) -> RoutingResult:
        """
        根据模式路由到相应的处理器

        Args:
            mode: 对话模式
            message: 用户消息
            context: 会话上下文
            intent_result: 意图识别结果

        Returns:
            RoutingResult: 处理结果
        """
        try:
            if mode == ConversationMode.KNOWLEDGE_BASE:
                return await self._handle_knowledge_base_query(message, context, intent_result)
            elif mode == ConversationMode.REQUIREMENT_COLLECTION:
                return await self._handle_requirement_collection(message, context, intent_result)
            elif mode == ConversationMode.CLARIFICATION:
                return await self._handle_clarification(message, context, intent_result)
            else:
                return await self._handle_unknown_mode(message, context, intent_result)

        except Exception as e:
            self.logger.error(f"路由处理失败: {e}", exc_info=True)
            return RoutingResult(
                success=False,
                content="处理请求时出现错误，请稍后再试。",
                mode=mode,
                context_updates={},
                processing_info={"error": str(e)},
                error=str(e)
            )

    async def _handle_knowledge_base_query(self, message: str, context: Dict[str, Any],
                                          intent_result: Dict[str, Any]) -> RoutingResult:
        """
        处理知识库查询

        Args:
            message: 用户消息
            context: 会话上下文
            intent_result: 意图识别结果

        Returns:
            RoutingResult: 处理结果
        """
        self.routing_stats["knowledge_base_requests"] += 1

        try:
            # 检查知识库功能是否启用
            if not self.kb_get_unified_config().is_knowledge_base_enabled():
                self.logger.warning("知识库功能未启用，回退到需求采集模式")
                return await self._handle_requirement_collection(message, context, intent_result)

            # 构建查询上下文
            query_context = {
                "session_id": context.get("session_id"),
                "user_id": context.get("user_id"),
                "current_mode": ConversationMode.KNOWLEDGE_BASE.value,
                "role_filter": intent_result.get("role_filter"),
                "conversation_history": context.get("conversation_history", [])
            }

            # 调用RAG代理
            rag_result = await self.rag_agent.query(message, query_context)

            if rag_result.success:
                # 更新查询历史
                query_history = context.get("current_state", {}).get("query_history", [])
                query_history.append({
                    "query": message,
                    "timestamp": time.time(),
                    "sources_count": len(rag_result.sources)
                })

                return RoutingResult(
                    success=True,
                    content=rag_result.answer,
                    mode=ConversationMode.KNOWLEDGE_BASE,
                    context_updates={
                        "last_sources": rag_result.sources,
                        "query_history": query_history[-20:]  # 保留最近20次查询
                    },
                    processing_info={
                        "rag_processing_info": rag_result.processing_info,
                        "sources_count": len(rag_result.sources)
                    }
                )
            else:
                # RAG查询失败，检查是否为自我介绍请求
                if self._is_self_introduction_request(message):
                    self.logger.info("知识库查询失败，但检测到自我介绍请求，提供自我介绍")
                    fallback_content = "您好！我是由己AI助手，专门帮助您进行项目需求分析和整理。\n\n我的主要功能包括：\n• 📋 需求收集和分析\n• 💡 项目建议和指导\n• 🔍 信息查询和解答\n• 📝 文档生成和整理\n\n请告诉我您的项目需求，我来帮您详细分析！"

                    return RoutingResult(
                        success=True,
                        content=fallback_content,
                        mode=ConversationMode.CLARIFICATION,
                        context_updates={
                            "fallback_reason": "self_introduction_provided",
                            "original_query": message
                        },
                        processing_info={
                            "self_introduction_triggered": True,
                            "fallback_from_kb": True
                        }
                    )
                else:
                    # 其他情况的通用回退
                    fallback_content = f"抱歉，我在知识库中没有找到相关信息。\n\n您可以：\n1. 尝试用不同的关键词重新提问\n2. 或者我可以帮您收集具体需求来提供定制化建议\n\n请告诉我您希望如何继续？"

                    return RoutingResult(
                        success=True,
                        content=fallback_content,
                        mode=ConversationMode.CLARIFICATION,
                        context_updates={
                            "fallback_reason": "knowledge_base_no_results",
                            "original_query": message
                        },
                        processing_info={
                            "rag_error": rag_result.error,
                            "fallback_triggered": True
                        }
                    )

        except Exception as e:
            self.logger.error(f"知识库查询处理失败: {e}", exc_info=True)
            return RoutingResult(
                success=False,
                content="知识库查询出现问题，让我为您转到需求采集模式。",
                mode=ConversationMode.REQUIREMENT_COLLECTION,
                context_updates={"fallback_reason": "knowledge_base_error"},
                processing_info={"error": str(e)},
                error=str(e)
            )

    async def _handle_requirement_collection(self, message: str, context: Dict[str, Any],
                                           intent_result: Dict[str, Any]) -> RoutingResult:
        """
        处理需求采集

        Args:
            message: 用户消息
            context: 会话上下文
            intent_result: 意图识别结果

        Returns:
            RoutingResult: 处理结果
        """
        self.routing_stats["requirement_requests"] += 1

        try:
            # 🔥 关键修复：获取当前实际的会话状态
            session_id = context.get("session_id")
            user_id = context.get("user_id")

            # 从数据库获取当前会话的实际状态
            actual_state = await self._get_actual_conversation_state(session_id, user_id)
            self.logger.info(f"获取到实际会话状态: {actual_state}")

            # 🔥 关键修复：根据实际状态直接处理
            if actual_state == "COLLECTING_INFO":
                self.logger.info("检测到用户在需求采集过程中，直接调用process_answer_and_ask_next处理")

                try:
                    # 直接调用需求采集的回答处理方法
                    if hasattr(self.requirement_flow, 'factory'):
                        conversation_agent = self.requirement_flow.factory.get_conversation_flow_agent(session_id)

                        # 直接调用处理用户回答的方法
                        result_content = await conversation_agent.handle_process_answer_and_ask_next(
                            message=message,
                            session_id=session_id,
                            user_id=user_id,
                            history=[],  # 这里可以传入实际的对话历史
                            decision_result=intent_result
                        )

                        return RoutingResult(
                            success=True,
                            content=result_content,
                            mode=ConversationMode.REQUIREMENT_COLLECTION,
                            context_updates={
                                "requirement_state": {
                                    "phase": "collecting",
                                    "last_action": "process_answer_and_ask_next"
                                }
                            },
                            processing_info={
                                "direct_answer_processing": True,
                                "skipped_domain_classification": True,
                                "skipped_category_classification": True
                            }
                        )

                except Exception as e:
                    self.logger.error(f"直接处理用户回答失败: {e}", exc_info=True)
                    # 如果失败，继续使用原有逻辑

            # 更新上下文中的状态信息，确保决策引擎能正确识别状态
            context["current_state"] = {
                "mode": "requirement_collection",
                "actual_conversation_state": actual_state,
                "collection_phase": "active" if actual_state == "COLLECTING_INFO" else "initial"
            }

            # 调用现有的需求采集流程
            # 这里需要根据实际的需求采集接口进行调用
            if hasattr(self.requirement_flow, 'process_message'):
                # 如果有统一的消息处理接口
                result = await self.requirement_flow.process_message(
                    message=message,
                    session_id=session_id,
                    user_id=user_id,
                    decision_result=intent_result
                )
            elif hasattr(self.requirement_flow, 'handle_message'):
                # 如果有handle_message接口
                result = await self.requirement_flow.handle_message(
                    message, session_id, intent_result
                )
            else:
                # 回退到基本处理
                result = {
                    "content": "我来帮您收集需求信息。请详细描述您的项目需求。",
                    "domain_result": None,
                    "category_result": None,
                    "focus_points_status": None
                }

            # 提取内容
            content = result.get("content", "")
            if not content:
                content = "让我来帮您整理需求。请告诉我更多详细信息。"

            # 更新需求采集状态
            requirement_state = context.get("current_state", {})
            if result.get("domain_result"):
                requirement_state["domain"] = result["domain_result"]
            if result.get("category_result"):
                requirement_state["category"] = result["category_result"]
            if result.get("focus_points_status"):
                requirement_state["focus_points"] = result["focus_points_status"]

            return RoutingResult(
                success=True,
                content=content,
                mode=ConversationMode.REQUIREMENT_COLLECTION,
                context_updates={
                    "requirement_state": requirement_state,
                    "last_requirement_result": result
                },
                processing_info={
                    "requirement_flow_result": result,
                    "has_domain": bool(result.get("domain_result")),
                    "has_category": bool(result.get("category_result"))
                }
            )

        except Exception as e:
            self.logger.error(f"需求采集处理失败: {e}", exc_info=True)
            return RoutingResult(
                success=False,
                content="处理需求采集时出现问题，请重新描述您的需求。",
                mode=ConversationMode.REQUIREMENT_COLLECTION,
                context_updates={},
                processing_info={"error": str(e)},
                error=str(e)
            )

    async def _handle_clarification(self, message: str, context: Dict[str, Any],
                                   intent_result: Dict[str, Any]) -> RoutingResult:
        """
        处理澄清对话

        Args:
            message: 用户消息
            context: 会话上下文
            intent_result: 意图识别结果

        Returns:
            RoutingResult: 处理结果
        """
        self.routing_stats["clarification_requests"] += 1

        try:
            # 获取澄清状态
            clarification_state = context.get("current_state", {})
            attempts = clarification_state.get("clarification_attempts", 0)

            # 构建澄清回复
            if attempts == 0:
                content = "我需要更好地理解您的需求。请问您是想：\n\n1. 查询现有的帮助文档和常见问题\n2. 开始一个新的项目需求收集\n3. 其他具体问题\n\n请选择或详细说明您的需求。"
            elif attempts == 1:
                content = "让我换个方式来理解您的需求。您可以：\n\n• 直接描述您遇到的具体问题\n• 说明您想要实现的目标\n• 或者告诉我您需要什么类型的帮助\n\n请用您自己的话来描述。"
            else:
                # 多次澄清后，引导到需求采集
                content = "我来直接帮您整理需求吧。请告诉我：\n\n1. 您的项目类型（如：网站、App、系统等）\n2. 主要功能需求\n3. 目标用户群体\n\n我会根据您的回答提供针对性的建议。"

                # 准备切换到需求采集模式
                context["next_mode"] = ConversationMode.REQUIREMENT_COLLECTION.value

            # 更新澄清状态
            clarification_state.update({
                "clarification_attempts": attempts + 1,
                "last_clarification": message,
                "timestamp": time.time()
            })

            return RoutingResult(
                success=True,
                content=content,
                mode=ConversationMode.CLARIFICATION,
                context_updates={
                    "clarification_state": clarification_state
                },
                processing_info={
                    "clarification_attempts": attempts + 1,
                    "will_switch_mode": attempts >= 2
                }
            )

        except Exception as e:
            self.logger.error(f"澄清处理失败: {e}", exc_info=True)
            return RoutingResult(
                success=False,
                content="让我直接帮您收集需求信息吧。请描述您的项目需求。",
                mode=ConversationMode.REQUIREMENT_COLLECTION,
                context_updates={},
                processing_info={"error": str(e)},
                error=str(e)
            )

    async def _handle_unknown_mode(self, message: str, context: Dict[str, Any],
                                  intent_result: Dict[str, Any]) -> RoutingResult:
        """
        处理未知模式

        Args:
            message: 用户消息
            context: 会话上下文
            intent_result: 意图识别结果

        Returns:
            RoutingResult: 处理结果
        """
        try:
            # 未知模式时，默认引导到澄清
            content = "您好！我是您的AI助手。我可以帮您：\n\n1. 🔍 查询相关文档和常见问题\n2. 📋 收集和整理项目需求\n3. 💡 提供专业建议和指导\n\n请告诉我您需要什么帮助？"

            return RoutingResult(
                success=True,
                content=content,
                mode=ConversationMode.CLARIFICATION,
                context_updates={
                    "initial_greeting": True,
                    "available_modes": ["knowledge_base", "requirement_collection"]
                },
                processing_info={
                    "fallback_to_clarification": True,
                    "reason": "unknown_mode"
                }
            )

        except Exception as e:
            self.logger.error(f"未知模式处理失败: {e}", exc_info=True)
            return RoutingResult(
                success=False,
                content="您好！请告诉我您需要什么帮助？",
                mode=ConversationMode.CLARIFICATION,
                context_updates={},
                processing_info={"error": str(e)},
                error=str(e)
            )

    def get_routing_stats(self) -> Dict[str, Any]:
        """
        获取路由统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.routing_stats.copy()

    def reset_routing_stats(self):
        """重置路由统计"""
        self.routing_stats = {
            "total_requests": 0,
            "knowledge_base_requests": 0,
            "requirement_requests": 0,
            "clarification_requests": 0,
            "mode_switches": 0
        }

    def _is_self_introduction_request(self, message: str) -> bool:
        """
        检查是否为自我介绍请求

        Args:
            message: 用户消息

        Returns:
            bool: 是否为自我介绍请求
        """
        message_lower = message.lower()

        self_intro_patterns = [
            "介绍自己", "介绍一下自己", "你是谁", "你是什么",
            "自我介绍", "介绍下自己", "请介绍自己", "你好，请介绍自己",
            "能介绍一下吗", "可以介绍一下吗"
        ]

        return any(pattern in message_lower for pattern in self_intro_patterns)

    async def _get_actual_conversation_state(self, session_id: str, user_id: str) -> str:
        """
        从数据库获取当前会话的实际状态

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            str: 当前会话状态 (IDLE, COLLECTING_INFO, DOCUMENTING)
        """
        try:
            # 通过requirement_flow获取数据库管理器
            db_manager = None

            # 尝试多种方式获取数据库管理器
            if hasattr(self.requirement_flow, 'db_manager'):
                db_manager = self.requirement_flow.db_manager
            elif hasattr(self.requirement_flow, 'factory'):
                # RequirementFlowAdapter的情况
                try:
                    conversation_agent = self.requirement_flow.factory.get_conversation_flow_agent(session_id)
                    if hasattr(conversation_agent, 'db_manager'):
                        db_manager = conversation_agent.db_manager
                    elif hasattr(conversation_agent, 'database_manager'):
                        db_manager = conversation_agent.database_manager
                except Exception as e:
                    self.logger.debug(f"通过factory获取数据库管理器失败: {e}")
            elif hasattr(self.requirement_flow, 'conversation_flow') and hasattr(self.requirement_flow.conversation_flow, 'db_manager'):
                db_manager = self.requirement_flow.conversation_flow.db_manager

            if not db_manager:
                self.logger.warning("无法获取数据库管理器")
                return "IDLE"

            # 检查是否有未完成的文档
            doc_result = await db_manager.get_record(
                """
                SELECT document_id FROM documents
                WHERE conversation_id = ? AND user_id = ? AND status = 'draft'
                """,
                (session_id, user_id)
            )

            if doc_result:
                return "DOCUMENTING"

            # 检查是否有领域和分类信息
            domain_result = await db_manager.get_record(
                """
                SELECT domain_id, category_id
                FROM conversations
                WHERE conversation_id = ? AND user_id = ?
                """,
                (session_id, user_id)
            )

            if domain_result and domain_result.get('domain_id'):
                return "COLLECTING_INFO"

            return "IDLE"

        except Exception as e:
            self.logger.warning(f"获取会话状态失败: {e}")
            return "IDLE"

    async def _re_analyze_intent_with_state(self, message: str, context: Dict[str, Any], actual_state: str) -> Optional[Dict[str, Any]]:
        """
        基于实际会话状态重新分析用户意图

        Args:
            message: 用户消息
            context: 会话上下文
            actual_state: 实际会话状态

        Returns:
            Optional[Dict[str, Any]]: 重新分析的意图结果，如果失败则返回None
        """
        try:
            # 构建包含状态信息的上下文
            state_aware_context = context.copy()
            state_aware_context["current_state"] = actual_state

            # 重新进行意图识别
            intent_result = await self.intent_engine.analyze(
                message=message,
                context=state_aware_context
            )

            if not intent_result:
                self.logger.warning("重新意图识别失败")
                return None

            # 重新进行决策
            decision_result = await self.decision_engine.decide(
                intent=intent_result.get("intent"),
                sub_intent=intent_result.get("sub_intent"),
                emotion=intent_result.get("emotion"),
                context=state_aware_context
            )

            if not decision_result:
                self.logger.warning("重新决策失败")
                return None

            # 合并结果
            updated_result = intent_result.copy()
            updated_result.update(decision_result)

            return updated_result

        except Exception as e:
            self.logger.error(f"基于状态重新分析意图失败: {e}")
            return None