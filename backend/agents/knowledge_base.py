"""负责知识库的查询"""
import json
import logging
from typing import Any, Dict, List, Optional, Union
import autogen
from ..utils.db_optimizer import DBOptimizer
from ..config import settings
from ..config.unified_config_loader import get_unified_config

# 负责知识库查询
class KnowledgeBaseAgent(autogen.ConversableAgent):

    # 初始化KnowledgeBaseAgent（无状态）
    def __init__(self, db_path: str = None):
        """
        初始化知识库代理（无状态设计）

        Args:
            db_path (str, optional): 数据库路径。默认为 settings.DATABASE_PATH。
        """
        super().__init__(
            name="KnowledgeBase",
            human_input_mode="NEVER"  # 不需要人工输入
        )
        # 移除user_id实例变量，改为在查询时动态传递
        # self.user_id = user_id  # 已移除

        # 数据库优化器，用于执行SQL查询（无状态）
        db_path_str = db_path or str(settings.DATABASE_PATH)
        self.db_optimizer = DBOptimizer(
            db_path=db_path_str
            # user_id=user_id  # 已移除，改为在查询时传递
        )

        # 从配置读取日志级别
        self.logger = logging.getLogger(__name__)
        log_level = getattr(settings, 'LOG_LEVEL', 'INFO')
        self.logger.setLevel(getattr(logging, log_level.upper()))

        self.register_reply([autogen.Agent, None], self.process_query)

    # 处理知识库查询请求。
    def process_query(
        self,
        recipient: autogen.Agent,
        messages: Optional[List[Dict]] = None,
        sender: Optional[autogen.Agent] = None,
        config: Optional[Any] = None,
    ) -> Dict[str, Any]:
        """
        处理知识库查询请求

        Args:
            recipient (autogen.Agent): 接收消息的Agent
            messages (Optional[List[Dict]]): 消息列表
            sender (Optional[autogen.Agent]): 发送消息的Agent
            config (Optional[Any]): 配置信息

        Returns:
            Dict[str, Any]: 查询结果
        """
        if not messages or len(messages) == 0:
            raise ValueError("查询消息不能为空")

        query_text = messages[-1]["content"]

        # 查询知识库
        try:
            # 解析查询参数
            params = self._parse_query(query_text)

            # 执行知识库查询
            sql_query = """
                SELECT fp.focus_id, fp.name, fp.description, fp.priority, fp.example,
                       c.name as category_name, d.name as domain_name
                FROM focus_point_definitions fp
                JOIN categories c ON fp.category_id = c.category_id
                JOIN domains d ON c.domain_id = d.domain_id
                WHERE d.name LIKE :domain
                AND c.name LIKE :category
                AND (fp.name LIKE :keywords OR fp.description LIKE :keywords)
                LIMIT :limit
            """

            # 修复参数传递问题 - 使用正确的参数格式
            query_params = {
                "domain": f"%{params.get('domain', '')}%",
                "category": f"%{params.get('category', '')}%",
                "keywords": f"%{params.get('keywords', [''])[0] if params.get('keywords') else ''}%",
                "limit": params.get("limit", 5)
            }

            results = self.db_optimizer.execute_query(sql_query, query_params)
            validated_results = self._validate_query_results(results)

            return {
                "content": self._format_results(validated_results),
                "role": "assistant"
            }
        except Exception as e:
            self.logger.error(f"知识库查询失败: {str(e)}", exc_info=True)
            return {
                "content": f"知识库查询失败: {str(e)}",
                "role": "error"
            }

    # 解析查询参数
    def _parse_query(self, query: str) -> Dict[str, Any]:
        """
        解析查询字符串，支持更复杂的查询语法

        支持格式：
        - "domain:软件开发 category:移动应用 关键词"
        - "关键词1 关键词2"
        - "limit:10 关键词"

        Args:
            query (str): 查询字符串

        Returns:
            Dict[str, Any]: 解析后的查询参数
        """
        params = {"keywords": [], "domain": "", "category": "", "limit": 5}

        if not query or not query.strip():
            return params

        parts = query.strip().split()
        for part in parts:
            if ":" in part and len(part.split(":", 1)) == 2:
                key, value = part.split(":", 1)
                key = key.lower()
                if key in ["domain", "category"]:
                    params[key] = value
                elif key == "limit":
                    try:
                        # 使用配置的最大限制
                        max_limit = get_unified_config().get_threshold("knowledge_base.performance.max_results", 50)
                        default_limit = get_unified_config().get_threshold("knowledge_base.performance.default_limit", 5)
                        params[key] = max(1, min(int(value), max_limit))
                    except ValueError:
                        default_limit = get_unified_config().get_threshold("knowledge_base.performance.default_limit", 5)
                        self.logger.warning(f"无效的limit值: {value}, 使用默认值{default_limit}")
                        params[key] = default_limit
                else:
                    # 不认识的键值对当作关键词处理
                    params["keywords"].append(part)
            else:
                params["keywords"].append(part)

        return params

    def _validate_query_results(self, results: List[Dict]) -> List[Dict]:
        """
        验证和清理查询结果

        Args:
            results (List[Dict]): 原始查询结果

        Returns:
            List[Dict]: 验证后的查询结果
        """
        if not results:
            return []

        validated_results = []
        for result in results:
            # 确保必要字段存在
            if all(key in result for key in ['name', 'description']):
                # 清理空值和None值
                cleaned_result = {}
                for key, value in result.items():
                    if value is not None:
                        cleaned_result[key] = str(value).strip() if isinstance(value, str) else value
                    else:
                        cleaned_result[key] = ''
                validated_results.append(cleaned_result)
            else:
                self.logger.warning(f"查询结果缺少必要字段: {result}")

        return validated_results

    # 格式化查询结果
    def _format_results(self, results: List[Dict]) -> str:
        """
        Args:
            results (List[Dict]): 查询结果列表。
        Returns:
            str: 格式化后的查询结果。
        """
        if not results:
            return "没有找到匹配的知识库记录"
            
        formatted = []
        for idx, result in enumerate(results, 1):
            formatted.append(
                f"{idx}. {result.get('name', '无标题')}\n"
                f"   领域: {result.get('domain_name', '未知')}\n"
                f"   分类: {result.get('category_name', '未知')}\n"
                f"   描述: {result.get('description', '无描述')}\n"
                f"   优先级: {result.get('priority', 'P2')}\n"
                f"   示例: {result.get('example', '无示例')}"
            )
        return "\n\n".join(formatted)

    # 获取所有领域
    def get_domains(self) -> List[Dict[str, Any]]:
        """
        获取所有领域列表

        Returns:
            List[Dict[str, Any]]: 领域列表
        """
        query = """
            SELECT domain_id, name, description
            FROM domains
            ORDER BY name
        """

        try:
            results = self.db_optimizer.execute_query(query)
            validated_results = self._validate_query_results(results)
            self.logger.debug(f"获取到 {len(validated_results)} 个领域")
            return validated_results
        except Exception as e:
            self.logger.error(f"获取领域列表失败: {str(e)}", exc_info=True)
            return []

    # 获取指定领域的所有类别
    def get_categories(self, domain_id: Union[str, int]) -> List[Dict[str, Any]]:
        """
        获取指定领域的所有类别

        Args:
            domain_id (Union[str, int]): 领域ID，支持字符串或整数类型

        Returns:
            List[Dict[str, Any]]: 类别列表
        """
        query = """
            SELECT category_id, name, description
            FROM categories
            WHERE domain_id = :domain_id
            ORDER BY name
        """

        try:
            # 确保domain_id类型兼容性
            domain_id_str = str(domain_id) if domain_id is not None else ""
            results = self.db_optimizer.execute_query(query, {"domain_id": domain_id_str})
            return self._validate_query_results(results)
        except Exception as e:
            self.logger.error(f"获取类别列表失败 - domain_id: {domain_id}, 错误: {str(e)}", exc_info=True)
            return []

    # 获取指定领域的所有类别（别名方法）
    def get_categories_by_domain(self, domain_id: Union[str, int]) -> List[Dict[str, Any]]:
        """
        获取指定领域的所有类别（别名方法）

        Args:
            domain_id (Union[str, int]): 领域ID，支持字符串或整数类型

        Returns:
            List[Dict[str, Any]]: 类别列表
        """
        return self.get_categories(domain_id)

    # 获取指定类别的所有关注点
    def get_concern_points(self, category_id: Union[str, int]) -> List[Dict[str, Any]]:
        """
        获取指定类别的所有关注点

        Args:
            category_id (Union[str, int]): 类别ID，支持字符串或整数类型

        Returns:
            List[Dict[str, Any]]: 关注点列表
        """
        query = """
            SELECT
                fp.focus_id as id,
                fp.name,
                fp.description,
                fp.priority,
                fp.example,
                fp.required,
                c.name as category_name,
                d.name as domain_name
            FROM focus_point_definitions fp
            JOIN categories c ON fp.category_id = c.category_id
            JOIN domains d ON c.domain_id = d.domain_id
            WHERE fp.category_id = :category_id
            ORDER BY
                CASE fp.priority
                    WHEN 'P0' THEN 1
                    WHEN 'P1' THEN 2
                    WHEN 'P2' THEN 3
                    ELSE 4
                END,
                fp.name
        """

        try:
            # 确保category_id类型兼容性
            category_id_str = str(category_id) if category_id is not None else ""

            self.logger.debug(f"开始获取关注点 - category_id: {category_id_str}")
            self.logger.debug(f"完整SQL查询:\n{query}")

            results = self.db_optimizer.execute_query(query, {"category_id": category_id_str})
            validated_results = self._validate_query_results(results)

            self.logger.debug(f"查询结果数量: {len(validated_results)}")
            if validated_results:
                self.logger.debug(f"第一个关注点示例: {json.dumps(validated_results[0], ensure_ascii=False)}")

            return validated_results
        except Exception as e:
            self.logger.error(f"获取关注点列表失败 - category_id: {category_id}, 错误: {str(e)}", exc_info=True)
            return []

    # 获取指定领域和类别的关注点（别名方法）
    def get_focus_points_by_domain_and_category(self, domain_id: Union[str, int], category_id: Union[str, int]) -> List[Dict[str, Any]]:
        """
        获取指定领域和类别的关注点（别名方法）

        Args:
            domain_id (Union[str, int]): 领域ID（暂时未使用，保持接口兼容性）
            category_id (Union[str, int]): 类别ID

        Returns:
            List[Dict[str, Any]]: 关注点列表
        """
        # 目前只使用category_id，domain_id用于接口兼容性
        return self.get_concern_points(category_id)

    # 获取通用建议
    def get_general_suggestions(self, category_id: Union[str, int]) -> List[str]:
        """
        获取指定类别的通用建议

        Args:
            category_id (Union[str, int]): 类别ID

        Returns:
            List[str]: 通用建议列表
        """
        try:
            # 基于类别获取相关的关注点，从中提取通用建议
            concern_points = self.get_concern_points(category_id)

            if not concern_points:
                # 如果没有关注点，返回默认的通用建议
                return [
                    "明确项目的核心目标和预期成果",
                    "确定目标用户群体和使用场景",
                    "制定合理的时间计划和预算范围",
                    "考虑技术实现的可行性和复杂度"
                ]

            # 从关注点描述中提取建议
            suggestions = []
            for point in concern_points[:3]:  # 取前3个重要关注点
                name = point.get('name', '')
                description = point.get('description', '')
                if name and description:
                    suggestions.append(f"关于{name}：{description}")

            # 如果没有足够的建议，添加通用建议
            if len(suggestions) < 2:
                suggestions.extend([
                    "详细描述项目的具体需求和期望",
                    "考虑项目实施过程中可能遇到的挑战"
                ])

            return suggestions[:4]  # 最多返回4个建议

        except Exception as e:
            self.logger.error(f"获取通用建议失败 - category_id: {category_id}, 错误: {str(e)}", exc_info=True)
            # 返回默认建议
            return [
                "明确项目的核心目标",
                "确定目标用户群体",
                "制定合理的实施计划",
                "考虑技术实现方案"
            ]
