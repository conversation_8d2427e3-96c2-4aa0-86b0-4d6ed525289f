#!/usr/bin/env python3
"""
优化的Agent工厂

集成ComponentPoolManager，提供高性能的Agent创建服务。
保持与原有AgentFactory的API兼容性。
"""

import time
from typing import Any, Dict, Optional
from backend.services.component_pool_manager import get_component_pool_manager


class OptimizedAgentFactory:
    """
    优化的Agent工厂

    使用ComponentPoolManager提供高性能的Agent创建服务，
    同时保持与原有AgentFactory的API兼容性。
    """

    def __init__(self):
        self.pool_manager = get_component_pool_manager()
        self._creation_count = 0
        self._total_creation_time = 0.0

        # 预热关键组件
        self._prewarm_components()

    def _prewarm_components(self):
        """预热关键组件"""
        try:
            prewarm_results = self.pool_manager.prewarm_components([
                "config_service",
                "database_manager",
                "llm_service",
                "intent_decision_engine"
            ])

            success_count = sum(1 for success in prewarm_results.values() if success)
            print(f"[INFO] 预热组件完成: {success_count}/{len(prewarm_results)} 成功")

        except Exception as e:
            print(f"[WARNING] 组件预热失败: {e}")

    def get_conversation_flow_agent(self, session_id: str, user_id: str = None, **kwargs) -> Any:
        """
        创建对话流程Agent（优化版本）

        Args:
            session_id: 会话ID
            user_id: 用户ID（可选）
            **kwargs: 额外参数

        Returns:
            配置好的ConversationFlowAgent实例
        """
        start_time = time.time()

        try:
            # 使用ComponentPoolManager快速创建Agent
            agent = self.pool_manager.get_conversation_flow_agent(
                session_id=session_id,
                user_id=user_id,
                **kwargs
            )

            creation_time = time.time() - start_time
            self._creation_count += 1
            self._total_creation_time += creation_time

            print(f"[INFO] 优化Agent创建成功: {session_id} (耗时: {creation_time:.3f}s)")
            return agent

        except Exception as e:
            creation_time = time.time() - start_time
            self._total_creation_time += creation_time
            print(f"[ERROR] 优化Agent创建失败: {session_id}, 错误: {e}")
            raise

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = self._total_creation_time / self._creation_count if self._creation_count > 0 else 0

        return {
            "creation_count": self._creation_count,
            "total_creation_time": self._total_creation_time,
            "average_creation_time": avg_time,
            "pool_metrics": self.pool_manager.get_metrics_summary()
        }

    def get_pool_status(self) -> Dict[str, Any]:
        """获取池状态"""
        return self.pool_manager.get_pool_status()


class CompatibilityAgentFactory:
    """
    兼容性Agent工厂

    提供与原有AgentFactory完全兼容的接口，
    内部使用OptimizedAgentFactory实现。
    """

    def __init__(self):
        self.optimized_factory = OptimizedAgentFactory()

        # 兼容性属性
        self.container = None  # 保持兼容性
        self.logger = self._create_simple_logger()

    def _create_simple_logger(self):
        """创建简单的日志器"""
        class SimpleLogger:
            def info(self, message: str):
                print(f"[INFO] {message}")

            def error(self, message: str):
                print(f"[ERROR] {message}")

            def warning(self, message: str):
                print(f"[WARNING] {message}")

            def debug(self, message: str):
                print(f"[DEBUG] {message}")

        return SimpleLogger()

    def get_conversation_flow_agent(self, session_id: str, **kwargs) -> Any:
        """
        创建对话流程Agent（兼容性接口）

        Args:
            session_id: 会话ID
            **kwargs: 额外参数

        Returns:
            配置好的ConversationFlowAgent实例
        """
        return self.optimized_factory.get_conversation_flow_agent(session_id, **kwargs)

    def _create_message_processor_for_agent(self, agent) -> Any:
        """兼容性方法：创建MessageProcessor"""
        # 这个方法在优化版本中已经集成到Agent创建过程中
        # 这里只是为了保持API兼容性
        if hasattr(agent, 'message_processor') and agent.message_processor is not None:
            return agent.message_processor
        else:
            raise RuntimeError("MessageProcessor应该已经在Agent创建过程中设置")


# ==================== 全局实例 ====================

# 创建全局优化工厂实例
_optimized_factory: Optional[OptimizedAgentFactory] = None
_compatibility_factory: Optional[CompatibilityAgentFactory] = None


def get_optimized_agent_factory() -> OptimizedAgentFactory:
    """获取优化的Agent工厂实例"""
    global _optimized_factory
    if _optimized_factory is None:
        _optimized_factory = OptimizedAgentFactory()
    return _optimized_factory


def get_compatibility_agent_factory() -> CompatibilityAgentFactory:
    """获取兼容性Agent工厂实例"""
    global _compatibility_factory
    if _compatibility_factory is None:
        _compatibility_factory = CompatibilityAgentFactory()
    return _compatibility_factory


# ==================== 便捷函数 ====================

def create_agent_fast(session_id: str, user_id: str = None, **kwargs) -> Any:
    """快速创建Agent的便捷函数"""
    factory = get_optimized_agent_factory()
    return factory.get_conversation_flow_agent(session_id, user_id, **kwargs)


def get_agent_performance_stats() -> Dict[str, Any]:
    """获取Agent创建性能统计"""
    factory = get_optimized_agent_factory()
    return factory.get_performance_stats()


# ==================== 导出接口 ====================

__all__ = [
    'OptimizedAgentFactory',
    'CompatibilityAgentFactory',
    'get_optimized_agent_factory',
    'get_compatibility_agent_factory',
    'create_agent_fast',
    'get_agent_performance_stats',
]