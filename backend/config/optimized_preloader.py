#!/usr/bin/env python3
"""
优化的配置预加载器

实现配置的智能缓存、增量更新和高性能访问。
核心功能：
1. 智能缓存：内存缓存配置数据，避免重复文件I/O
2. 增量更新：监控文件变化，只更新修改的配置
3. 分层缓存：L1内存缓存 + L2文件缓存
4. 并发安全：线程安全的配置访问
5. 性能监控：详细的缓存命中率和加载时间统计
"""

import time
import threading
import hashlib
import json
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
import yaml


class CacheLevel(Enum):
    """缓存级别"""
    L1_MEMORY = "l1_memory"      # 内存缓存
    L2_FILE = "l2_file"          # 文件缓存
    DISK = "disk"                # 磁盘文件


@dataclass
class ConfigCacheEntry:
    """配置缓存条目"""
    config_name: str
    data: Any
    file_hash: str
    file_mtime: float
    cache_time: float
    access_count: int = 0
    last_access_time: float = field(default_factory=time.time)

    def update_access(self):
        """更新访问统计"""
        self.access_count += 1
        self.last_access_time = time.time()


@dataclass
class CacheMetrics:
    """缓存性能指标"""
    l1_hits: int = 0
    l1_misses: int = 0
    l2_hits: int = 0
    l2_misses: int = 0
    disk_reads: int = 0
    total_load_time: float = 0.0
    cache_updates: int = 0

    def get_l1_hit_rate(self) -> float:
        """L1缓存命中率"""
        total = self.l1_hits + self.l1_misses
        return self.l1_hits / total if total > 0 else 0.0

    def get_l2_hit_rate(self) -> float:
        """L2缓存命中率"""
        total = self.l2_hits + self.l2_misses
        return self.l2_hits / total if total > 0 else 0.0

    def get_overall_hit_rate(self) -> float:
        """总体缓存命中率"""
        total_requests = self.l1_hits + self.l1_misses
        cache_hits = self.l1_hits + self.l2_hits
        return cache_hits / total_requests if total_requests > 0 else 0.0


class OptimizedConfigPreloader:
    """
    优化的配置预加载器

    实现多级缓存和智能更新机制。
    """

    def __init__(self, cache_dir: str = "logs/config_cache"):
        # 缓存存储
        self.l1_cache: Dict[str, ConfigCacheEntry] = {}  # 内存缓存
        self.l2_cache_dir = Path(cache_dir)  # 文件缓存目录
        self.l2_cache_dir.mkdir(parents=True, exist_ok=True)

        # 线程安全
        self.cache_lock = threading.RLock()

        # 性能指标
        self.metrics = CacheMetrics()

        # 配置文件路径
        self.config_base_path = Path("backend/config")

        # 支持的配置文件
        self.config_files = [
            "unified_config.yaml",
            "business_rules.yaml",
            "strategies.yaml",
            "message_config.yaml",
            "database_queries.yaml"
        ]

        # 文件监控
        self.file_watchers: Dict[str, float] = {}  # 文件名 -> 最后修改时间

        print("[INFO] 优化配置预加载器初始化完成")

    def _get_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        if not file_path.exists():
            return ""

        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()

    def _get_file_mtime(self, file_path: Path) -> float:
        """获取文件修改时间"""
        try:
            return file_path.stat().st_mtime if file_path.exists() else 0.0
        except OSError:
            return 0.0

    def _load_from_disk(self, config_name: str) -> Tuple[Any, str, float]:
        """从磁盘加载配置"""
        file_path = self.config_base_path / config_name

        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        file_hash = self._get_file_hash(file_path)
        file_mtime = self._get_file_mtime(file_path)

        # 加载YAML文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)

        self.metrics.disk_reads += 1
        return data, file_hash, file_mtime

    def _save_to_l2_cache(self, config_name: str, entry: ConfigCacheEntry):
        """保存到L2文件缓存"""
        try:
            cache_file = self.l2_cache_dir / f"{config_name}.cache.json"
            cache_data = {
                "config_name": entry.config_name,
                "data": entry.data,
                "file_hash": entry.file_hash,
                "file_mtime": entry.file_mtime,
                "cache_time": entry.cache_time
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False, default=str)

        except Exception as e:
            print(f"[WARNING] L2缓存保存失败: {config_name}, 错误: {e}")

    def _load_from_l2_cache(self, config_name: str) -> Optional[ConfigCacheEntry]:
        """从L2文件缓存加载"""
        try:
            cache_file = self.l2_cache_dir / f"{config_name}.cache.json"

            if not cache_file.exists():
                return None

            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            return ConfigCacheEntry(
                config_name=cache_data["config_name"],
                data=cache_data["data"],
                file_hash=cache_data["file_hash"],
                file_mtime=cache_data["file_mtime"],
                cache_time=cache_data["cache_time"]
            )

        except Exception as e:
            print(f"[WARNING] L2缓存加载失败: {config_name}, 错误: {e}")
            return None

    def _is_cache_valid(self, entry: ConfigCacheEntry, config_name: str) -> bool:
        """检查缓存是否有效"""
        file_path = self.config_base_path / config_name

        if not file_path.exists():
            return False

        # 检查文件修改时间
        current_mtime = self._get_file_mtime(file_path)
        if current_mtime != entry.file_mtime:
            return False

        # 检查文件哈希（可选，更严格的验证）
        current_hash = self._get_file_hash(file_path)
        if current_hash != entry.file_hash:
            return False

        return True

    def get_config(self, config_name: str, force_reload: bool = False) -> Any:
        """
        获取配置数据（多级缓存）

        Args:
            config_name: 配置文件名
            force_reload: 是否强制重新加载

        Returns:
            配置数据
        """
        start_time = time.time()

        with self.cache_lock:
            try:
                # 强制重新加载
                if force_reload:
                    return self._reload_config(config_name)

                # L1缓存检查
                if config_name in self.l1_cache:
                    entry = self.l1_cache[config_name]

                    # 验证缓存有效性
                    if self._is_cache_valid(entry, config_name):
                        entry.update_access()
                        self.metrics.l1_hits += 1
                        print(f"[DEBUG] L1缓存命中: {config_name}")
                        return entry.data
                    else:
                        # L1缓存失效，移除
                        del self.l1_cache[config_name]
                        print(f"[DEBUG] L1缓存失效: {config_name}")

                self.metrics.l1_misses += 1

                # L2缓存检查
                l2_entry = self._load_from_l2_cache(config_name)
                if l2_entry and self._is_cache_valid(l2_entry, config_name):
                    # L2缓存命中，提升到L1
                    l2_entry.update_access()
                    self.l1_cache[config_name] = l2_entry
                    self.metrics.l2_hits += 1
                    print(f"[DEBUG] L2缓存命中: {config_name}")
                    return l2_entry.data

                self.metrics.l2_misses += 1

                # 从磁盘加载
                return self._reload_config(config_name)

            finally:
                load_time = time.time() - start_time
                self.metrics.total_load_time += load_time

    def _reload_config(self, config_name: str) -> Any:
        """重新加载配置"""
        print(f"[DEBUG] 从磁盘加载配置: {config_name}")

        data, file_hash, file_mtime = self._load_from_disk(config_name)

        # 创建缓存条目
        entry = ConfigCacheEntry(
            config_name=config_name,
            data=data,
            file_hash=file_hash,
            file_mtime=file_mtime,
            cache_time=time.time()
        )

        # 更新L1缓存
        self.l1_cache[config_name] = entry

        # 异步保存到L2缓存
        try:
            self._save_to_l2_cache(config_name, entry)
        except Exception as e:
            print(f"[WARNING] L2缓存保存失败: {e}")

        self.metrics.cache_updates += 1
        return data

    def preload_all_configs(self) -> Dict[str, bool]:
        """
        预加载所有配置文件

        Returns:
            Dict[str, bool]: 各配置文件的加载结果
        """
        results = {}
        start_time = time.time()

        print(f"[INFO] 开始预加载 {len(self.config_files)} 个配置文件...")

        for config_name in self.config_files:
            try:
                self.get_config(config_name)
                results[config_name] = True
                print(f"[INFO] 预加载成功: {config_name}")
            except Exception as e:
                results[config_name] = False
                print(f"[ERROR] 预加载失败: {config_name}, 错误: {e}")

        total_time = time.time() - start_time
        success_count = sum(1 for success in results.values() if success)

        print(f"[INFO] 配置预加载完成: {success_count}/{len(self.config_files)} 成功, 耗时: {total_time:.3f}s")

        return results

    def check_for_updates(self) -> List[str]:
        """
        检查配置文件更新

        Returns:
            List[str]: 需要更新的配置文件列表
        """
        updated_configs = []

        with self.cache_lock:
            for config_name in self.config_files:
                if config_name in self.l1_cache:
                    entry = self.l1_cache[config_name]
                    if not self._is_cache_valid(entry, config_name):
                        updated_configs.append(config_name)

        return updated_configs

    def refresh_updated_configs(self) -> Dict[str, bool]:
        """
        刷新已更新的配置

        Returns:
            Dict[str, bool]: 刷新结果
        """
        updated_configs = self.check_for_updates()
        results = {}

        if not updated_configs:
            print("[INFO] 没有配置文件需要更新")
            return results

        print(f"[INFO] 发现 {len(updated_configs)} 个配置文件需要更新: {updated_configs}")

        for config_name in updated_configs:
            try:
                self.get_config(config_name, force_reload=True)
                results[config_name] = True
                print(f"[INFO] 配置更新成功: {config_name}")
            except Exception as e:
                results[config_name] = False
                print(f"[ERROR] 配置更新失败: {config_name}, 错误: {e}")

        return results

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            return {
                "l1_cache_size": len(self.l1_cache),
                "l1_hit_rate": self.metrics.get_l1_hit_rate(),
                "l2_hit_rate": self.metrics.get_l2_hit_rate(),
                "overall_hit_rate": self.metrics.get_overall_hit_rate(),
                "total_requests": self.metrics.l1_hits + self.metrics.l1_misses,
                "disk_reads": self.metrics.disk_reads,
                "cache_updates": self.metrics.cache_updates,
                "total_load_time": self.metrics.total_load_time,
                "avg_load_time": self.metrics.total_load_time / max(1, self.metrics.disk_reads),
                "cached_configs": list(self.l1_cache.keys()),
                "config_access_stats": {
                    name: {
                        "access_count": entry.access_count,
                        "last_access": entry.last_access_time,
                        "cache_age": time.time() - entry.cache_time
                    }
                    for name, entry in self.l1_cache.items()
                }
            }

    def clear_cache(self, config_name: str = None):
        """
        清理缓存

        Args:
            config_name: 指定配置名称，None表示清理所有缓存
        """
        with self.cache_lock:
            if config_name:
                if config_name in self.l1_cache:
                    del self.l1_cache[config_name]
                    print(f"[INFO] 已清理配置缓存: {config_name}")
            else:
                self.l1_cache.clear()
                print("[INFO] 已清理所有配置缓存")

    def get_performance_report(self) -> str:
        """获取性能报告"""
        stats = self.get_cache_stats()

        report = []
        report.append("="*60)
        report.append("配置缓存性能报告")
        report.append("="*60)

        report.append(f"L1缓存大小: {stats['l1_cache_size']}")
        report.append(f"L1命中率: {stats['l1_hit_rate']:.2%}")
        report.append(f"L2命中率: {stats['l2_hit_rate']:.2%}")
        report.append(f"总体命中率: {stats['overall_hit_rate']:.2%}")
        report.append(f"总请求数: {stats['total_requests']}")
        report.append(f"磁盘读取次数: {stats['disk_reads']}")
        report.append(f"缓存更新次数: {stats['cache_updates']}")
        report.append(f"平均加载时间: {stats['avg_load_time']:.4f}s")

        report.append("\n缓存的配置文件:")
        for config_name in stats['cached_configs']:
            access_info = stats['config_access_stats'][config_name]
            report.append(f"  {config_name}: 访问{access_info['access_count']}次, 缓存时长{access_info['cache_age']:.1f}s")

        return "\n".join(report)


# ==================== 全局实例 ====================

# 创建全局优化配置预加载器实例
_global_optimized_preloader: Optional[OptimizedConfigPreloader] = None
_preloader_lock = threading.Lock()


def get_optimized_config_preloader() -> OptimizedConfigPreloader:
    """
    获取全局优化配置预加载器实例（单例模式）

    Returns:
        OptimizedConfigPreloader实例
    """
    global _global_optimized_preloader

    if _global_optimized_preloader is None:
        with _preloader_lock:
            if _global_optimized_preloader is None:
                _global_optimized_preloader = OptimizedConfigPreloader()
                print("[INFO] 全局优化配置预加载器创建完成")

    return _global_optimized_preloader


# ==================== 便捷函数 ====================

def get_config_fast(config_name: str, force_reload: bool = False) -> Any:
    """
    快速获取配置的便捷函数

    Args:
        config_name: 配置文件名
        force_reload: 是否强制重新加载

    Returns:
        配置数据
    """
    preloader = get_optimized_config_preloader()
    return preloader.get_config(config_name, force_reload)


def preload_all_configs_fast() -> Dict[str, bool]:
    """
    快速预加载所有配置的便捷函数

    Returns:
        Dict[str, bool]: 各配置文件的加载结果
    """
    preloader = get_optimized_config_preloader()
    return preloader.preload_all_configs()


def get_config_cache_stats() -> Dict[str, Any]:
    """
    获取配置缓存统计的便捷函数

    Returns:
        缓存统计信息
    """
    preloader = get_optimized_config_preloader()
    return preloader.get_cache_stats()


def refresh_configs() -> Dict[str, bool]:
    """
    刷新配置的便捷函数

    Returns:
        刷新结果
    """
    preloader = get_optimized_config_preloader()
    return preloader.refresh_updated_configs()


def clear_config_cache(config_name: str = None):
    """
    清理配置缓存的便捷函数

    Args:
        config_name: 指定配置名称，None表示清理所有缓存
    """
    preloader = get_optimized_config_preloader()
    preloader.clear_cache(config_name)


# ==================== 兼容性接口 ====================

class ConfigPreloaderAdapter:
    """
    配置预加载器适配器

    提供与原有config_preloader兼容的接口。
    """

    def __init__(self):
        self.optimized_preloader = get_optimized_config_preloader()

    async def preload_all_configs(self) -> bool:
        """兼容性方法：预加载所有配置"""
        results = self.optimized_preloader.preload_all_configs()
        return all(results.values())

    def get_preload_status(self) -> Dict[str, Any]:
        """兼容性方法：获取预加载状态"""
        stats = self.optimized_preloader.get_cache_stats()
        return {
            "status": "completed" if stats["l1_cache_size"] > 0 else "not_started",
            "loaded_configs": stats["cached_configs"],
            "cache_stats": stats
        }


# 创建兼容性实例
config_preloader_compat = ConfigPreloaderAdapter()


# ==================== 导出接口 ====================

__all__ = [
    # 核心类
    'OptimizedConfigPreloader',
    'ConfigCacheEntry',
    'CacheMetrics',
    'CacheLevel',

    # 全局实例获取函数
    'get_optimized_config_preloader',

    # 便捷函数
    'get_config_fast',
    'preload_all_configs_fast',
    'get_config_cache_stats',
    'refresh_configs',
    'clear_config_cache',

    # 兼容性接口
    'ConfigPreloaderAdapter',
    'config_preloader_compat',
]