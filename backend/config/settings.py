"""
基础配置设置
"""
from pathlib import Path
import os
from dotenv import load_dotenv
from pydantic import BaseModel, Field
import logging

# Agent类型定义已移至统一配置管理系统
# 请通过 get_unified_config().get_scenario_llm_mapping() 获取场景-模型映射

# Agent实例名称配置已移至统一配置管理系统
# 现代化架构中，Agent通过依赖注入动态创建，不再需要硬编码名称映射

# 加载环境变量
env_path = Path(__file__).resolve().parent.parent.parent / '.env'
load_dotenv(env_path)

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 确保日志目录存在并有写入权限
LOG_DIR = BASE_DIR.parent / "logs"  # 项目根目录/logs
PERFORMANCE_LOG_DIR = LOG_DIR / "performance"  # 项目根目录/logs/performance
LOG_DIR.mkdir(exist_ok=True)
PERFORMANCE_LOG_DIR.mkdir(exist_ok=True)
LOG_LEVEL = "DEBUG"  # 改为DEBUG级别记录更详细信息
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5  # 保留5个日志文件

# 测试日志目录权限
try:
    test_log_file = LOG_DIR / "test_write.log"
    with open(test_log_file, "w") as f:
        f.write("测试写入权限\n")
    os.remove(test_log_file)
    logger.info(f"日志目录 {LOG_DIR} 可写")
except Exception as e:
    logger.error(f"日志目录 {LOG_DIR} 写入测试失败: {str(e)}")
    # 尝试使用当前目录
    

# 文件日志处理器已由统一日志系统管理
logger.info("使用统一日志系统，无需单独配置文件处理器")

class CircuitBreakerConfig(BaseModel):
    failure_threshold: int = Field(default=8, env="LLM_CIRCUIT_BREAKER_FAILURE_THRESHOLD")
    recovery_time: int = Field(default=60, env="LLM_CIRCUIT_BREAKER_RECOVERY_TIME")

class LLMConfig(BaseModel):
    provider: str
    api_key: str = Field(env="DEEPSEEK_API_KEY")
    timeout: float = Field(default=45.0, env="LLM_TIMEOUT")
    max_retries: int = Field(default=5, env="LLM_MAX_RETRIES")
    circuit_breaker: CircuitBreakerConfig = CircuitBreakerConfig()

    class Config:
        env_prefix = "LLM_"

# 检查必要的环境变量
def get_required_env(key: str, default: str = "") -> str:
    """获取必要的环境变量，如果不存在则使用默认值"""
    value = os.getenv(key, default)
    return value

# LLM配置已迁移到统一配置系统 (unified_config.yaml)
# 请使用 get_unified_config().get_llm_config() 获取LLM配置

# 数据库配置
DATABASE_PATH = BASE_DIR / "data" / "aidatabase.db"

# 会话配置
SESSION_TIMEOUT = 3600  # 会话超时时间(秒)

# API超时配置
API_REQUEST_TIMEOUT = 60.0  # API请求超时时间(秒)，应该比LLM超时时间更长

# ============================================================================
# 已迁移配置说明
# ============================================================================
#
# 🚀 场景参数配置已迁移到 unified_config.yaml
#
# 原 SCENARIO_PARAMS 和 DEFAULT_LLM_PARAMS 已迁移到:
# backend/config/unified_config.yaml -> llm.scenario_params
#
# 新的访问方式:
# from backend.agents.llm_config_manager import LLMConfigManager
# config_manager = LLMConfigManager()
# params = config_manager.get_scenario_params("question_polisher")
#
# 或者通过配置服务:
# from backend.config import config_service
# params = config_service.get_scenario_params("question_polisher")
#
# ============================================================================

# 向后兼容：保留SCENARIO_PARAMS用于测试和迁移验证
# 注意：这些配置已不再被系统使用，仅用于兼容性检查
# TODO: 在确认所有模块都已迁移后，可以删除这些配置

# 已废弃 - 请使用 unified_config.yaml 中的配置
SCENARIO_PARAMS = {}  # 已迁移到 unified_config.yaml
DEFAULT_LLM_PARAMS = {}  # 已迁移到 unified_config.yaml

# ============================================================================
# 已迁移配置说明 - 业务阈值配置
# ============================================================================
#
# 🚀 业务阈值配置已迁移到 unified_config.yaml
#
# 原 BUSINESS_THRESHOLDS 已迁移到:
# - extraction.completeness_threshold → business_rules.requirement_collection.completion_threshold
# - conversation.history.max_turns → system.performance.max_conversation_turns
# - conversation.history.max_message_length → system.performance.max_message_length
# - knowledge_base.* → knowledge_base.performance.*
#
# 新的访问方式:
# from backend.config import config_service
# threshold = config_service.get_business_rule("requirement_collection.completion_threshold", 0.8)
# max_turns = config_service.get_threshold("system.performance.max_conversation_turns", 15)
#
# ============================================================================

# 已废弃 - 请使用 unified_config.yaml 中的配置
# BUSINESS_THRESHOLDS = {}  # 已迁移到 unified_config.yaml

# ============================================================================
# 配置获取函数
# ============================================================================

# get_llm_config 函数已迁移到统一配置系统
# 请使用 LLMConfigManager 或 config_service.get_llm_config() 获取LLM配置

def get_scenario_params(scenario: str) -> dict:
    """
    获取指定场景的参数配置

    ⚠️  已废弃：此函数已迁移到统一配置系统

    新的使用方式:
    from backend.config import config_service
    params = config_service.get_scenario_params(scenario)

    Args:
        scenario: 场景名称

    Returns:
        场景参数字典
    """
    import warnings
    warnings.warn(
        "get_scenario_params() 已废弃，请使用 config_service.get_scenario_params()",
        DeprecationWarning,
        stacklevel=2
    )

    # 重定向到新的配置服务
    try:
        from backend.config import config_service
        return config_service.get_scenario_params(scenario)
    except Exception:
        # 回退到默认配置
        return {
            "temperature": 0.7,
            "max_tokens": 4000,
            "timeout": 30
        }
