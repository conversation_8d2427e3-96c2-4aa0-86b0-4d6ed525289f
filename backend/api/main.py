"""
基于FastAPI的需求采集系统API

提供与前端交互的接口，用于测试需求采集系统的功能。
集成了性能监控和优化模块，提高系统性能和稳定性。
支持AutoGen框架的会话管理和生命周期控制。
"""

# 首先加载环境变量
from dotenv import load_dotenv
load_dotenv()

import os
import json
import asyncio
import time
import datetime
import asyncio
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

# 导入Agent工厂和依赖项
from backend.agents.factory import AgentFactory, agent_factory
from backend.api.dependencies import get_conversation_flow_agent_factory, get_current_user_id

# 导入配置管理器
from backend.config.unified_config_loader import get_unified_config

# IOLogger已被统一日志系统替代
# 先导入logging以便后续使用
import logging

# 导入统一日志配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from backend.utils.logging_config import configure_logging, get_logger, ErrorLogger, SessionLogger

# 基础日志配置（最小化配置，确保基本可用）
def _init_basic_logging():
    """初始化基础日志配置，确保在应用启动前有基本的日志功能"""
    if not hasattr(_init_basic_logging, '_initialized'):
        configure_logging(
            log_level=logging.INFO,  # 基础级别
            enable_console=True,
            enable_file=False,  # 启动前不写文件
            template_logging_enabled=False
        )
        _init_basic_logging._initialized = True

# 初始化基础日志
_init_basic_logging()

# 获取基础日志记录器
logger = get_logger(__name__)

def _configure_production_logging():
    """配置生产级日志系统（在lifespan中调用）"""
    configure_logging(
        log_level=logging.DEBUG,  # 开发环境记录所有细节
        enable_console=True,
        enable_file=True,
        max_bytes=10*1024*1024,  # 10MB日志文件
        backup_count=7,  # 保留7份备份
        json_format_enabled=True,  # 启用JSON格式，便于检索
        deduplication_interval=2.0,  # 2秒去重窗口，防止高频日志刷屏
        log_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        template_logging_enabled=False  # 禁用模版内容日志输出
    )

    # 确保控制台处理器也能显示DEBUG级别的日志
    for handler in logging.getLogger().handlers:
        if isinstance(handler, logging.StreamHandler):
            handler.setLevel(logging.INFO)

    logger.info("=== 生产级日志系统配置完成 ===")

# 其他导入
import autogen

# 其他导入
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
import autogen
from backend.api.dependencies import get_current_user_id

# 导入Agent (部分保留，因为可能在其他地方用到)
from backend.agents.llm_service import AutoGenLLMServiceAgent

# 性能监控模块
from backend.utils.performance_monitor import performance_monitor
from backend.utils.performance_middleware import add_performance_middleware
from backend.utils.performance_init import (
     init_performance_monitoring,
     shutdown_performance_features
 )

# 导入配置
from backend.config.settings import API_REQUEST_TIMEOUT

# 导入进度管理器
from backend.utils.progress_indicator import ProgressIndicator, ProgressStage, ProgressUpdate
import asyncio
import json

# AutoGen配置 - 使用统一配置系统
def get_autogen_config():
    """获取AutoGen配置，使用统一配置系统"""
    try:
        from backend.config import config_service
        default_config = config_service.get_llm_config_with_metadata("default")

        return {
            "work_dir": "/tmp/autogen_work",  # 使用临时目录，避免在logs目录创建文件
            "config_list": [
                {
                    "model": default_config.get("model_name", "deepseek-chat"),
                    "api_key": default_config.get("api_key", ""),
                    "base_url": default_config.get("api_base", "https://api.deepseek.com"),
                    "api_type": "deepseek"
                }
            ]
        }
    except Exception as e:
        logger.error(f"获取AutoGen配置失败: {e}")
        # 回退到默认配置
        return {
            "work_dir": "/tmp/autogen_work",
            "config_list": [
                {
                    "model": "deepseek-chat",
                    "api_key": "",
                    "base_url": "https://api.deepseek.com",
                    "api_type": "deepseek"
                }
            ]
        }

AUTOGEN_CONFIG = get_autogen_config()

# 初始化AutoGen工作目录
os.makedirs(AUTOGEN_CONFIG["work_dir"], exist_ok=True)

# 存储会话实例
sessions: Dict[str, Any] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理 - 优化版"""
    # 首先配置生产级日志系统
    _configure_production_logging()
    logger.info("应用启动，开始配置预加载...")

    # 预加载所有配置，避免请求处理时重复加载
    from backend.config.preloader import config_preloader
    preload_success = await config_preloader.preload_all_configs()

    if preload_success:
        logger.info("配置预加载成功，继续初始化Agent工厂...")
    else:
        logger.warning("配置预加载部分失败，但继续启动...")
        failed_configs = config_preloader.get_failed_configs()
        if failed_configs:
            logger.warning(f"失败的配置文件: {', '.join(failed_configs)}")

    # 使用Agent工厂进行统一的依赖注入管理，替代手动创建GlobalResources
    from backend.agents.factory import agent_factory
    app.state.agent_factory = agent_factory
    logger.info("Agent工厂初始化成功。")

    # 初始化性能监控系统
    logger.info("初始化性能监控系统...")
    try:
        # 从业务配置获取监控设置
        from backend.utils.performance_init import get_business_performance_config
        perf_config = get_business_performance_config()
        monitoring_config = perf_config.get("monitoring", {})

        init_performance_monitoring(
            enabled=monitoring_config.get("enable_monitoring", True),
            auto_save=monitoring_config.get("auto_save", True),
            save_interval=monitoring_config.get("save_interval", 300)
        )
        logger.info("性能监控系统初始化成功")
    except Exception as e:
        logger.error(f"性能监控系统初始化失败: {str(e)}")

    # 启动定期清理任务
    cleanup_task = asyncio.create_task(run_periodic_cleanup())

    yield

    logger.info("应用关闭，清理资源...")

    # 关闭性能监控系统
    try:
        shutdown_performance_features()
        logger.info("性能监控系统已关闭")
    except Exception as e:
        logger.error(f"关闭性能监控系统时出错: {str(e)}")

    # 取消清理任务
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        logger.info("清理任务已取消")

async def run_periodic_cleanup():
    """运行定期清理任务"""
    while True:
        try:
            await cleanup_expired_sessions()
            await asyncio.sleep(3600)
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"清理会话时出错: {str(e)}")
            await asyncio.sleep(60)

async def cleanup_expired_sessions(max_age_hours: int = 24):
    """清理过期会话"""
    current_time = time.time()
    for session_id in list(sessions.keys()):
        # 检查会话最后活动时间
        session_data = sessions[session_id]

        # 处理不同类型的会话对象
        if isinstance(session_data, tuple) and len(session_data) == 2:
            # 处理 (groupchat, manager) 类型的会话
            groupchat, _ = session_data
            if not hasattr(groupchat, 'messages') or not groupchat.messages:
                continue
            last_message_time = getattr(groupchat.messages[-1], 'timestamp', 0) or 0
        elif hasattr(session_data, 'get_state'):
            # 处理有 get_state 方法的会话对象
            state = session_data.get_state()
            last_message_time = state.get('last_activity', 0)
        else:
            # 无法确定最后活动时间的会话，保守处理
            continue

        # 检查是否过期
        if current_time - last_message_time > max_age_hours * 3600:
            logger.info(f"清理过期会话: {session_id}")
            del sessions[session_id]

# 定义数据模型
class Message(BaseModel):
    """用户消息模型"""
    message: str = Field(..., min_length=1, max_length=2000, description="用户输入的消息内容")
    session_id: str = Field(default="default_session", description="会话ID")

class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., min_length=1, max_length=2000)
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: str
    state: Optional[Dict[str, Any]] = None
    domain_result: Optional[Dict[str, Any]] = None
    category_result: Optional[Dict[str, Any]] = None
    focus_points_status: Optional[List[Dict[str, Any]]] = None


# 混合AI代理相关数据模型
class KnowledgeBaseQuery(BaseModel):
    """知识库查询请求模型"""
    query: str = Field(..., min_length=1, max_length=1000, description="查询问题")
    session_id: str = Field(default="default_session", description="会话ID")
    role_filter: Optional[str] = Field(None, description="角色过滤 (company/developer)")
    category_filter: Optional[str] = Field(None, description="类别过滤")


class KnowledgeBaseResponse(BaseModel):
    """知识库查询响应模型"""
    success: bool
    answer: str
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    processing_info: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None


class ConfigToggleRequest(BaseModel):
    """配置开关请求模型"""
    enabled: bool = Field(..., description="是否启用")
    reason: Optional[str] = Field(None, description="操作原因")


class SafetyStatusResponse(BaseModel):
    """安全状态响应模型"""
    safety_level: str
    metrics: Dict[str, Any]
    recent_errors: int
    kb_enabled: bool
    auto_recovery_enabled: bool


class DocumentIngestionRequest(BaseModel):
    """文档摄入请求模型"""
    content: str = Field(..., min_length=1, description="文档内容")
    source_path: str = Field(..., description="源文件路径")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="文档元数据")



# 创建FastAPI应用
app = FastAPI(
    title="需求采集系统API(统一入口)",
    description="提供与前端交互的完整API接口，集成AutoGen框架和所有功能模块",
    version="2.0.0",
    openapi_tags=[
        {
            "name": "chat",
            "description": "处理用户对话的核心接口"
        },
        {
            "name": "autogen",
            "description": "AutoGen框架相关接口"
        },
        {
            "name": "session",
            "description": "会话管理相关接口"
        },
        {
            "name": "system",
            "description": "系统管理接口"
        },
        {
            "name": "knowledge_base",
            "description": "知识库相关接口"
        },
        {
            "name": "safety",
            "description": "安全管理接口"
        }
    ],
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# 添加性能监控中间件
add_performance_middleware(app, enabled=True)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 全局的Agent实例化代码已被移除，由GlobalResources和依赖注入代替

# 全局进度管理器存储
progress_managers: Dict[str, ProgressIndicator] = {}
progress_streams: Dict[str, asyncio.Queue] = {}

@app.get("/progress/{session_id}", tags=["chat"], summary="获取实时进度更新")
async def progress_stream(session_id: str, user_id: str):
    """
    Server-Sent Events端点，用于实时推送处理进度
    """
    async def event_generator():
        # 创建进度队列
        progress_key = f"{session_id}_{user_id}"
        if progress_key not in progress_streams:
            progress_streams[progress_key] = asyncio.Queue()

        queue = progress_streams[progress_key]

        try:
            # 发送初始连接确认
            yield f"data: {json.dumps({'type': 'connected', 'session_id': session_id})}\n\n"

            while True:
                try:
                    # 等待进度更新，设置超时避免长时间阻塞
                    progress_data = await asyncio.wait_for(queue.get(), timeout=30.0)
                    yield f"data: {json.dumps(progress_data)}\n\n"
                except asyncio.TimeoutError:
                    # 发送心跳保持连接
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                except Exception as e:
                    logger.error(f"进度流发送失败: {e}")
                    break
        except Exception as e:
            logger.error(f"进度流异常: {e}")
        finally:
            # 清理资源
            if progress_key in progress_streams:
                del progress_streams[progress_key]

    return StreamingResponse(
        event_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/chat", tags=["chat"], summary="处理用户消息(统一入口)", description="接收用户输入并返回系统响应，集成所有功能模块")
@performance_monitor.track_api_call("chat")
async def chat(
    message: Message,
    user_id: str = Depends(get_current_user_id),
    agent_factory: AgentFactory = Depends(get_conversation_flow_agent_factory)
):
    """
    处理用户消息 - 修复多用户并发问题

    - 接收用户文本输入
    - 为每个用户会话创建独立的Agent实例
    - 维护会话状态隔离
    - 返回系统响应
    """
    session_id = message.session_id or f"session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id=user_id)

    try:
        session_logger.log_user_input(
            message=message.message,
            input_type="text",
            request_id=f"req_{int(time.time())}"
        )
        session_logger.log_business_node(
            node_name="chat_request_processing",
            node_type="request_processing",
            result="started",
            flow_id="需求采集流程",
            step_sequence=1,
            total_steps=3 # 简化后的步骤
        )

        # 🔥 修复：使用Agent工厂为每个用户会话创建独立的Agent实例
        logger.info(f"为用户 {user_id} 会话 {session_id} 创建独立的ConversationFlowAgent")
        conversation_flow_agent = agent_factory.get_conversation_flow_agent(session_id)



        session_logger.log_business_node(
            node_name="llm_processing",
            node_type="llm_processing",
            result="started",
            step_sequence=2,
            total_steps=3
        )

        # 设置进度回调
        progress_key = f"{session_id}_{user_id}"

        def progress_callback(update: ProgressUpdate):
            """进度更新回调函数"""
            try:
                progress_data = {
                    'type': 'progress',
                    'stage': update.stage.value,
                    'percentage': update.percentage,
                    'message': update.message,
                    'details': update.details,
                    'estimated_remaining': update.estimated_remaining
                }

                # 如果有活跃的进度流，推送更新
                if progress_key in progress_streams:
                    queue = progress_streams[progress_key]
                    try:
                        queue.put_nowait(progress_data)
                    except asyncio.QueueFull:
                        logger.warning(f"进度队列已满，跳过更新: {session_id}")
            except Exception as e:
                logger.error(f"进度回调执行失败: {e}")

        start_time = time.time()
        try:
            # process_message是异步方法，需要await
            # 根据core_refactored.py的签名，第一个参数应该是消息字符串
            response_data = await conversation_flow_agent.process_message(
                message.message,  # 传递消息字符串而不是字典
                session_id=session_id,
                user_id=user_id,
                progress_callback=progress_callback  # 传递进度回调
            )
        except asyncio.TimeoutError:
            logger.error(f"对话流程处理超时（{API_REQUEST_TIMEOUT}秒），会话: {session_id}")
            return JSONResponse(
                status_code=408,
                content={
                    "response": "抱歉，处理您的请求超时，请稍后再试。",
                    "session_id": session_id,
                }
            )
        processing_duration = time.time() - start_time

        # ... (日志记录部分基本保持不变，此处省略以便清晰)

        # 检查新格式的响应
        if (response_data is None or not isinstance(response_data, dict)):
            logger.error(f"对话流程Agent返回无效响应: {response_data}")
            return JSONResponse(
                status_code=500,
                content={
                    "response": "抱歉，系统未能生成有效响应，请稍后再试。",
                    "session_id": session_id,
                }
            )
            
        # 检查是否成功处理
        if not response_data.get("success", False):
            logger.error(f"对话流程Agent处理失败: {response_data.get('error', 'Unknown error')}")
            return JSONResponse(
                status_code=500,
                content={
                    "response": response_data.get("reply", "抱歉，系统处理失败，请稍后再试。"),
                    "session_id": session_id,
                }
            )
        
        # 记录AI回复日志（新格式适配）
        ai_reply = response_data.get("reply", "")
        if ai_reply:
            # 获取实际使用的模型信息
            try:
                from backend.config import config_service
                model_config = config_service.get_llm_config_with_metadata("conversation_flow")
                actual_model = model_config.get("model_name", "unknown")
                provider = model_config.get("provider", "unknown")

                # 添加模型验证日志 - 显示场景和实际模型
                logger.info(f"[模型验证] API层 - 场景: conversation_flow -> 实际模型: {actual_model} ({provider}), 响应长度: {len(ai_reply)}")

                # 添加说明：这里显示的是API层统一处理的模型，内部组件可能使用不同模型
                logger.info(f"[架构说明] API层使用conversation_flow统一处理，内部组件(如domain_classifier、intent_recognition等)使用各自配置的模型")

                session_logger.log_ai_response(
                    response=ai_reply,
                    model=f"{actual_model} (场景: conversation_flow)",
                    duration=processing_duration
                )

                # 记录LLM调用结果
                session_logger.log_llm_call(
                    model=f"{actual_model} (场景: conversation_flow)",
                    scenario="chat_completion",
                    prompt_length=len(message.message),
                    response_length=len(ai_reply),
                    duration=processing_duration
                )
            except Exception as e:
                logger.warning(f"获取模型配置失败: {e}, 使用默认标识")
                session_logger.log_ai_response(
                    response=ai_reply,
                    model="conversation_flow",
                    duration=processing_duration
                )

                session_logger.log_llm_call(
                    model="conversation_flow",
                    scenario="chat_completion",
                    prompt_length=len(message.message),
                    response_length=len(ai_reply),
                    duration=processing_duration
                )

        # 构建API响应（新格式适配）
        api_response = {
            "response": ai_reply,
            "session_id": session_id,
            "domain_result": response_data.get("domain_result"),
            "category_result": response_data.get("category_result"),
            "focus_points_status": response_data.get("focus_points_status")
        }

        session_logger.log_business_node(
            node_name="chat_request_completed",
            node_type="request_completion",
            result="success",
            response_size=len(json.dumps(api_response, ensure_ascii=False)),
            step_sequence=3,
            total_steps=3,
            flow_id="需求采集流程"
        )

        return api_response

    except Exception as e:
        error_logger = ErrorLogger(__name__, session_id=session_id)
        error_logger.log_system_error(
            component="chat_api",
            operation="process_chat_request",
            error=e,
            request_data={"message": message.message, "session_id": session_id}
        )
        return JSONResponse(
            status_code=500,
            content={
                "response": f"服务器内部错误: {str(e)}",
                "session_id": session_id,
            }
        )



    except Exception as e:
        # 使用专门的错误日志记录器记录详细错误信息
        error_logger = ErrorLogger(__name__, session_id=session_id)
        error_logger.log_system_error(
            component="chat_api",
            operation="process_chat_request",
            error=e,
            request_data={
                "message_length": len(message.message),
                "session_id": session_id,
                "message_preview": message.message[:100] + "..." if len(message.message) > 100 else message.message
            }
        )

        # 在异常情况下，也尝试返回符合前端期望结构的错误信息
        return JSONResponse(
            status_code=500,
            content={
                "response": f"服务器内部错误: {str(e)}",
                "session_id": message.session_id or "unknown_session", # Try to get session_id if possible
                "domain_result": None,
                "category_result": None,
                "focus_points_status": []
            }
        )

@app.post("/autogen/chat", tags=["autogen"], response_model=ChatResponse,
          summary="AutoGen聊天接口(兼容模式)",
          description="兼容旧版AutoGen框架的聊天接口，建议使用/chat接口")
@performance_monitor.track_api_call("autogen_chat")
async def autogen_chat(request: ChatRequest):
    """处理基于AutoGen框架的聊天消息"""
    try:
        logger.info(f"收到AutoGen聊天请求: {request.model_dump_json()}")

        # 获取或创建会话
        session_id = request.session_id or f"session_{int(time.time())}"
        
        # 创建会话日志记录器
        session_logger = SessionLogger(__name__, session_id=session_id, user_id="anonymous")

        # 记录用户输入
        session_logger.log_user_input(
            message=request.message,
            input_type="text"
        )

        if session_id not in sessions:
            llm_agent = AutoGenLLMServiceAgent()
            user_agent = autogen.UserProxyAgent(
                name="UserProxy",
                human_input_mode="NEVER",
                default_auto_reply="...",
                llm_config={"config_list": AUTOGEN_CONFIG["config_list"]},
                code_execution_config={"use_docker": False}
            )
            groupchat = autogen.GroupChat(
                agents=[user_agent, llm_agent],
                messages=[],
                max_round=10
            )
            manager = autogen.GroupChatManager(
                groupchat=groupchat,
                llm_config={"config_list": AUTOGEN_CONFIG["config_list"]}
            )
            sessions[session_id] = (groupchat, manager)

        # 记录业务节点 - 开始LLM处理
        session_logger.log_business_node(
            node_name="autogen_llm_processing",
            node_type="llm_processing",
            result="started"
        )

        # 直接使用LLMServiceAgent处理消息
        groupchat, manager = sessions[session_id]
        llm_agent = groupchat.agents[1]

        start_time = time.time()
        response = await llm_agent.call_llm(
            messages=[{"role": "user", "content": request.message}],
            scenario="conversation",
            session_id=session_id
        )
        processing_duration = time.time() - start_time

        # 记录LLM调用
        session_logger.log_llm_call(
            model=llm_agent.get_model_name(),
            scenario="autogen_conversation",
            prompt_length=len(request.message),
            response_length=len(str(response)),
            duration=processing_duration
        )

        # 记录AI回复
        session_logger.log_ai_response(
            response=str(response),
            model=llm_agent.get_model_name(),
            duration=processing_duration
        )

        return ChatResponse(
            response=str(response),
            session_id=session_id
        )

    except Exception as e:
        # 使用专门的错误日志记录器
        error_logger = ErrorLogger(__name__, session_id=session_id)
        error_logger.log_system_error(
            component="autogen_chat_api",
            operation="process_autogen_chat",
            error=e,
            request_data={
                "message": request.message[:100] + "..." if len(request.message) > 100 else request.message,
                "session_id": session_id
            }
        )
        raise HTTPException(status_code=500, detail="服务器内部错误")

@app.get("/health", tags=["system"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "健康",
        "timestamp": datetime.datetime.now().isoformat(),
        "message": "系统正常运行"
    }

@app.get("/sessions", tags=["session"])
async def list_sessions():
    """列出所有活跃会话"""
    session_list = []
    for session_id, session_data in sessions.items():
        session_info = {"session_id": session_id}

        # 尝试获取更多会话信息
        try:
            if isinstance(session_data, tuple) and len(session_data) == 2:
                groupchat, _ = session_data
                session_info["type"] = "autogen_groupchat"
                session_info["agent_count"] = len(groupchat.agents) if hasattr(groupchat, 'agents') else 0
            elif hasattr(session_data, 'get_state'):
                state = session_data.get_state()
                session_info["type"] = "user_interaction"
                session_info["state"] = state.get("current_state", "UNKNOWN")
            else:
                session_info["type"] = "unknown"
        except Exception as e:
            logger.error(f"获取会话信息时出错: {str(e)}")
            session_info["type"] = "error"

        session_list.append(session_info)

    return {
        "sessions": session_list,
        "count": len(session_list)
    }

@app.delete("/sessions/{session_id}", tags=["session"])
async def delete_session(session_id: str):
    """删除指定会话"""
    if (session_id in sessions):
        del sessions[session_id]
        return {"status": "success", "message": f"会话 {session_id} 已删除"}
    else:
        raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")

@app.get("/performance/stats", tags=["system"])
async def get_performance_stats():
    """获取性能统计信息"""
    try:
        from backend.utils.performance_middleware import get_performance_stats
        stats = get_performance_stats()
        return {
            "status": "success",
            "data": stats,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        error_logger = ErrorLogger(__name__)
        error_logger.log_system_error(
            component="performance_api",
            operation="get_performance_stats",
            error=e
        )
        raise HTTPException(status_code=500, detail="获取性能统计信息失败")

@app.get("/performance/status", tags=["system"])
async def get_performance_status():
    """获取性能监控状态"""
    try:
        from backend.utils.performance_init import get_performance_status
        status = get_performance_status()
        return {
            "status": "success",
            "data": status,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取性能监控状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取性能监控状态失败")

@app.post("/performance/reset", tags=["system"])
async def reset_performance_stats():
    """重置性能统计数据"""
    try:
        from backend.utils.performance_middleware import reset_performance_stats
        reset_performance_stats()
        return {
            "status": "success",
            "message": "性能统计数据已重置",
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"重置性能统计数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="重置性能统计数据失败")


# ==================== 混合AI代理API端点 ====================

@app.post("/knowledge-base/query", response_model=KnowledgeBaseResponse, tags=["knowledge_base"])
async def query_knowledge_base(request: KnowledgeBaseQuery):
    """
    知识库查询专用端点

    直接查询知识库，不经过对话流程，适用于快速问答场景
    """
    try:
        logger.info(f"知识库查询请求: {request.query[:50]}...")

        # 获取RAG代理
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        if not rag_agent:
            raise HTTPException(status_code=503, detail="知识库服务不可用")

        # 构建查询上下文
        query_context = {
            "session_id": request.session_id,
            "user_id": request.session_id,  # 简化处理
            "role_filter": request.role_filter,
            "category_filter": request.category_filter
        }

        # 执行查询
        result = await rag_agent.query(request.query, query_context)

        return KnowledgeBaseResponse(
            success=result.success,
            answer=result.answer,
            sources=result.sources,
            processing_info=result.processing_info,
            error=result.error
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"知识库查询失败: {e}", exc_info=True)
        return KnowledgeBaseResponse(
            success=False,
            answer="抱歉，查询知识库时出现错误",
            error=str(e)
        )


@app.get("/knowledge-base/config", tags=["knowledge_base"])
async def get_knowledge_base_config():
    """获取知识库配置状态"""
    try:
        kb_config_manager = agent_factory.get_knowledge_base_config_manager()
        if not kb_config_manager:
            raise HTTPException(status_code=503, detail="配置管理器不可用")

        config = get_unified_config().get_config()

        return {
            "status": "success",
            "data": {
                "enabled": config.enabled,
                "features": config.features,
                "chroma_db": config.chroma_db,
                "document_processing": config.document_processing
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取配置失败")


@app.post("/knowledge-base/toggle", tags=["knowledge_base"])
async def toggle_knowledge_base(request: ConfigToggleRequest):
    """切换知识库功能开关"""
    try:
        kb_config_manager = agent_factory.get_knowledge_base_config_manager()
        if not kb_config_manager:
            raise HTTPException(status_code=503, detail="配置管理器不可用")

        if request.enabled:
            success = get_unified_config().enable_knowledge_base()
            action = "启用"
        else:
            success = get_unified_config().disable_knowledge_base()
            action = "禁用"

        if success:
            logger.info(f"知识库功能{action}成功: {request.reason or '无原因'}")
            return {
                "status": "success",
                "message": f"知识库功能已{action}",
                "enabled": request.enabled
            }
        else:
            raise HTTPException(status_code=500, detail=f"知识库功能{action}失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换知识库功能失败: {e}")
        raise HTTPException(status_code=500, detail="操作失败")


@app.get("/knowledge-base/status", tags=["knowledge_base"])
async def get_knowledge_base_status():
    """获取知识库状态"""
    try:
        # 获取知识库相关组件的状态
        kb_config_manager = agent_factory.get_knowledge_base_config_manager()
        rag_agent = agent_factory.get_rag_knowledge_base_agent()
        kb_manager = agent_factory.get_knowledge_base_manager()

        # 获取知识库统计
        kb_stats = {}
        if kb_manager:
            try:
                kb_stats = kb_manager.get_statistics()
            except Exception as e:
                kb_stats = {"error": str(e)}

        return {
            "status": "success",
            "data": {
                "components": {
                    "kb_config_manager": kb_config_manager is not None,
                    "rag_agent": rag_agent is not None,
                    "kb_manager": kb_manager is not None
                },
                "knowledge_base": {
                    "enabled": kb_config_manager.is_knowledge_base_enabled() if kb_config_manager else False,
                    "statistics": kb_stats
                },
                "architecture": "SimplifiedDecisionEngine + ActionExecutor"
            }
        }

    except Exception as e:
        logger.error(f"获取知识库状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取状态失败")


@app.get("/safety/status", response_model=SafetyStatusResponse, tags=["safety"])
async def get_safety_status():
    """获取安全管理器状态"""
    try:
        from backend.utils.safety_manager import get_safety_manager
        safety_manager = get_safety_manager()

        status = safety_manager.get_safety_status()

        return SafetyStatusResponse(
            safety_level=status["safety_level"],
            metrics=status["metrics"],
            recent_errors=status["recent_errors"],
            kb_enabled=status["kb_enabled"],
            auto_recovery_enabled=status["auto_recovery_enabled"]
        )

    except Exception as e:
        logger.error(f"获取安全状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取安全状态失败")


@app.post("/safety/reset", tags=["safety"])
async def reset_safety_metrics():
    """重置安全指标"""
    try:
        from backend.utils.safety_manager import get_safety_manager
        safety_manager = get_safety_manager()

        safety_manager.reset_metrics()

        return {
            "status": "success",
            "message": "安全指标已重置",
            "timestamp": datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"重置安全指标失败: {e}")
        raise HTTPException(status_code=500, detail="重置安全指标失败")


@app.post("/safety/force-degradation", tags=["safety"])
async def force_safety_degradation(level: str = "degraded", reason: str = "手动触发"):
    """强制安全降级"""
    try:
        from backend.utils.safety_manager import get_safety_manager, SafetyLevel
        safety_manager = get_safety_manager()

        # 验证安全级别
        valid_levels = {
            "warning": SafetyLevel.WARNING,
            "degraded": SafetyLevel.DEGRADED,
            "emergency": SafetyLevel.EMERGENCY
        }

        if level not in valid_levels:
            raise HTTPException(status_code=400, detail="无效的安全级别")

        safety_manager.force_degradation(valid_levels[level], reason)

        return {
            "status": "success",
            "message": f"已强制降级到 {level} 级别",
            "reason": reason,
            "timestamp": datetime.datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制安全降级失败: {e}")
        raise HTTPException(status_code=500, detail="强制降级失败")


@app.post("/safety/force-recovery", tags=["safety"])
async def force_safety_recovery(reason: str = "手动恢复"):
    """强制安全恢复"""
    try:
        from backend.utils.safety_manager import get_safety_manager
        safety_manager = get_safety_manager()

        safety_manager.force_recovery(reason)

        return {
            "status": "success",
            "message": "已强制恢复到正常状态",
            "reason": reason,
            "timestamp": datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"强制安全恢复失败: {e}")
        raise HTTPException(status_code=500, detail="强制恢复失败")


@app.post("/knowledge-base/documents", tags=["knowledge_base"])
async def ingest_document(request: DocumentIngestionRequest):
    """摄入文档到知识库"""
    try:
        kb_manager = agent_factory.get_knowledge_base_manager()
        if not kb_manager:
            raise HTTPException(status_code=503, detail="知识库管理器不可用")

        result = await kb_manager.ingest_document(
            content=request.content,
            source_path=request.source_path,
            metadata=request.metadata
        )

        return {
            "status": "success" if result.success else "error",
            "data": {
                "doc_id": result.doc_id,
                "chunks_processed": result.chunks_processed,
                "processing_time": result.processing_time,
                "error": result.error,
                "warnings": result.warnings
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档摄入失败: {e}")
        raise HTTPException(status_code=500, detail="文档摄入失败")


@app.get("/knowledge-base/documents", tags=["knowledge_base"])
async def list_documents(role: Optional[str] = None, category: Optional[str] = None):
    """列出知识库文档"""
    try:
        kb_manager = agent_factory.get_knowledge_base_manager()
        if not kb_manager:
            raise HTTPException(status_code=503, detail="知识库管理器不可用")

        documents = kb_manager.list_documents(role=role, category=category)

        # 转换为可序列化的格式
        doc_list = []
        for doc in documents:
            doc_list.append({
                "doc_id": doc.doc_id,
                "title": doc.title,
                "source_path": doc.source_path,
                "role": doc.role,
                "category": doc.category,
                "tags": doc.tags,
                "created_at": doc.created_at.isoformat(),
                "updated_at": doc.updated_at.isoformat(),
                "version": doc.version,
                "chunk_count": doc.chunk_count
            })

        return {
            "status": "success",
            "data": {
                "documents": doc_list,
                "total_count": len(doc_list),
                "filters": {
                    "role": role,
                    "category": category
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出文档失败: {e}")
        raise HTTPException(status_code=500, detail="列出文档失败")


@app.get("/knowledge-base/statistics", tags=["knowledge_base"])
async def get_knowledge_base_statistics():
    """获取知识库统计信息"""
    try:
        kb_manager = agent_factory.get_knowledge_base_manager()
        if not kb_manager:
            raise HTTPException(status_code=503, detail="知识库管理器不可用")

        stats = kb_manager.get_statistics()

        return {
            "status": "success",
            "data": stats,
            "timestamp": datetime.datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")

@app.post("/performance/save", tags=["system"])
async def save_performance_report():
    """保存性能报告"""
    try:
        from backend.utils.performance_middleware import save_performance_report
        file_path = save_performance_report()
        return {
            "status": "success",
            "message": "性能报告已保存",
            "file_path": file_path,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"保存性能报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail="保存性能报告失败")


@app.get("/config/status")
async def config_status():
    """配置状态检查端点"""
    from backend.config.preloader import config_preloader
    import datetime as dt

    preload_status = config_preloader.get_preload_status()
    config_status = get_unified_config().get_config_status()

    return {
        "preload_status": preload_status,
        "config_manager_status": config_status,
        "timestamp": dt.datetime.now().isoformat()
    }


if __name__ == "__main__":
    import uvicorn
    logger.info("启动FastAPI服务器...")
    uvicorn.run(
        "backend.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["backend"],
        log_level="info",
        access_log=True
    )
