#!/usr/bin/env python3
"""
阶段4系统稳定性测试：验证长时间运行的稳定性

目标：确保系统在各种条件下都能稳定运行
"""

import sys
import os
import time
import gc
import threading
import random
from pathlib import Path
from typing import List, Dict, Any
import psutil

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

class SystemStabilityTester:
    """系统稳定性测试器"""
    
    def __init__(self):
        """初始化稳定性测试器"""
        self.test_results = {}
        self.is_running = True
        
    def test_memory_leak(self, duration_seconds: int = 30) -> Dict[str, Any]:
        """测试内存泄漏"""
        print(f"🧪 开始内存泄漏测试 (持续{duration_seconds}秒)\n")
        
        try:
            from backend.utils.intent_manager import IntentManager
            from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
            
            process = psutil.Process()
            memory_samples = []
            start_time = time.time()
            
            print("📋 内存使用情况监控:")
            
            # 初始内存
            gc.collect()
            initial_memory = process.memory_info().rss / 1024 / 1024
            memory_samples.append(initial_memory)
            print(f"   初始内存: {initial_memory:.2f}MB")
            
            # 持续创建和销毁对象
            iteration = 0
            while time.time() - start_time < duration_seconds:
                iteration += 1
                
                # 创建多个实例
                managers = [IntentManager() for _ in range(5)]
                engines = [SimplifiedDecisionEngine() for _ in range(3)]
                
                # 执行一些操作
                for manager in managers:
                    manager.get_valid_intents()
                    manager.is_valid_intent("greeting")
                    manager.get_config_info()
                
                # 清理引用
                del managers
                del engines
                
                # 每5秒记录一次内存
                if iteration % 50 == 0:
                    gc.collect()
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_samples.append(current_memory)
                    elapsed = time.time() - start_time
                    print(f"   {elapsed:.1f}s: {current_memory:.2f}MB (+{current_memory-initial_memory:.2f}MB)")
                
                time.sleep(0.1)  # 短暂休息
            
            # 最终内存检查
            gc.collect()
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_samples.append(final_memory)
            
            # 分析内存趋势
            memory_increase = final_memory - initial_memory
            max_memory = max(memory_samples)
            avg_memory = sum(memory_samples) / len(memory_samples)
            
            # 判断是否有内存泄漏（增长超过20MB认为有问题）
            has_leak = memory_increase > 20.0
            
            result = {
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "max_memory_mb": max_memory,
                "avg_memory_mb": avg_memory,
                "memory_increase_mb": memory_increase,
                "memory_samples": memory_samples,
                "iterations": iteration,
                "duration_seconds": duration_seconds,
                "has_memory_leak": has_leak,
                "leak_threshold_mb": 20.0
            }
            
            print(f"\n📊 内存泄漏测试结果:")
            print(f"   最终内存: {final_memory:.2f}MB")
            print(f"   内存增长: {memory_increase:.2f}MB")
            print(f"   最大内存: {max_memory:.2f}MB")
            print(f"   平均内存: {avg_memory:.2f}MB")
            print(f"   执行迭代: {iteration} 次")
            print(f"   {'❌ 检测到内存泄漏' if has_leak else '✅ 无明显内存泄漏'}")
            
            return result
            
        except Exception as e:
            print(f"❌ 内存泄漏测试失败: {e}")
            return {"error": str(e)}
    
    def test_concurrent_stability(self, thread_count: int = 10, duration_seconds: int = 30) -> Dict[str, Any]:
        """测试并发稳定性"""
        print(f"🧪 开始并发稳定性测试 ({thread_count}线程, 持续{duration_seconds}秒)\n")
        
        try:
            from backend.utils.intent_manager import IntentManager
            from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
            
            results = {
                "thread_results": [],
                "errors": [],
                "total_operations": 0,
                "successful_operations": 0
            }
            
            def worker_thread(thread_id: int):
                """工作线程函数"""
                thread_results = {
                    "thread_id": thread_id,
                    "operations": 0,
                    "errors": 0,
                    "start_time": time.time()
                }
                
                try:
                    # 每个线程创建自己的实例
                    manager = IntentManager()
                    engine = SimplifiedDecisionEngine()
                    
                    test_intents = ["greeting", "business_requirement", "domain_specific_query", "general_chat"]
                    
                    while time.time() - thread_results["start_time"] < duration_seconds:
                        try:
                            # 随机执行不同操作
                            operation = random.choice([
                                lambda: manager.get_valid_intents(),
                                lambda: manager.is_valid_intent(random.choice(test_intents)),
                                lambda: manager.get_config_info(),
                                lambda: engine._validate_intent_configuration() if hasattr(engine, '_validate_intent_configuration') else None
                            ])
                            
                            operation()
                            thread_results["operations"] += 1
                            
                        except Exception as e:
                            thread_results["errors"] += 1
                            results["errors"].append(f"Thread {thread_id}: {str(e)}")
                        
                        time.sleep(random.uniform(0.01, 0.1))  # 随机间隔
                
                except Exception as e:
                    results["errors"].append(f"Thread {thread_id} initialization error: {str(e)}")
                
                thread_results["duration"] = time.time() - thread_results["start_time"]
                thread_results["ops_per_second"] = thread_results["operations"] / thread_results["duration"] if thread_results["duration"] > 0 else 0
                
                return thread_results
            
            # 启动所有线程
            print("📋 启动并发测试线程:")
            threads = []
            thread_results = []
            
            for i in range(thread_count):
                thread = threading.Thread(target=lambda tid=i: thread_results.append(worker_thread(tid)))
                threads.append(thread)
                thread.start()
                print(f"   启动线程 {i}")
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 汇总结果
            results["thread_results"] = thread_results
            results["total_operations"] = sum(tr["operations"] for tr in thread_results)
            results["successful_operations"] = results["total_operations"] - sum(tr["errors"] for tr in thread_results)
            results["total_errors"] = sum(tr["errors"] for tr in thread_results)
            results["success_rate"] = (results["successful_operations"] / results["total_operations"]) * 100 if results["total_operations"] > 0 else 0
            results["avg_ops_per_second"] = sum(tr["ops_per_second"] for tr in thread_results) / len(thread_results) if thread_results else 0
            
            print(f"\n📊 并发稳定性测试结果:")
            print(f"   总操作数: {results['total_operations']}")
            print(f"   成功操作: {results['successful_operations']}")
            print(f"   错误数量: {results['total_errors']}")
            print(f"   成功率: {results['success_rate']:.2f}%")
            print(f"   平均操作/秒: {results['avg_ops_per_second']:.2f}")
            
            # 显示每个线程的结果
            print(f"\n📋 各线程执行情况:")
            for tr in thread_results:
                print(f"   线程{tr['thread_id']}: {tr['operations']}操作, {tr['errors']}错误, {tr['ops_per_second']:.1f}ops/s")
            
            if results["errors"]:
                print(f"\n⚠️ 发现的错误:")
                for error in results["errors"][:5]:  # 只显示前5个错误
                    print(f"   - {error}")
                if len(results["errors"]) > 5:
                    print(f"   ... 还有 {len(results['errors']) - 5} 个错误")
            
            # 判断稳定性（成功率95%以上认为稳定）
            is_stable = results["success_rate"] >= 95.0
            print(f"\n{'✅ 并发稳定性良好' if is_stable else '❌ 并发稳定性存在问题'}")
            
            results["is_stable"] = is_stable
            return results
            
        except Exception as e:
            print(f"❌ 并发稳定性测试失败: {e}")
            return {"error": str(e)}
    
    def test_configuration_resilience(self) -> Dict[str, Any]:
        """测试配置弹性"""
        print("🧪 开始配置弹性测试\n")
        
        try:
            from backend.utils.intent_manager import IntentManager
            from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
            
            results = {
                "tests": [],
                "passed": 0,
                "failed": 0
            }
            
            # 测试1: 正常配置加载
            print("📋 测试正常配置加载:")
            try:
                manager = IntentManager()
                intents = manager.get_valid_intents()
                
                test_result = {
                    "test": "normal_config_loading",
                    "passed": len(intents) > 0,
                    "details": f"加载了 {len(intents)} 个意图"
                }
                results["tests"].append(test_result)
                
                if test_result["passed"]:
                    results["passed"] += 1
                    print(f"   ✅ {test_result['details']}")
                else:
                    results["failed"] += 1
                    print(f"   ❌ 配置加载失败")
                    
            except Exception as e:
                results["tests"].append({
                    "test": "normal_config_loading",
                    "passed": False,
                    "error": str(e)
                })
                results["failed"] += 1
                print(f"   ❌ 配置加载异常: {e}")
            
            # 测试2: 决策引擎配置集成
            print(f"\n📋 测试决策引擎配置集成:")
            try:
                engine = SimplifiedDecisionEngine()
                has_intent_manager = engine.intent_manager is not None
                
                test_result = {
                    "test": "decision_engine_integration",
                    "passed": has_intent_manager,
                    "details": f"IntentManager集成: {'成功' if has_intent_manager else '失败'}"
                }
                results["tests"].append(test_result)
                
                if test_result["passed"]:
                    results["passed"] += 1
                    print(f"   ✅ {test_result['details']}")
                else:
                    results["failed"] += 1
                    print(f"   ❌ {test_result['details']}")
                    
            except Exception as e:
                results["tests"].append({
                    "test": "decision_engine_integration",
                    "passed": False,
                    "error": str(e)
                })
                results["failed"] += 1
                print(f"   ❌ 决策引擎集成异常: {e}")
            
            # 测试3: 模板同步状态
            print(f"\n📋 测试模板同步状态:")
            try:
                from backend.utils.template_synchronizer import TemplateSynchronizer
                
                synchronizer = TemplateSynchronizer()
                is_synced, differences = synchronizer.validate_template_sync()
                
                test_result = {
                    "test": "template_synchronization",
                    "passed": is_synced,
                    "details": f"模板同步: {'正常' if is_synced else '存在差异'}",
                    "differences": differences
                }
                results["tests"].append(test_result)
                
                if test_result["passed"]:
                    results["passed"] += 1
                    print(f"   ✅ {test_result['details']}")
                else:
                    results["failed"] += 1
                    print(f"   ❌ {test_result['details']}")
                    if differences:
                        for diff in differences:
                            print(f"      - {diff}")
                            
            except Exception as e:
                results["tests"].append({
                    "test": "template_synchronization",
                    "passed": False,
                    "error": str(e)
                })
                results["failed"] += 1
                print(f"   ❌ 模板同步测试异常: {e}")
            
            # 测试4: 重复初始化稳定性
            print(f"\n📋 测试重复初始化稳定性:")
            try:
                initialization_count = 10
                successful_inits = 0
                
                for i in range(initialization_count):
                    try:
                        manager = IntentManager()
                        engine = SimplifiedDecisionEngine()
                        # 执行一些基本操作
                        manager.get_valid_intents()
                        successful_inits += 1
                    except Exception:
                        pass
                
                success_rate = (successful_inits / initialization_count) * 100
                test_passed = success_rate >= 90.0
                
                test_result = {
                    "test": "repeated_initialization",
                    "passed": test_passed,
                    "details": f"重复初始化成功率: {success_rate:.1f}% ({successful_inits}/{initialization_count})"
                }
                results["tests"].append(test_result)
                
                if test_result["passed"]:
                    results["passed"] += 1
                    print(f"   ✅ {test_result['details']}")
                else:
                    results["failed"] += 1
                    print(f"   ❌ {test_result['details']}")
                    
            except Exception as e:
                results["tests"].append({
                    "test": "repeated_initialization",
                    "passed": False,
                    "error": str(e)
                })
                results["failed"] += 1
                print(f"   ❌ 重复初始化测试异常: {e}")
            
            # 计算总体结果
            total_tests = results["passed"] + results["failed"]
            overall_success_rate = (results["passed"] / total_tests) * 100 if total_tests > 0 else 0
            results["overall_success_rate"] = overall_success_rate
            results["is_resilient"] = overall_success_rate >= 90.0
            
            print(f"\n📊 配置弹性测试结果:")
            print(f"   通过测试: {results['passed']}")
            print(f"   失败测试: {results['failed']}")
            print(f"   成功率: {overall_success_rate:.1f}%")
            print(f"   {'✅ 配置弹性良好' if results['is_resilient'] else '❌ 配置弹性存在问题'}")
            
            return results
            
        except Exception as e:
            print(f"❌ 配置弹性测试失败: {e}")
            return {"error": str(e)}

def main():
    """主测试函数"""
    print("🚀 开始阶段4系统稳定性测试\n")
    
    tester = SystemStabilityTester()
    all_results = {}
    
    # 测试1: 内存泄漏测试
    print("=== 测试1: 内存泄漏测试 ===")
    memory_leak_results = tester.test_memory_leak(duration_seconds=30)
    all_results["memory_leak"] = memory_leak_results
    
    print("\n" + "="*60 + "\n")
    
    # 测试2: 并发稳定性测试
    print("=== 测试2: 并发稳定性测试 ===")
    concurrent_stability_results = tester.test_concurrent_stability(thread_count=8, duration_seconds=20)
    all_results["concurrent_stability"] = concurrent_stability_results
    
    print("\n" + "="*60 + "\n")
    
    # 测试3: 配置弹性测试
    print("=== 测试3: 配置弹性测试 ===")
    config_resilience_results = tester.test_configuration_resilience()
    all_results["config_resilience"] = config_resilience_results
    
    # 总结测试结果
    print("\n" + "="*60)
    print("🏁 系统稳定性测试总结")
    
    # 判断稳定性测试是否通过
    stability_indicators = []
    
    # 检查内存泄漏
    if "memory_leak" in all_results and "has_memory_leak" in all_results["memory_leak"]:
        no_memory_leak = not all_results["memory_leak"]["has_memory_leak"]
        stability_indicators.append(no_memory_leak)
        print(f"{'✅' if no_memory_leak else '❌'} 内存泄漏: {'无明显泄漏' if no_memory_leak else '检测到泄漏'}")
    
    # 检查并发稳定性
    if "concurrent_stability" in all_results and "is_stable" in all_results["concurrent_stability"]:
        concurrent_stable = all_results["concurrent_stability"]["is_stable"]
        stability_indicators.append(concurrent_stable)
        success_rate = all_results["concurrent_stability"]["success_rate"]
        print(f"{'✅' if concurrent_stable else '❌'} 并发稳定性: {success_rate:.1f}% 成功率")
    
    # 检查配置弹性
    if "config_resilience" in all_results and "is_resilient" in all_results["config_resilience"]:
        config_resilient = all_results["config_resilience"]["is_resilient"]
        stability_indicators.append(config_resilient)
        resilience_rate = all_results["config_resilience"]["overall_success_rate"]
        print(f"{'✅' if config_resilient else '❌'} 配置弹性: {resilience_rate:.1f}% 成功率")
    
    overall_stability_good = all(stability_indicators) if stability_indicators else False
    
    if overall_stability_good:
        print("\n🎉 系统稳定性测试通过！")
        print("\n📋 稳定性验证状态:")
        print("- [x] 无明显内存泄漏")
        print("- [x] 并发稳定性良好")
        print("- [x] 配置弹性正常")
        print("- [x] 重复初始化稳定")
        print("\n🎯 系统稳定性验证完成")
    else:
        print("\n⚠️ 系统稳定性测试存在问题")
        print("建议检查失败项目并修复后重新测试")
    
    return overall_stability_good

if __name__ == "__main__":
    main()
