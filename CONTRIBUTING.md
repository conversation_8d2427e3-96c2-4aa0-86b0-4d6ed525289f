# 贡献指南

感谢您对智能需求采集系统的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug 报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献
- 🧪 测试用例

## 🚀 快速开始

### 环境准备

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 Fork 项目到您的账户
   # 然后克隆到本地
   git clone https://github.com/YOUR_USERNAME/requirement-collection-system.git
   cd requirement-collection-system
   ```

2. **设置开发环境**
   ```bash
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   
   # 安装依赖
   pip install -r requirements.txt
   
   # 前端环境
   cd frontend
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，添加必要的配置
   ```

## 📋 贡献流程

### 1. 创建分支
```bash
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/your-bug-fix
```

### 2. 开发规范

#### 代码风格
- **Python**: 遵循 PEP 8 规范
- **JavaScript/TypeScript**: 使用 ESLint 和 Prettier
- **提交信息**: 使用约定式提交格式

#### 提交信息格式
```
type(scope): description

[optional body]

[optional footer]
```

类型说明：
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(api): add user authentication endpoint

Add JWT-based authentication for API endpoints
- Implement login/logout functionality
- Add middleware for token validation
- Update API documentation

Closes #123
```

### 3. 测试

在提交代码前，请确保：

```bash
# 运行后端测试
cd backend
python -m pytest

# 运行前端测试
cd frontend
npm test

# 运行集成测试
python scripts/comprehensive_system_test.py
```

### 4. 提交和推送

```bash
git add .
git commit -m "feat(scope): your commit message"
git push origin feature/your-feature-name
```

### 5. 创建 Pull Request

1. 在 GitHub 上创建 Pull Request
2. 填写 PR 模板中的信息
3. 等待代码审查
4. 根据反馈进行修改

## 🐛 Bug 报告

使用 GitHub Issues 报告 Bug，请包含：

- **环境信息**: 操作系统、Python 版本、Node.js 版本
- **重现步骤**: 详细的操作步骤
- **期望行为**: 您期望发生什么
- **实际行为**: 实际发生了什么
- **错误日志**: 相关的错误信息
- **截图**: 如果适用

## 💡 功能建议

我们欢迎新功能建议！请通过 GitHub Issues 提交，包含：

- **功能描述**: 详细描述建议的功能
- **使用场景**: 什么情况下会用到这个功能
- **预期收益**: 这个功能能带来什么价值
- **实现思路**: 如果有想法，可以简单描述

## 📝 文档贡献

文档改进包括：

- 修复错别字和语法错误
- 改进现有文档的清晰度
- 添加缺失的文档
- 翻译文档

文档位于 `docs/` 目录，使用 Markdown 格式。

## 🔧 代码贡献指南

### 架构原则

1. **模块化设计**: 保持模块间的低耦合
2. **配置驱动**: 业务逻辑与配置分离
3. **性能优先**: 考虑性能影响
4. **可测试性**: 编写可测试的代码

### 关键组件

- **三层识别系统**: 关键词 → 语义 → LLM 识别
- **Handler 模式**: 统一的动作处理架构
- **状态管理**: 基于会话状态的流程控制
- **配置系统**: 分层的配置管理

### 开发工具

项目提供了丰富的开发工具：

```bash
# 代码质量检查
python tools/analyze_code_issues.py

# 架构检查
python tools/architecture_checker.py

# 性能测试
python scripts/performance_test.py

# 健康检查
python scripts/project_health_checker.py
```

## 🧪 测试指南

### 测试类型

1. **单元测试**: 测试单个函数或类
2. **集成测试**: 测试组件间的交互
3. **端到端测试**: 测试完整的用户流程
4. **性能测试**: 测试系统性能

### 测试覆盖率

我们要求：
- 新功能的测试覆盖率 ≥ 80%
- 关键业务逻辑的测试覆盖率 ≥ 90%

## 📞 获取帮助

如果您在贡献过程中遇到问题：

1. 查看 [文档](docs/)
2. 搜索现有的 [Issues](https://github.com/your-org/requirement-collection-system/issues)
3. 创建新的 Issue 寻求帮助
4. 参与 [Discussions](https://github.com/your-org/requirement-collection-system/discussions)

## 🎉 贡献者

感谢所有为项目做出贡献的开发者！

## 📄 许可证

通过贡献代码，您同意您的贡献将在 MIT 许可证下发布。

---

再次感谢您的贡献！每一个贡献都让这个项目变得更好。 🚀
