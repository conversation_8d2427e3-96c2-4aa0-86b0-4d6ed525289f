#!/usr/bin/env python3
"""
重构指标测量工具

功能：
1. 测量重构前后的代码指标
2. 跟踪重构进度
3. 生成重构效果报告
4. 监控质量变化
"""

import json
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, Any


class RefactorMetrics:
    """重构指标测量器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backend_root = self.project_root / "backend"
        self.metrics_history_file = self.project_root / "docs" / "refactor_metrics_history.json"
        
    def measure_current_metrics(self) -> Dict[str, Any]:
        """测量当前项目指标"""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "code_metrics": self._measure_code_metrics(),
            "file_metrics": self._measure_file_metrics(),
            "quality_metrics": self._measure_quality_metrics(),
            "dependency_metrics": self._measure_dependency_metrics()
        }
        
        return metrics
    
    def _measure_code_metrics(self) -> Dict[str, int]:
        """测量代码指标"""
        total_lines = 0
        total_files = 0
        
        # 统计Python文件
        python_files = list(self.backend_root.glob("**/*.py"))
        total_files = len(python_files)
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    total_lines += lines
            except:
                continue
        
        return {
            "total_lines": total_lines,
            "total_files": total_files,
            "avg_lines_per_file": total_lines // total_files if total_files > 0 else 0
        }
    
    def _measure_file_metrics(self) -> Dict[str, Any]:
        """测量文件指标"""
        file_sizes = []
        large_files = []
        
        for py_file in self.backend_root.glob("**/*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    file_sizes.append(lines)
                    
                    if lines > 500:
                        large_files.append({
                            "path": str(py_file.relative_to(self.backend_root)),
                            "lines": lines
                        })
            except:
                continue
        
        file_sizes.sort(reverse=True)
        
        return {
            "largest_file_lines": file_sizes[0] if file_sizes else 0,
            "large_files_count": len(large_files),
            "large_files": large_files[:10],  # 只保存前10个
            "median_file_size": file_sizes[len(file_sizes)//2] if file_sizes else 0
        }
    
    def _measure_quality_metrics(self) -> Dict[str, Any]:
        """测量代码质量指标"""
        metrics = {
            "test_coverage": self._get_test_coverage(),
            "static_analysis": self._run_static_analysis(),
            "complexity_score": self._calculate_complexity()
        }
        
        return metrics
    
    def _measure_dependency_metrics(self) -> Dict[str, int]:
        """测量依赖指标"""
        # 运行依赖分析器获取数据
        try:
            from tools.analyze_dependencies import analyze_project_dependencies
            dep_report = analyze_project_dependencies(str(self.project_root))
            
            return {
                "total_dependencies": dep_report["summary"]["total_dependencies"],
                "avg_dependencies": round(dep_report["summary"]["avg_dependencies_per_module"], 2),
                "high_dependency_modules": len(dep_report["high_dependency_modules"]),
                "circular_dependencies": len(dep_report["circular_dependencies"])
            }
        except:
            return {
                "total_dependencies": 0,
                "avg_dependencies": 0,
                "high_dependency_modules": 0,
                "circular_dependencies": 0
            }
    
    def _get_test_coverage(self) -> float:
        """获取测试覆盖率"""
        try:
            # 运行pytest获取覆盖率
            result = subprocess.run([
                "python", "-m", "pytest", 
                "--cov=backend", 
                "--cov-report=json",
                "--cov-report=term-missing",
                "backend/tests/"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # 读取coverage.json
            coverage_file = self.project_root / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file, 'r') as f:
                    coverage_data = json.load(f)
                    return round(coverage_data.get("totals", {}).get("percent_covered", 0), 2)
        except:
            pass
        
        return 0.0
    
    def _run_static_analysis(self) -> Dict[str, int]:
        """运行静态分析"""
        metrics = {
            "flake8_issues": 0,
            "mypy_errors": 0
        }
        
        try:
            # 运行flake8
            result = subprocess.run([
                "python", "-m", "flake8", 
                "backend/", 
                "--count",
                "--max-line-length=100"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.isdigit():
                        metrics["flake8_issues"] = int(line)
                        break
        except:
            pass
        
        return metrics
    
    def _calculate_complexity(self) -> float:
        """计算代码复杂度评分"""
        # 简单的复杂度评分算法
        file_metrics = self._measure_file_metrics()
        code_metrics = self._measure_code_metrics()
        
        # 基于文件大小分布计算复杂度
        if code_metrics["total_files"] == 0:
            return 0.0
        
        complexity_score = 0.0
        
        # 大文件惩罚
        large_files_ratio = file_metrics["large_files_count"] / code_metrics["total_files"]
        complexity_score += large_files_ratio * 50
        
        # 平均文件大小惩罚
        avg_size_penalty = min(code_metrics["avg_lines_per_file"] / 200, 1.0) * 30
        complexity_score += avg_size_penalty
        
        # 最大文件大小惩罚
        max_file_penalty = min(file_metrics["largest_file_lines"] / 1000, 1.0) * 20
        complexity_score += max_file_penalty
        
        return round(complexity_score, 2)
    
    def save_metrics(self, metrics: Dict[str, Any]):
        """保存指标到历史记录"""
        history = []
        
        # 读取现有历史
        if self.metrics_history_file.exists():
            try:
                with open(self.metrics_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            except:
                history = []
        
        # 添加新指标
        history.append(metrics)
        
        # 只保留最近50次记录
        history = history[-50:]
        
        # 保存到文件
        self.metrics_history_file.parent.mkdir(exist_ok=True)
        with open(self.metrics_history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    
    def generate_progress_report(self) -> Dict[str, Any]:
        """生成重构进度报告"""
        current_metrics = self.measure_current_metrics()
        
        # 读取历史数据
        history = []
        if self.metrics_history_file.exists():
            try:
                with open(self.metrics_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            except:
                pass
        
        if not history:
            return {
                "current_metrics": current_metrics,
                "progress": "无历史数据，这是首次测量"
            }
        
        # 与最早的记录比较（重构开始时的基线）
        baseline = history[0]
        
        # 计算改进
        improvements = {
            "files_reduced": baseline["code_metrics"]["total_files"] - current_metrics["code_metrics"]["total_files"],
            "lines_reduced": baseline["code_metrics"]["total_lines"] - current_metrics["code_metrics"]["total_lines"],
            "large_files_reduced": baseline["file_metrics"]["large_files_count"] - current_metrics["file_metrics"]["large_files_count"],
            "complexity_improved": baseline["quality_metrics"]["complexity_score"] - current_metrics["quality_metrics"]["complexity_score"]
        }
        
        # 计算百分比变化
        percentages = {}
        if baseline["code_metrics"]["total_files"] > 0:
            percentages["files_reduction_percent"] = round(
                improvements["files_reduced"] / baseline["code_metrics"]["total_files"] * 100, 2
            )
        
        if baseline["code_metrics"]["total_lines"] > 0:
            percentages["lines_reduction_percent"] = round(
                improvements["lines_reduced"] / baseline["code_metrics"]["total_lines"] * 100, 2
            )
        
        return {
            "current_metrics": current_metrics,
            "baseline_metrics": baseline,
            "improvements": improvements,
            "percentages": percentages,
            "progress_summary": self._generate_progress_summary(improvements, percentages)
        }
    
    def _generate_progress_summary(self, improvements: Dict, percentages: Dict) -> str:
        """生成进度摘要"""
        summary_parts = []
        
        if improvements.get("files_reduced", 0) > 0:
            summary_parts.append(f"删除了 {improvements['files_reduced']} 个文件")
        
        if improvements.get("lines_reduced", 0) > 0:
            summary_parts.append(f"减少了 {improvements['lines_reduced']} 行代码")
        
        if improvements.get("large_files_reduced", 0) > 0:
            summary_parts.append(f"减少了 {improvements['large_files_reduced']} 个大文件")
        
        if percentages.get("lines_reduction_percent", 0) > 0:
            summary_parts.append(f"代码量减少 {percentages['lines_reduction_percent']}%")
        
        if not summary_parts:
            return "尚未开始重构或暂无改进"
        
        return "；".join(summary_parts)


def main():
    """主函数"""
    project_root = "/Users/<USER>/由己ai项目/需求采集项目"
    
    print("📊 重构指标测量工具")
    print("=" * 50)
    
    metrics_tool = RefactorMetrics(project_root)
    
    # 测量当前指标
    print("🔍 测量当前项目指标...")
    current_metrics = metrics_tool.measure_current_metrics()
    
    # 保存指标
    metrics_tool.save_metrics(current_metrics)
    print("✅ 指标已保存到历史记录")
    
    # 生成进度报告
    print("\n📈 生成重构进度报告...")
    progress_report = metrics_tool.generate_progress_report()
    
    # 输出当前指标
    code_metrics = current_metrics["code_metrics"]
    file_metrics = current_metrics["file_metrics"] 
    quality_metrics = current_metrics["quality_metrics"]
    
    print(f"\n📊 当前项目指标:")
    print(f"总文件数: {code_metrics['total_files']}")
    print(f"总代码行数: {code_metrics['total_lines']:,}")
    print(f"平均文件大小: {code_metrics['avg_lines_per_file']} 行")
    print(f"最大文件大小: {file_metrics['largest_file_lines']} 行")
    print(f"大文件数量 (>500行): {file_metrics['large_files_count']}")
    print(f"复杂度评分: {quality_metrics['complexity_score']}")
    print(f"测试覆盖率: {quality_metrics['test_coverage']}%")
    
    # 输出进度信息
    if "progress_summary" in progress_report:
        print(f"\n📈 重构进度:")
        print(f"{progress_report['progress_summary']}")
    
    # 输出最大的文件
    if file_metrics["large_files"]:
        print(f"\n📁 最大的文件:")
        for file_info in file_metrics["large_files"][:5]:
            print(f"  {file_info['path']}: {file_info['lines']} 行")
    
    print("=" * 50)


if __name__ == "__main__":
    main()