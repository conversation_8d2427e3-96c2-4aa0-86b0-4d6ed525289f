#!/usr/bin/env python3
"""
文件删除安全验证工具

功能：
1. 验证文件删除的安全性
2. 检查文件依赖关系
3. 生成安全删除建议
4. 提供回滚机制
"""

import ast
from pathlib import Path
from typing import List, Dict


class DeletionValidator:
    """文件删除安全验证器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backend_root = self.project_root / "backend"
        
    def validate_safe_deletion(self, file_paths: List[str]) -> Dict[str, any]:
        """验证文件删除的安全性"""
        results = {
            "safe_to_delete": [],
            "risky_deletions": [],
            "blocked_deletions": [],
            "warnings": []
        }
        
        for file_path in file_paths:
            safety_level = self._check_file_safety(file_path)
            
            if safety_level == "safe":
                results["safe_to_delete"].append(file_path)
            elif safety_level == "risky":
                results["risky_deletions"].append(file_path)
            else:
                results["blocked_deletions"].append(file_path)
        
        return results
    
    def _check_file_safety(self, file_path: str) -> str:
        """检查单个文件的删除安全性"""
        file_path = Path(file_path)
        
        # 检查文件是否存在
        if not file_path.exists():
            return "safe"  # 文件不存在，安全删除
        
        # 检查是否为关键文件
        if self._is_critical_file(file_path):
            return "blocked"
        
        # 检查依赖关系
        dependencies = self._find_dependencies(file_path)
        if dependencies:
            return "risky"
        
        return "safe"
    
    def _is_critical_file(self, file_path: Path) -> bool:
        """判断是否为关键文件"""
        critical_patterns = [
            "main.py",
            "run_aip.py", 
            "__init__.py",
            "config.py",
            "settings.py"
        ]
        
        # 检查文件名
        for pattern in critical_patterns:
            if pattern in file_path.name:
                return True
        
        # 检查路径
        critical_paths = ["api", "config", "data/db"]
        for critical_path in critical_paths:
            if critical_path in str(file_path):
                return True
        
        return False
    
    def _find_dependencies(self, file_path: Path) -> List[str]:
        """查找文件的依赖关系"""
        dependencies = []
        
        # 确保路径是绝对路径
        if not file_path.is_absolute():
            file_path = self.project_root / file_path
        
        # 检查文件是否在backend目录下
        try:
            relative_path = file_path.relative_to(self.backend_root)
            module_name = str(relative_path).replace('/', '.').replace('.py', '')
        except ValueError:
            # 文件不在backend目录下
            return dependencies
        
        # 搜索所有Python文件，查找对该模块的引用
        for py_file in self.backend_root.glob("**/*.py"):
            if py_file == file_path:
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查import语句
                if self._has_import_reference(content, module_name):
                    dependencies.append(str(py_file))
                    
            except Exception:
                continue
        
        return dependencies
    
    def _has_import_reference(self, content: str, module_name: str) -> bool:
        """检查文件内容是否引用了指定模块"""
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if module_name in alias.name:
                            return True
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module and module_name in node.module:
                        return True
            
        except:
            # 如果AST解析失败，使用字符串搜索
            import_patterns = [
                f"import {module_name}",
                f"from {module_name}",
                f"import backend.{module_name}",
                f"from backend.{module_name}"
            ]
            
            for pattern in import_patterns:
                if pattern in content:
                    return True
        
        return False


def generate_deletion_plan(project_root: str, candidate_files: List[str]) -> Dict:
    """生成文件删除计划"""
    validator = DeletionValidator(project_root)
    
    print("🔍 验证文件删除安全性...")
    validation_results = validator.validate_safe_deletion(candidate_files)
    
    # 生成详细报告
    report = {
        "summary": {
            "total_files": len(candidate_files),
            "safe_deletions": len(validation_results["safe_to_delete"]),
            "risky_deletions": len(validation_results["risky_deletions"]),
            "blocked_deletions": len(validation_results["blocked_deletions"])
        },
        "deletion_plan": {
            "phase_1_safe": validation_results["safe_to_delete"],
            "phase_2_risky": validation_results["risky_deletions"],
            "phase_3_blocked": validation_results["blocked_deletions"]
        },
        "recommendations": []
    }
    
    # 生成建议
    if validation_results["safe_to_delete"]:
        report["recommendations"].append(
            f"可以安全删除 {len(validation_results['safe_to_delete'])} 个文件"
        )
    
    if validation_results["risky_deletions"]:
        report["recommendations"].append(
            f"{len(validation_results['risky_deletions'])} 个文件需要谨慎处理，建议手动检查依赖"
        )
    
    if validation_results["blocked_deletions"]:
        report["recommendations"].append(
            f"{len(validation_results['blocked_deletions'])} 个关键文件不建议删除"
        )
    
    return report


if __name__ == "__main__":
    # 从未使用代码报告中提取候选删除文件
    project_root = "/Users/<USER>/由己ai项目/需求采集项目"
    
    # 基于分析结果的候选删除文件列表
    candidate_files = [
        "backend/scripts/enhanced_session_analyzer.py",
        "backend/scripts/test_performance_monitoring.py", 
        "backend/scripts/performance_test_autogen.py",
        "backend/scripts/error_log_analyzer.py",
        "backend/tests/test_conversation_history_consistency.py",
        "backend/tests/conftest.py",
        "backend/data/db/conversation_manager.py",
        "backend/agents/intelligent_keyword_matcher.py",
        "backend/scripts/quick_log_query.py",
        "backend/utils/monitoring_dashboard.py",
        "backend/tests/test_session_recovery.py",
        "backend/tests/test_conversation_flow_constants.py",
        "backend/scripts/view_session.py",
        "backend/tests/quick_session_test.py",
        "backend/tests/test_conversation_flow_intent.py"
    ]
    
    print("📋 文件删除安全验证工具")
    print("=" * 50)
    
    # 生成删除计划
    plan = generate_deletion_plan(project_root, candidate_files)
    
    # 输出报告
    print(f"\n📊 验证摘要:")
    print(f"总文件数: {plan['summary']['total_files']}")
    print(f"安全删除: {plan['summary']['safe_deletions']}")
    print(f"风险删除: {plan['summary']['risky_deletions']}")
    print(f"禁止删除: {plan['summary']['blocked_deletions']}")
    
    print(f"\n✅ 第一阶段 - 安全删除 ({len(plan['deletion_plan']['phase_1_safe'])} 个文件):")
    for file_path in plan['deletion_plan']['phase_1_safe']:
        print(f"  {file_path}")
    
    if plan['deletion_plan']['phase_2_risky']:
        print(f"\n⚠️  第二阶段 - 谨慎删除 ({len(plan['deletion_plan']['phase_2_risky'])} 个文件):")
        for file_path in plan['deletion_plan']['phase_2_risky']:
            print(f"  {file_path}")
    
    if plan['deletion_plan']['phase_3_blocked']:
        print(f"\n🚫 不建议删除 ({len(plan['deletion_plan']['phase_3_blocked'])} 个文件):")
        for file_path in plan['deletion_plan']['phase_3_blocked']:
            print(f"  {file_path}")
    
    print(f"\n💡 建议:")
    for recommendation in plan['recommendations']:
        print(f"  • {recommendation}")
    
    print("=" * 50)