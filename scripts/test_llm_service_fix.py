#!/usr/bin/env python3
"""
测试 LLM 服务修复效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_unified_config_loader_methods():
    """测试统一配置加载器的方法"""
    print("🧪 测试统一配置加载器方法...")
    
    try:
        from backend.config.unified_config_loader import get_unified_config
        
        config = get_unified_config()
        
        # 测试 get_model_config 方法
        print("  测试 get_model_config 方法...")
        
        # 测试默认配置
        default_config = config.get_model_config()
        print(f"    默认配置: {bool(default_config)}")
        
        # 测试指定 agent_name
        agent_config = config.get_model_config(agent_name="llm_service")
        print(f"    llm_service配置: {bool(agent_config)}")
        
        # 测试指定 model_name
        model_config = config.get_model_config(model_name="default")
        print(f"    default模型配置: {bool(model_config)}")
        
        print("  ✅ get_model_config 方法测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ get_model_config 方法测试失败: {e}")
        return False


def test_llm_service_initialization():
    """测试 LLM 服务初始化"""
    print("\n🧪 测试 LLM 服务初始化...")
    
    try:
        from backend.agents.llm_service import AutoGenLLMServiceAgent
        
        # 尝试初始化 LLM 服务
        print("  初始化 AutoGenLLMServiceAgent...")
        llm_service = AutoGenLLMServiceAgent()
        
        print(f"    LLM服务初始化: ✅")
        print(f"    默认客户端: {bool(llm_service.client)}")
        print(f"    配置管理器: {bool(llm_service.config_manager)}")
        
        print("  ✅ LLM 服务初始化测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ LLM 服务初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_agent_factory():
    """测试代理工厂"""
    print("\n🧪 测试代理工厂...")
    
    try:
        from backend.agents.factory import AgentFactory
        
        # 初始化代理工厂
        print("  初始化 AgentFactory...")
        factory = AgentFactory()
        
        # 测试获取 LLM 服务
        print("  获取 LLM 服务...")
        llm_service = factory.container.get_service("llm_service")
        
        print(f"    LLM服务获取: ✅")
        print(f"    服务类型: {type(llm_service).__name__}")
        
        print("  ✅ 代理工厂测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 代理工厂测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_conversation_flow_agent():
    """测试对话流程代理"""
    print("\n🧪 测试对话流程代理...")
    
    try:
        from backend.agents.factory import AgentFactory
        
        # 初始化代理工厂
        factory = AgentFactory()
        
        # 测试获取对话流程代理
        print("  获取对话流程代理...")
        session_id = "test_session_123"
        conversation_agent = factory.get_conversation_flow_agent(session_id)
        
        print(f"    对话流程代理获取: ✅")
        print(f"    代理类型: {type(conversation_agent).__name__}")
        print(f"    会话ID: {session_id}")
        
        print("  ✅ 对话流程代理测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 对话流程代理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🚀 开始测试 LLM 服务修复效果...\n")
    
    results = []
    
    # 测试统一配置加载器方法
    results.append(test_unified_config_loader_methods())
    
    # 测试 LLM 服务初始化
    results.append(test_llm_service_initialization())
    
    # 测试代理工厂
    results.append(test_agent_factory())
    
    # 测试对话流程代理
    results.append(await test_conversation_flow_agent())
    
    # 总结
    success_count = sum(results)
    total_tests = len(results)
    
    print(f"\n📊 测试总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {success_count}")
    print(f"   成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！LLM 服务修复成功。")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步调试。")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
