#!/usr/bin/env python3
"""
向量化单个文件到知识库
"""

import chromadb
import os
import sys
import argparse
import re

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入文档分块器
from scripts.build_knowledge_base import DocumentChunker

# 定义知识库存储路径
CHROMA_DB_PATH = os.path.join(os.getcwd(), 'backend', 'data', 'chroma_db')
COLLECTION_NAME = 'hybrid_knowledge_base'

class SingleFileVectorizer:
    """单文件向量化器"""
    
    def __init__(self):
        self.chunker = DocumentChunker(chunk_size=800, chunk_overlap=100)
        
    def vectorize_file(self, file_path, role, category=None):
        """
        向量化单个文件
        
        Args:
            file_path: 文件路径
            role: 文档角色
            category: 文档类别
            
        Returns:
            bool: 是否成功
        """
        print(f"=== 向量化文件: {file_path} ===")
        
        try:
            # 验证文件存在
            if not os.path.exists(file_path):
                print(f"错误: 文件不存在: {file_path}")
                return False
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if not content.strip():
                print(f"错误: 文件内容为空")
                return False
            
            # 提取文件名和标题
            file_name = os.path.basename(file_path)
            title = self._extract_title(content) or file_name.split('.')[0]
            
            # 准备元数据
            metadata = {
                "role": role,
                "category": category or "general",
                "source_path": file_path,
                "title": title,
                "doc_id": self._generate_doc_id(file_path)
            }
            
            print(f"文档信息:")
            print(f"  标题: {title}")
            print(f"  角色: {role}")
            print(f"  类别: {metadata['category']}")
            
            # 分块处理
            chunks = self.chunker.chunk_document(content, metadata)
            print(f"文档分块: {len(chunks)} 个块")
            
            if not chunks:
                print("错误: 文档分块失败")
                return False
            
            # 准备向量化数据
            documents = []
            metadatas = []
            ids = []
            
            for i, (chunk_content, chunk_metadata) in enumerate(chunks):
                documents.append(chunk_content)
                metadatas.append(chunk_metadata)
                ids.append(f"chunk_{role}_{metadata['doc_id']}_{i}")
            
            # 连接到ChromaDB
            print(f"连接到ChromaDB: {CHROMA_DB_PATH}")
            client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
            
            # 获取集合
            try:
                collection = client.get_collection(name=COLLECTION_NAME)
                print(f"成功获取集合: {COLLECTION_NAME}")
            except Exception as e:
                print(f"获取集合失败，尝试创建新集合: {e}")
                
                # 创建嵌入函数
                from chromadb.utils import embedding_functions
                embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                    model_name="moka-ai/m3e-base"
                )
                
                # 创建集合
                collection = client.create_collection(
                    name=COLLECTION_NAME,
                    embedding_function=embedding_function
                )
            
            # 添加到集合
            print(f"添加 {len(documents)} 个文档块到知识库...")
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            print("文档添加成功")
            
            # 验证添加
            verify_results = collection.query(
                query_texts=[title],
                where={"role": {"$eq": role}, "title": {"$eq": title}},
                n_results=1
            )
            
            if verify_results['ids'][0]:
                print(f"验证成功: 文档已成功添加到知识库")
                return True
            else:
                print(f"验证失败: 文档可能未成功添加")
                return False
            
        except Exception as e:
            print(f"向量化失败: {e}")
            return False
    
    def _extract_title(self, content):
        """从内容中提取标题"""
        # 尝试匹配Markdown标题
        match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        if match:
            return match.group(1).strip()
        
        # 尝试匹配第一行作为标题
        lines = content.split('\n')
        if lines:
            return lines[0].strip().replace('#', '').strip()
        
        return None
    
    def _generate_doc_id(self, file_path):
        """生成文档ID"""
        import hashlib
        return hashlib.md5(file_path.encode()).hexdigest()[:16]

def main():
    parser = argparse.ArgumentParser(description='向量化单个文件到知识库')
    parser.add_argument('--file', type=str, required=True, help='要向量化的文件路径')
    parser.add_argument('--role', type=str, required=True, help='文档角色')
    parser.add_argument('--category', type=str, help='文档类别')
    args = parser.parse_args()
    
    vectorizer = SingleFileVectorizer()
    success = vectorizer.vectorize_file(args.file, args.role, args.category)
    
    if success:
        print("\n向量化操作成功完成")
    else:
        print("\n向量化操作失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
