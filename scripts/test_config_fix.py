#!/usr/bin/env python3
"""
测试配置修复效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_unified_config_loader():
    """测试统一配置加载器"""
    print("🧪 测试统一配置加载器...")
    
    try:
        from backend.config.unified_config_loader import get_unified_config
        
        config = get_unified_config()
        
        # 测试新添加的方法
        print("  测试 _get_config 方法...")
        business_rules = config._get_config("business_rules")
        print(f"    business_rules: {bool(business_rules)}")
        
        print("  测试 get_message_config 方法...")
        message_config = config.get_message_config()
        print(f"    message_config: {bool(message_config)}")
        
        # 测试各个配置部分
        print("  测试配置部分...")
        strategies = config.get_config_value("strategies", {})
        print(f"    strategies: {bool(strategies)}")
        
        business_rules = config.get_config_value("business_rules", {})
        print(f"    business_rules: {bool(business_rules)}")
        
        message_templates = config.get_message_templates()
        print(f"    message_templates: {bool(message_templates)}")
        
        database = config.get_config_value("database", {})
        print(f"    database: {bool(database)}")
        
        print("  ✅ 统一配置加载器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 统一配置加载器测试失败: {e}")
        return False

async def test_config_preloader():
    """测试配置预加载器"""
    print("\n🧪 测试配置预加载器...")

    try:
        from backend.config.preloader import ConfigPreloader

        preloader = ConfigPreloader()

        # 测试预加载单个配置
        print("  测试预加载单个配置...")

        test_configs = [
            "business_rules.yaml",
            "strategies.yaml",
            "message_config.yaml",
            "database_queries.yaml"
        ]

        success_count = 0
        for config_name in test_configs:
            try:
                result = await preloader._preload_single_config(config_name)
                if result.success:
                    success_count += 1
                    print(f"    ✅ {config_name}: 成功")
                else:
                    print(f"    ❌ {config_name}: 失败 - {result.error}")
            except Exception as e:
                print(f"    ❌ {config_name}: 异常 - {e}")

        print(f"  预加载成功率: {success_count}/{len(test_configs)} ({success_count/len(test_configs)*100:.1f}%)")

        if success_count == len(test_configs):
            print("  ✅ 配置预加载器测试通过")
            return True
        else:
            print("  ⚠️  配置预加载器部分通过")
            return False

    except Exception as e:
        print(f"  ❌ 配置预加载器测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始测试配置修复效果...\n")

    results = []

    # 测试统一配置加载器
    results.append(test_unified_config_loader())

    # 测试配置预加载器
    results.append(await test_config_preloader())

    # 总结
    success_count = sum(results)
    total_tests = len(results)

    print(f"\n📊 测试总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {success_count}")
    print(f"   成功率: {success_count/total_tests*100:.1f}%")

    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！配置修复成功。")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
