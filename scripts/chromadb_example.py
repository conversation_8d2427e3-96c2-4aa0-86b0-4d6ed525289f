import chromadb
import os

# 定义知识库存储路径
# 我们将把 ChromaDB 的数据存储在 backend/data/chroma_db 目录下
CHROMA_DB_PATH = os.path.join(os.getcwd(), 'backend', 'data', 'chroma_db')

def run_chromadb_example():
    print(f"Initializing ChromaDB client at: {CHROMA_DB_PATH}")
    # 初始化 ChromaDB 客户端，指定数据存储路径
    client = chromadb.PersistentClient(path=CHROMA_DB_PATH)

    # 获取或创建 Collection
    # Collection 类似于传统数据库中的“表”，用于存储一组相关的文档和它们的向量
    collection_name = "my_knowledge_base"
    print(f"Getting or creating collection: {collection_name}")
    collection = client.get_or_create_collection(name=collection_name)

    # 准备一些示例文档
    documents = [
        "产品A的主要功能是数据分析和报告生成。",
        "产品B专注于用户界面设计和交互体验优化。",
        "产品C提供强大的后端服务和API集成能力。",
        "如何联系客服？请拨打我们的24小时服务热线：400-123-4567。",
        "关于退款政策，请查阅官网的退款说明页面。",
        "我们的办公时间是周一至周五，上午9点到下午6点。"
    ]
    # 为每个文档生成一个唯一的ID
    ids = [f"doc_{i}" for i in range(len(documents))]

    print("Adding documents to the knowledge base...")
    # 将文档添加到 Collection 中
    # ChromaDB 会自动为这些文档生成向量（Embedding）
    collection.add(
        documents=documents,
        ids=ids
    )
    print(f"Added {len(documents)} documents.")

    # 示例查询
    query_text = "我想知道产品B的特点是什么？"
    print(f"Querying for: '{query_text}'")
    results = collection.query(
        query_texts=[query_text],
        n_results=2  # 返回最相似的2个结果
    )

    print("\n--- Query Results ---")
    for i, doc in enumerate(results['documents'][0]):
        print(f"Result {i+1}:")
        print(f"  Document: {doc}")
        print(f"  Distance: {results['distances'][0][i]:.4f}") # 距离越小，相似度越高
    print("---------------------\n")

    query_text_2 = "如何联系你们？"
    print(f"Querying for: '{query_text_2}'")
    results_2 = collection.query(
        query_texts=[query_text_2],
        n_results=1
    )
    print("\n--- Query Results ---")
    for i, doc in enumerate(results_2['documents'][0]):
        print(f"Result {i+1}:")
        print(f"  Document: {doc}")
        print(f"  Distance: {results_2['distances'][0][i]:.4f}")
    print("---------------------\n")

    # 清理：删除 Collection (可选，如果想重新开始)
    # print("Deleting collection (optional)...")
    # client.delete_collection(name=collection_name)
    # print("Collection deleted.")

if __name__ == "__main__":
    run_chromadb_example()
