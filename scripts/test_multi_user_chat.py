import asyncio
import httpx
import uuid

# API的URL
BASE_URL = "http://127.0.0.1:8000"

async def run_chat_test(user_id: str, session_id: str, message: str):
    """一个辅助函数，用于向/chat端点发送POST请求"""
    url = f"{BASE_URL}/chat"
    headers = {
        "X-Session-ID": user_id,  # 我们将user_id作为X-Session-ID头传递
        "Content-Type": "application/json"
    }
    payload = {
        "message": message,
        "session_id": session_id
    }
    
    print(f"\n--- [User: {user_id}, Session: {session_id}] Sending: '{message}' ---")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload, headers=headers, timeout=60.0)
            response.raise_for_status()  # 如果HTTP状态码是4xx或5xx，则引发异常
            
            response_data = response.json()
            print(f"Server Response: {response_data.get('response')}")
            print(f"Focus Points Status: {response_data.get('focus_points_status')}")
            return response_data
            
        except httpx.HTTPStatusError as e:
            print(f"Error: HTTP Status {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            print(f"Error: Request failed - {e}")
        return None

async def main():
    """主测试函数"""
    user_a_id = "user_A_test"
    user_b_id = "user_B_test"
    
    session_a = f"session_{str(uuid.uuid4())}"
    session_b = f"session_{str(uuid.uuid4())}"

    print("=================================================")
    print("===   启动多用户聊天数据隔离测试   ===")
    print("=================================================")

    # 步骤 1: 用户A开始一个新的对话
    await run_chat_test(user_a_id, session_a, "你好，我想开发一个社交App。")

    await asyncio.sleep(2) # 等待一下，模拟真实场景

    # 步骤 2: 用户B开始一个完全独立的对话
    await run_chat_test(user_b_id, session_b, "你好，我需要一个工具来分析销售数据。")
    
    await asyncio.sleep(2)

    # 步骤 3: 用户A继续他的对话，系统应该记得上下文
    await run_chat_test(user_a_id, session_a, "需要包含实时聊天功能。")
    
    await asyncio.sleep(2)

    # 步骤 4: 用户B继续他的对话，系统也应该记得他的上下文
    await run_chat_test(user_b_id, session_b, "数据源是CSV文件。")

    print("\n=================================================")
    print("===   测试完成   ===")
    print("=================================================")
    print("请检查上面的输出来验证：")
    print("1. 用户A和用户B的对话是完全独立的。")
    print("2. 系统能够正确地为每个用户维护独立的上下文。")

if __name__ == "__main__":
    # 在运行此脚本之前，请确保FastAPI服务器正在运行:
    # uvicorn backend.api.main:app --host 127.0.0.1 --port 8000 --reload
    asyncio.run(main())