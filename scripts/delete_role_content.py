#!/usr/bin/env python3
"""
删除知识库中特定角色的内容
"""

import chromadb
import os
import sys
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 定义知识库存储路径
CHROMA_DB_PATH = os.path.join(os.getcwd(), 'backend', 'data', 'chroma_db')
COLLECTION_NAME = 'hybrid_knowledge_base'

def delete_role_content(role):
    """删除特定角色的内容"""
    print(f"=== 删除知识库中的 '{role}' 角色内容 ===")
    
    try:
        # 初始化ChromaDB客户端
        print(f"连接到ChromaDB: {CHROMA_DB_PATH}")
        client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        
        # 获取集合
        try:
            collection = client.get_collection(name=COLLECTION_NAME)
            print(f"成功获取集合: {COLLECTION_NAME}")
        except Exception as e:
            print(f"获取集合失败: {e}")
            return False
        
        # 查询特定角色的内容
        results = collection.query(
            query_texts=[""],
            where={"role": {"$eq": role}},
            n_results=100  # 设置一个较大的值以获取所有匹配项
        )
        
        if not results['ids'][0]:
            print(f"未找到角色为 '{role}' 的内容")
            return True
        
        # 获取要删除的ID
        ids_to_delete = results['ids'][0]
        print(f"找到 {len(ids_to_delete)} 个 '{role}' 角色的内容")
        
        # 删除内容
        collection.delete(ids=ids_to_delete)
        print(f"成功删除 {len(ids_to_delete)} 个内容")
        
        # 验证删除
        verify_results = collection.query(
            query_texts=[""],
            where={"role": {"$eq": role}},
            n_results=10
        )
        
        if not verify_results['ids'][0]:
            print(f"验证成功: 所有 '{role}' 角色的内容已删除")
            return True
        else:
            print(f"验证失败: 仍有 {len(verify_results['ids'][0])} 个 '{role}' 角色的内容")
            return False
        
    except Exception as e:
        print(f"删除失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='删除知识库中特定角色的内容')
    parser.add_argument('--role', type=str, required=True, help='要删除的角色名称')
    args = parser.parse_args()
    
    success = delete_role_content(args.role)
    
    if success:
        print("\n删除操作成功完成")
    else:
        print("\n删除操作失败")
        sys.exit(1)

if __name__ == "__main__":
    main()