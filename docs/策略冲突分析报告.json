{"summary": {"total_conflicts": 6, "total_warnings": 5, "total_recommendations": 0}, "priority_conflicts": {"total_conflicts": 5, "conflicts_by_state": {"IDLE": [{"type": "priority_conflict", "state": "IDLE", "priority": 1, "conflicted_intents": ["greeting", "ask_question", "business_requirement", "general_chat"], "actions": ["send_greeting", "explain_capabilities", "start_requirement_collection", "respond_to_general_chat"], "severity": "medium"}, {"type": "priority_conflict", "state": "IDLE", "priority": 2, "conflicted_intents": ["ask_introduction", "ask_capabilities", "search_knowledge_base", "emotional_support"], "actions": ["provide_self_introduction", "explain_capabilities", "search_knowledge_base", "provide_emotional_support"], "severity": "high"}], "COLLECTING_INFO": [{"type": "priority_conflict", "state": "COLLECTING_INFO", "priority": 2, "conflicted_intents": ["request_clarification", "confirm", "search_knowledge_base", "ask_question", "restart"], "actions": ["request_clarification", "start_document_generation", "search_knowledge_base", "provide_guidance_and_continue", "restart_conversation"], "severity": "high"}], "DOCUMENTING": [{"type": "priority_conflict", "state": "DOCUMENTING", "priority": 2, "conflicted_intents": ["confirm", "modify", "restart"], "actions": ["confirm_document", "modify_document", "restart_conversation"], "severity": "high"}], "COMPLETED": [{"type": "priority_conflict", "state": "COMPLETED", "priority": 1, "conflicted_intents": ["business_requirement", "greeting"], "actions": ["start_requirement_collection", "send_greeting"], "severity": "medium"}], "PROCESSING_INTENT": []}, "high_priority_conflicts": [{"type": "priority_conflict", "state": "IDLE", "priority": 2, "conflicted_intents": ["ask_introduction", "ask_capabilities", "search_knowledge_base", "emotional_support"], "actions": ["provide_self_introduction", "explain_capabilities", "search_knowledge_base", "provide_emotional_support"], "severity": "high"}, {"type": "priority_conflict", "state": "COLLECTING_INFO", "priority": 2, "conflicted_intents": ["request_clarification", "confirm", "search_knowledge_base", "ask_question", "restart"], "actions": ["request_clarification", "start_document_generation", "search_knowledge_base", "provide_guidance_and_continue", "restart_conversation"], "severity": "high"}, {"type": "priority_conflict", "state": "DOCUMENTING", "priority": 2, "conflicted_intents": ["confirm", "modify", "restart"], "actions": ["confirm_document", "modify_document", "restart_conversation"], "severity": "high"}]}, "intent_overlaps": {"total_overlaps": 0, "keyword_overlaps": [], "priority_issues": [], "intent_priority_order": ["search_knowledge_base", "ask_question", "business_requirement", "ask_introduction", "ask_capabilities", "modify", "confirm", "restart", "emotional_support", "greeting", "general_chat", "provide_information", "unknown"], "analysis_note": "关键词分析受限于硬编码实现，建议重构为配置驱动"}, "state_consistency": {"total_inconsistencies": 1, "invalid_transitions": [{"type": "invalid_state_transition", "current_state": "PROCESSING_INTENT", "intent": "default", "next_state": "IDLE", "valid_transitions": [], "severity": "high"}], "valid_transition_map": {"IDLE": ["IDLE", "COLLECTING_INFO"], "COLLECTING_INFO": ["COLLECTING_INFO", "DOCUMENTING", "IDLE"], "DOCUMENTING": ["DOCUMENTING", "COMPLETED", "IDLE"], "COMPLETED": ["IDLE", "COLLECTING_INFO"]}}, "rule_completeness": {"missing_intent_rules": [{"type": "missing_intent_rules", "state": "IDLE", "missing_intents": ["confirm", "unknown", "restart", "modify", "provide_information"], "available_intents": ["general_chat", "emotional_support", "search_knowledge_base", "business_requirement", "ask_introduction", "greeting", "ask_question", "ask_capabilities"], "coverage_rate": 0.6153846153846154, "severity": "high"}, {"type": "missing_intent_rules", "state": "COLLECTING_INFO", "missing_intents": ["general_chat", "unknown", "emotional_support", "modify", "business_requirement", "ask_introduction", "greeting", "ask_capabilities"], "available_intents": ["confirm", "restart", "search_knowledge_base", "request_clarification", "provide_information", "process_answer", "ask_question"], "coverage_rate": 0.5384615384615384, "severity": "high"}, {"type": "missing_intent_rules", "state": "DOCUMENTING", "missing_intents": ["general_chat", "unknown", "emotional_support", "search_knowledge_base", "business_requirement", "provide_information", "ask_introduction", "greeting", "ask_question", "ask_capabilities"], "available_intents": ["confirm", "modify", "restart"], "coverage_rate": 0.23076923076923078, "severity": "high"}, {"type": "missing_intent_rules", "state": "COMPLETED", "missing_intents": ["confirm", "general_chat", "unknown", "restart", "search_knowledge_base", "emotional_support", "modify", "provide_information", "ask_introduction", "ask_question", "ask_capabilities"], "available_intents": ["greeting", "business_requirement"], "coverage_rate": 0.15384615384615385, "severity": "high"}, {"type": "missing_intent_rules", "state": "PROCESSING_INTENT", "missing_intents": ["confirm", "general_chat", "unknown", "restart", "search_knowledge_base", "emotional_support", "modify", "business_requirement", "provide_information", "ask_introduction", "greeting", "ask_question", "ask_capabilities"], "available_intents": [], "coverage_rate": 0.0, "severity": "high"}], "states_without_default": [], "coverage_analysis": {"IDLE": {"total_intents": 13, "covered_intents": 8, "coverage_rate": 0.6153846153846154}, "COLLECTING_INFO": {"total_intents": 13, "covered_intents": 7, "coverage_rate": 0.5384615384615384}, "DOCUMENTING": {"total_intents": 13, "covered_intents": 3, "coverage_rate": 0.23076923076923078}, "COMPLETED": {"total_intents": 13, "covered_intents": 2, "coverage_rate": 0.15384615384615385}, "PROCESSING_INTENT": {"total_intents": 13, "covered_intents": 0, "coverage_rate": 0.0}}}, "conflicts": [{"type": "priority_conflict", "state": "IDLE", "priority": 1, "conflicted_intents": ["greeting", "ask_question", "business_requirement", "general_chat"], "actions": ["send_greeting", "explain_capabilities", "start_requirement_collection", "respond_to_general_chat"], "severity": "medium"}, {"type": "priority_conflict", "state": "IDLE", "priority": 2, "conflicted_intents": ["ask_introduction", "ask_capabilities", "search_knowledge_base", "emotional_support"], "actions": ["provide_self_introduction", "explain_capabilities", "search_knowledge_base", "provide_emotional_support"], "severity": "high"}, {"type": "priority_conflict", "state": "COLLECTING_INFO", "priority": 2, "conflicted_intents": ["request_clarification", "confirm", "search_knowledge_base", "ask_question", "restart"], "actions": ["request_clarification", "start_document_generation", "search_knowledge_base", "provide_guidance_and_continue", "restart_conversation"], "severity": "high"}, {"type": "priority_conflict", "state": "DOCUMENTING", "priority": 2, "conflicted_intents": ["confirm", "modify", "restart"], "actions": ["confirm_document", "modify_document", "restart_conversation"], "severity": "high"}, {"type": "priority_conflict", "state": "COMPLETED", "priority": 1, "conflicted_intents": ["business_requirement", "greeting"], "actions": ["start_requirement_collection", "send_greeting"], "severity": "medium"}, {"type": "invalid_state_transition", "current_state": "PROCESSING_INTENT", "intent": "default", "next_state": "IDLE", "valid_transitions": [], "severity": "high"}], "warnings": [{"type": "missing_intent_rules", "state": "IDLE", "missing_intents": ["confirm", "unknown", "restart", "modify", "provide_information"], "available_intents": ["general_chat", "emotional_support", "search_knowledge_base", "business_requirement", "ask_introduction", "greeting", "ask_question", "ask_capabilities"], "coverage_rate": 0.6153846153846154, "severity": "high"}, {"type": "missing_intent_rules", "state": "COLLECTING_INFO", "missing_intents": ["general_chat", "unknown", "emotional_support", "modify", "business_requirement", "ask_introduction", "greeting", "ask_capabilities"], "available_intents": ["confirm", "restart", "search_knowledge_base", "request_clarification", "provide_information", "process_answer", "ask_question"], "coverage_rate": 0.5384615384615384, "severity": "high"}, {"type": "missing_intent_rules", "state": "DOCUMENTING", "missing_intents": ["general_chat", "unknown", "emotional_support", "search_knowledge_base", "business_requirement", "provide_information", "ask_introduction", "greeting", "ask_question", "ask_capabilities"], "available_intents": ["confirm", "modify", "restart"], "coverage_rate": 0.23076923076923078, "severity": "high"}, {"type": "missing_intent_rules", "state": "COMPLETED", "missing_intents": ["confirm", "general_chat", "unknown", "restart", "search_knowledge_base", "emotional_support", "modify", "provide_information", "ask_introduction", "ask_question", "ask_capabilities"], "available_intents": ["greeting", "business_requirement"], "coverage_rate": 0.15384615384615385, "severity": "high"}, {"type": "missing_intent_rules", "state": "PROCESSING_INTENT", "missing_intents": ["confirm", "general_chat", "unknown", "restart", "search_knowledge_base", "emotional_support", "modify", "business_requirement", "provide_information", "ask_introduction", "greeting", "ask_question", "ask_capabilities"], "available_intents": [], "coverage_rate": 0.0, "severity": "high"}], "recommendations": [{"type": "conflict_resolution", "priority": "high", "description": "发现策略冲突，建议实施统一决策引擎架构", "actions": ["实现策略冲突检测机制", "建立优先级解决策略", "添加运行时冲突监控"]}, {"type": "rule_completeness", "priority": "medium", "description": "决策规则覆盖不完整，建议补充缺失规则", "actions": ["为每个状态添加完整的意图处理规则", "确保所有状态都有默认处理规则", "建立规则完整性检查机制"]}]}