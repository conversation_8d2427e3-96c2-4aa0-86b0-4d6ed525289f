# 后台管理系统 API 设计

## 📋 API 概述

后台管理系统API基于RESTful设计原则，提供完整的管理功能接口。所有API都需要JWT认证，并根据用户角色进行权限控制。

## 🔐 认证相关 API

### 1. 管理员登录
```http
POST /api/admin/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "super_admin",
      "email": "<EMAIL>"
    },
    "expires_in": 86400
  }
}
```

### 2. 刷新Token
```http
POST /api/admin/auth/refresh
Authorization: Bearer <token>
```

### 3. 退出登录
```http
POST /api/admin/auth/logout
Authorization: Bearer <token>
```

## 👥 用户管理 API

### 1. 获取用户列表
```http
GET /api/admin/users?page=1&limit=20&search=keyword&status=active
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_123",
        "created_at": "2025-01-01T00:00:00Z",
        "last_active": "2025-01-15T10:30:00Z",
        "status": "active",
        "conversation_count": 15,
        "document_count": 8
      }
    ],
    "total": 150,
    "page": 1,
    "limit": 20
  }
}
```

### 2. 获取用户详情
```http
GET /api/admin/users/{user_id}
Authorization: Bearer <token>
```

### 3. 更新用户状态
```http
PUT /api/admin/users/{user_id}/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "disabled",
  "reason": "违规操作"
}
```

### 4. 获取用户对话历史
```http
GET /api/admin/users/{user_id}/conversations?page=1&limit=10
Authorization: Bearer <token>
```

## 📊 数据统计 API

### 1. 获取系统概览统计
```http
GET /api/admin/stats/overview?period=7d
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "active_users": 1250,
    "total_conversations": 8500,
    "total_documents": 3200,
    "avg_response_time": 0.15,
    "error_rate": 0.02,
    "period_comparison": {
      "active_users_change": "+12%",
      "conversations_change": "+8%"
    }
  }
}
```

### 2. 获取使用趋势数据
```http
GET /api/admin/stats/trends?metric=conversations&period=30d&granularity=day
Authorization: Bearer <token>
```

### 3. 获取领域分布统计
```http
GET /api/admin/stats/domains?period=7d
Authorization: Bearer <token>
```

### 4. 获取性能统计
```http
GET /api/admin/stats/performance?period=24h
Authorization: Bearer <token>
```

## 🖥️ 系统监控 API

### 1. 获取系统状态
```http
GET /api/admin/monitor/system
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "cpu_usage": 45.2,
    "memory_usage": 68.5,
    "disk_usage": 32.1,
    "database_status": "healthy",
    "api_response_time": 0.12,
    "active_connections": 25,
    "uptime": 86400
  }
}
```

### 2. 获取错误日志
```http
GET /api/admin/monitor/logs/errors?page=1&limit=50&level=error&start_time=2025-01-01&end_time=2025-01-15
Authorization: Bearer <token>
```

### 3. 获取性能指标
```http
GET /api/admin/monitor/performance?metric=response_time&period=1h
Authorization: Bearer <token>
```

## ⚙️ 配置管理 API

### 1. 获取系统配置
```http
GET /api/admin/config?category=ai_models
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "configs": [
      {
        "key": "default_model_provider",
        "value": "deepseek",
        "type": "string",
        "description": "默认AI模型提供商",
        "category": "ai_models"
      }
    ]
  }
}
```

### 2. 更新配置
```http
PUT /api/admin/config/{config_key}
Authorization: Bearer <token>
Content-Type: application/json

{
  "value": "new_value",
  "description": "更新说明"
}
```

### 3. 获取AI模型配置
```http
GET /api/admin/config/ai-models
Authorization: Bearer <token>
```

### 4. 更新AI模型配置
```http
PUT /api/admin/config/ai-models
Authorization: Bearer <token>
Content-Type: application/json

{
  "deepseek": {
    "api_key": "sk-xxx",
    "base_url": "https://api.deepseek.com",
    "enabled": true
  }
}
```

## 💬 对话管理 API

### 1. 获取对话列表
```http
GET /api/admin/conversations?page=1&limit=20&user_id=user_123&status=completed&start_date=2025-01-01
Authorization: Bearer <token>
```

### 2. 获取对话详情
```http
GET /api/admin/conversations/{conversation_id}
Authorization: Bearer <token>
```

### 3. 标记异常对话
```http
PUT /api/admin/conversations/{conversation_id}/flag
Authorization: Bearer <token>
Content-Type: application/json

{
  "flag_type": "quality_issue",
  "reason": "回复不准确",
  "notes": "需要优化模型参数"
}
```

## 📋 操作日志 API

### 1. 获取操作日志
```http
GET /api/admin/logs/operations?page=1&limit=50&admin_id=1&action=user_update
Authorization: Bearer <token>
```

### 2. 记录操作日志
```http
POST /api/admin/logs/operations
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "config_update",
  "resource": "ai_models",
  "details": "更新DeepSeek API配置"
}
```

## 📈 报表导出 API

### 1. 导出用户数据
```http
GET /api/admin/export/users?format=csv&start_date=2025-01-01&end_date=2025-01-31
Authorization: Bearer <token>
```

### 2. 导出统计报表
```http
GET /api/admin/export/stats?type=daily_summary&period=30d&format=excel
Authorization: Bearer <token>
```

## 🔒 权限控制

### 角色权限矩阵
| 功能模块 | 超级管理员 | 系统管理员 | 数据分析师 | 客服管理员 |
|---------|-----------|-----------|-----------|-----------|
| 用户管理 | ✅ | ❌ | ❌ | ✅ |
| 系统配置 | ✅ | ✅ | ❌ | ❌ |
| 数据统计 | ✅ | ✅ | ✅ | ✅ |
| 系统监控 | ✅ | ✅ | ❌ | ❌ |
| 对话管理 | ✅ | ❌ | ❌ | ✅ |

## 📝 错误码定义

| 错误码 | 说明 |
|-------|------|
| 40001 | 认证失败 |
| 40003 | 权限不足 |
| 40004 | 用户不存在 |
| 42001 | 参数验证失败 |
| 50001 | 服务器内部错误 |
| 50002 | 数据库连接失败 |

## 🚀 API 版本控制

- 当前版本: v1
- 版本格式: `/api/v1/admin/...`
- 向后兼容策略: 保持至少2个版本的兼容性
