#!/usr/bin/env python3
"""
业务逻辑测试数据生成器

功能：
1. 生成各种测试场景的数据
2. 创建边界条件测试数据
3. 生成压力测试数据
4. 模拟真实用户交互数据

使用方法：
python tests/test_data_generator.py --type all
python tests/test_data_generator.py --type state_transitions
python tests/test_data_generator.py --type focus_points
"""

import json
import uuid
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import argparse

@dataclass
class TestUserSession:
    """测试用户会话数据"""
    user_id: str
    session_id: str
    current_state: str
    created_at: str
    last_activity: str
    domain_id: Optional[str] = None
    category_id: Optional[str] = None

@dataclass
class TestFocusPoint:
    """测试关注点数据"""
    id: str
    name: str
    priority: str  # P0, P1, P2
    status: str    # pending, processing, completed, skipped
    attempts: int
    max_attempts: Optional[int]
    created_at: str
    updated_at: str

@dataclass
class TestDocument:
    """测试文档数据"""
    id: str
    session_id: str
    user_id: str
    content: str
    status: str  # draft, confirmed, rejected
    version: int
    created_at: str
    updated_at: str

@dataclass
class TestUserInput:
    """测试用户输入数据"""
    id: str
    session_id: str
    user_id: str
    message: str
    expected_intent: str
    expected_state_transition: Optional[str]
    context: Dict[str, Any]
    timestamp: str

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self):
        self.generated_data = {
            "sessions": [],
            "focus_points": [],
            "documents": [],
            "user_inputs": [],
            "state_transitions": [],
            "edge_cases": []
        }
        
        # 预定义的测试数据模板
        self.state_transitions = [
            ("IDLE", "PROCESSING_INTENT", "user_input_received"),
            ("PROCESSING_INTENT", "COLLECTING_INFO", "intent_recognized"),
            ("COLLECTING_INFO", "DOCUMENTING", "focus_points_completed"),
            ("DOCUMENTING", "IDLE", "document_confirmed")
        ]
        
        self.focus_point_templates = [
            {"name": "核心功能需求", "priority": "P0"},
            {"name": "技术架构要求", "priority": "P0"},
            {"name": "性能指标", "priority": "P1"},
            {"name": "用户体验要求", "priority": "P1"},
            {"name": "扩展功能", "priority": "P2"},
            {"name": "可选特性", "priority": "P2"}
        ]
        
        self.user_input_templates = {
            "greeting": [
                "你好", "hello", "hi", "早上好", "下午好"
            ],
            "confirmation": [
                "确认", "没问题", "正确", "ok", "好的"
            ],
            "modification": [
                "修改", "调整", "不对", "需要改", "但是"
            ],
            "restart": [
                "重新开始", "新聊天", "重置", "清空"
            ],
            "mixed": [
                "好的，但是需要修改",
                "确认，不过要调整一下",
                "我想重新开始，先确认一下"
            ]
        }
    
    def generate_test_sessions(self, count: int = 10) -> List[TestUserSession]:
        """生成测试会话数据"""
        sessions = []
        states = ["IDLE", "PROCESSING_INTENT", "COLLECTING_INFO", "DOCUMENTING"]
        
        for i in range(count):
            user_id = f"test_user_{i+1:03d}"
            session_id = f"test_session_{uuid.uuid4().hex[:8]}"
            current_state = random.choice(states)
            
            created_time = datetime.now() - timedelta(hours=random.randint(1, 24))
            last_activity = created_time + timedelta(minutes=random.randint(1, 60))
            
            session = TestUserSession(
                user_id=user_id,
                session_id=session_id,
                current_state=current_state,
                created_at=created_time.isoformat(),
                last_activity=last_activity.isoformat(),
                domain_id=f"domain_{random.randint(1, 5)}" if random.random() > 0.3 else None,
                category_id=f"category_{random.randint(1, 10)}" if random.random() > 0.3 else None
            )
            
            sessions.append(session)
        
        self.generated_data["sessions"] = sessions
        return sessions
    
    def generate_test_focus_points(self, session_count: int = 5) -> List[TestFocusPoint]:
        """生成测试关注点数据"""
        focus_points = []
        sessions = self.generated_data.get("sessions", [])
        
        if not sessions:
            sessions = self.generate_test_sessions(session_count)
        
        for session in sessions[:session_count]:
            # 为每个会话生成3-6个关注点
            point_count = random.randint(3, 6)
            
            for i in range(point_count):
                template = random.choice(self.focus_point_templates)
                
                focus_point = TestFocusPoint(
                    id=f"fp_{session.session_id}_{i+1}",
                    name=f"{template['name']}_{i+1}",
                    priority=template['priority'],
                    status=random.choice(["pending", "processing", "completed", "skipped"]),
                    attempts=random.randint(0, 3),
                    max_attempts=3 if template['priority'] in ["P1", "P2"] else None,
                    created_at=session.created_at,
                    updated_at=session.last_activity
                )
                
                focus_points.append(focus_point)
        
        self.generated_data["focus_points"] = focus_points
        return focus_points
    
    def generate_test_documents(self, session_count: int = 3) -> List[TestDocument]:
        """生成测试文档数据"""
        documents = []
        sessions = self.generated_data.get("sessions", [])
        
        if not sessions:
            sessions = self.generate_test_sessions(session_count)
        
        for session in sessions[:session_count]:
            if session.current_state in ["DOCUMENTING", "IDLE"]:
                document = TestDocument(
                    id=f"doc_{uuid.uuid4().hex[:8]}",
                    session_id=session.session_id,
                    user_id=session.user_id,
                    content=self._generate_document_content(),
                    status=random.choice(["draft", "confirmed", "rejected"]),
                    version=random.randint(1, 3),
                    created_at=session.last_activity,
                    updated_at=session.last_activity
                )
                
                documents.append(document)
        
        self.generated_data["documents"] = documents
        return documents
    
    def generate_test_user_inputs(self, count: int = 50) -> List[TestUserInput]:
        """生成测试用户输入数据"""
        user_inputs = []
        sessions = self.generated_data.get("sessions", [])
        
        if not sessions:
            sessions = self.generate_test_sessions(10)
        
        for i in range(count):
            session = random.choice(sessions)
            
            # 根据当前状态选择合适的输入类型
            if session.current_state == "DOCUMENTING":
                input_types = ["confirmation", "modification", "restart", "mixed"]
            else:
                input_types = ["greeting", "restart"]
            
            input_type = random.choice(input_types)
            message = random.choice(self.user_input_templates[input_type])
            
            # 确定期望的意图
            expected_intent = self._determine_expected_intent(message, session.current_state)
            
            # 确定期望的状态转换
            expected_transition = self._determine_expected_transition(
                expected_intent, session.current_state
            )
            
            user_input = TestUserInput(
                id=f"input_{i+1:03d}",
                session_id=session.session_id,
                user_id=session.user_id,
                message=message,
                expected_intent=expected_intent,
                expected_state_transition=expected_transition,
                context={
                    "current_state": session.current_state,
                    "has_document": session.current_state == "DOCUMENTING",
                    "focus_points_completed": random.random() > 0.3
                },
                timestamp=datetime.now().isoformat()
            )
            
            user_inputs.append(user_input)
        
        self.generated_data["user_inputs"] = user_inputs
        return user_inputs
    
    def generate_edge_cases(self) -> List[Dict[str, Any]]:
        """生成边界条件测试数据"""
        edge_cases = [
            # 空输入
            {
                "type": "empty_input",
                "input": "",
                "expected_behavior": "handle_gracefully",
                "context": {"state": "IDLE"}
            },
            
            # 超长输入
            {
                "type": "very_long_input",
                "input": "x" * 10000,
                "expected_behavior": "truncate_or_reject",
                "context": {"state": "COLLECTING_INFO"}
            },
            
            # 特殊字符
            {
                "type": "special_characters",
                "input": "!@#$%^&*()_+{}[]|\\:;\"'<>?,./",
                "expected_behavior": "handle_gracefully",
                "context": {"state": "IDLE"}
            },
            
            # 混合语言
            {
                "type": "mixed_language",
                "input": "Hello 你好 こんにちは",
                "expected_behavior": "handle_gracefully",
                "context": {"state": "COLLECTING_INFO"}
            },
            
            # 状态不一致
            {
                "type": "state_inconsistency",
                "input": "确认文档",
                "expected_behavior": "recover_state",
                "context": {
                    "memory_state": "DOCUMENTING",
                    "database_state": "IDLE"
                }
            },
            
            # 并发操作
            {
                "type": "concurrent_operations",
                "input": "multiple_simultaneous_requests",
                "expected_behavior": "maintain_consistency",
                "context": {
                    "concurrent_users": 5,
                    "same_session": True
                }
            }
        ]
        
        self.generated_data["edge_cases"] = edge_cases
        return edge_cases
    
    def generate_state_transition_tests(self) -> List[Dict[str, Any]]:
        """生成状态转换测试数据"""
        transitions = []
        
        for from_state, to_state, trigger in self.state_transitions:
            # 正常转换
            transitions.append({
                "type": "normal_transition",
                "from_state": from_state,
                "to_state": to_state,
                "trigger": trigger,
                "expected_result": "success"
            })
            
            # 无效转换
            invalid_states = ["IDLE", "PROCESSING_INTENT", "COLLECTING_INFO", "DOCUMENTING"]
            invalid_states.remove(from_state)
            if to_state in invalid_states:
                invalid_states.remove(to_state)
            
            for invalid_to in invalid_states:
                transitions.append({
                    "type": "invalid_transition",
                    "from_state": from_state,
                    "to_state": invalid_to,
                    "trigger": trigger,
                    "expected_result": "failure"
                })
        
        self.generated_data["state_transitions"] = transitions
        return transitions
    
    def _generate_document_content(self) -> str:
        """生成文档内容"""
        templates = [
            "# 项目需求文档\n\n## 核心功能\n- 功能1\n- 功能2\n\n## 技术要求\n- 要求1\n- 要求2",
            "# 系统设计文档\n\n## 架构设计\n系统采用微服务架构\n\n## 数据库设计\n使用MySQL数据库",
            "# 用户需求分析\n\n## 用户画像\n目标用户群体\n\n## 功能需求\n核心功能列表"
        ]
        return random.choice(templates)
    
    def _determine_expected_intent(self, message: str, current_state: str) -> str:
        """确定期望的意图"""
        message_lower = message.lower()
        
        # 优先级规则：修改 > 重启 > 确认
        if any(word in message_lower for word in ["修改", "调整", "不对", "但是"]):
            return "modify"
        elif any(word in message_lower for word in ["重新开始", "新聊天", "重置"]):
            return "restart"
        elif any(word in message_lower for word in ["确认", "没问题", "ok", "好的"]):
            if current_state == "DOCUMENTING":
                return "confirm"
            else:
                return "unknown"
        elif any(word in message_lower for word in ["你好", "hello", "hi"]):
            return "greeting"
        else:
            return "unknown"
    
    def _determine_expected_transition(self, intent: str, current_state: str) -> Optional[str]:
        """确定期望的状态转换"""
        if intent == "confirm" and current_state == "DOCUMENTING":
            return "IDLE"
        elif intent == "restart":
            return "IDLE"
        elif intent == "modify" and current_state == "DOCUMENTING":
            return "DOCUMENTING"  # 保持在文档状态
        else:
            return None
    
    def save_to_file(self, filename: str = "tests/generated_test_data.json"):
        """保存生成的测试数据到文件"""
        # 转换dataclass对象为字典
        serializable_data = {}
        for key, value in self.generated_data.items():
            if isinstance(value, list) and value and hasattr(value[0], '__dict__'):
                serializable_data[key] = [asdict(item) for item in value]
            else:
                serializable_data[key] = value
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试数据已保存到: {filename}")
    
    def generate_all(self, session_count: int = 10, input_count: int = 50):
        """生成所有类型的测试数据"""
        print("🔄 生成测试会话数据...")
        self.generate_test_sessions(session_count)
        
        print("🎯 生成关注点测试数据...")
        self.generate_test_focus_points(session_count)
        
        print("📄 生成文档测试数据...")
        self.generate_test_documents(session_count // 2)
        
        print("💬 生成用户输入测试数据...")
        self.generate_test_user_inputs(input_count)
        
        print("⚠️ 生成边界条件测试数据...")
        self.generate_edge_cases()
        
        print("🔄 生成状态转换测试数据...")
        self.generate_state_transition_tests()
        
        print("✅ 所有测试数据生成完成！")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="业务逻辑测试数据生成器")
    parser.add_argument("--type", choices=["all", "sessions", "focus_points", "documents", 
                                          "user_inputs", "edge_cases", "state_transitions"],
                       default="all", help="生成的数据类型")
    parser.add_argument("--count", type=int, default=10, help="生成数据的数量")
    parser.add_argument("--output", default="tests/generated_test_data.json", 
                       help="输出文件路径")
    
    args = parser.parse_args()
    
    generator = TestDataGenerator()
    
    if args.type == "all":
        generator.generate_all(args.count, args.count * 5)
    elif args.type == "sessions":
        generator.generate_test_sessions(args.count)
    elif args.type == "focus_points":
        generator.generate_test_focus_points(args.count)
    elif args.type == "documents":
        generator.generate_test_documents(args.count)
    elif args.type == "user_inputs":
        generator.generate_test_user_inputs(args.count)
    elif args.type == "edge_cases":
        generator.generate_edge_cases()
    elif args.type == "state_transitions":
        generator.generate_state_transition_tests()
    
    generator.save_to_file(args.output)

if __name__ == "__main__":
    main()
