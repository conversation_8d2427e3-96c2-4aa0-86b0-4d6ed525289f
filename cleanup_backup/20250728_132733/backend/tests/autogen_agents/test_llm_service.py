"""
AutoGen LLM服务Agent测试
"""
import pytest
from unittest.mock import AsyncMock, MagicMock
from backend.agents.llm_service import AutoGenLLMServiceAgent

class TestAutoGenLLMServiceAgent:
    """AutoGenLLMServiceAgent测试类"""

    @pytest.fixture
    def agent(self):
        """测试Agent实例"""
        return AutoGenLLMServiceAgent()

    @pytest.fixture
    def mock_client(self):
        """Mock LLM客户端"""
        mock = AsyncMock()
        mock.chat.completions.create = AsyncMock()
        return mock

    @pytest.mark.asyncio
    async def test_call_llm_success(self, agent, mock_client):
        """测试成功调用LLM"""
        # 设置mock返回值
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "test response"
        mock_response.choices[0].message.role = "assistant"
        mock_response.choices[0].finish_reason = "stop"
        mock_response.model = "deepseek"
        mock_response.usage = MagicMock()
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 20
        mock_response.usage.total_tokens = 30
        
        mock_client.chat.completions.create.return_value = mock_response
        
        # 替换客户端
        agent.client = mock_client
        
        # 调用LLM
        messages = [{"role": "user", "content": "test"}]
        response = await agent.call_llm(messages)
        
        # 验证结果
        assert response["content"] == "test response"
        assert response["role"] == "assistant"
        assert response["model"] == "deepseek"
        assert agent.call_stats["successful_calls"] == 1

    @pytest.mark.asyncio
    async def test_call_llm_with_retry(self, agent, mock_client):
        """测试重试机制"""
        # 设置第一次调用失败，第二次成功
        mock_client.chat.completions.create.side_effect = [
            Exception("timeout"),
            MagicMock(choices=[MagicMock(message=MagicMock(content="success"))])
        ]
        
        agent.client = mock_client
        
        messages = [{"role": "user", "content": "test"}]
        response = await agent.call_llm(messages)
        
        assert response["content"] == "success"
        assert agent.call_stats["successful_calls"] == 1
        assert agent.call_stats["failed_calls"] == 0

    @pytest.mark.asyncio
    async def test_circuit_breaker(self, agent, mock_client):
        """测试断路器"""
        # 启用断路器
        agent.enable_circuit_breaker = True
        
        # 设置连续失败
        mock_client.chat.completions.create.side_effect = Exception("error")
        
        # 触发断路器
        for _ in range(agent.config.circuit_breaker.failure_threshold + 1):
            try:
                await agent.call_llm([{"role": "user", "content": "test"}])
            except:
                pass
        
        # 验证断路器已打开
        assert agent.circuit_breaker.is_open()
        assert agent.call_stats["circuit_breaks"] > 0

    @pytest.mark.asyncio
    async def test_get_stats(self, agent):
        """测试获取统计信息"""
        agent.call_stats = {
            "total_calls": 10,
            "successful_calls": 8,
            "failed_calls": 2,
            "total_latency": 5.0
        }
        
        stats = await agent.get_stats()
        assert stats["success_rate"] == 80.0
        assert stats["avg_latency"] == 5.0 / 8

    @pytest.mark.asyncio
    async def test_reset_stats(self, agent):
        """测试重置统计信息"""
        agent.call_stats = {
            "total_calls": 10,
            "successful_calls": 8,
            "failed_calls": 2,
            "total_latency": 5.0
        }
        
        await agent.reset_stats()
        assert agent.call_stats["total_calls"] == 0
        assert agent.call_stats["successful_calls"] == 0
