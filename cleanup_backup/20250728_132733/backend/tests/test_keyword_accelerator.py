#!/usr/bin/env python3
"""
关键词加速器测试

测试目标：
1. 验证关键词匹配的准确性
2. 验证业务逻辑保护机制
3. 验证性能提升效果
4. 验证回退机制的正确性
"""

import pytest
import time
from unittest.mock import Mock, AsyncMock

# 导入被测试的模块
try:
    from backend.accelerators.keyword_accelerator import (
        BusinessLogicProtectedKeywordAccelerator,
        BusinessContext,
        AcceleratedConversationFlow
    )
except ImportError as e:
    print(f"导入错误: {e}")
    # 如果导入失败，跳过测试
    pytest.skip("无法导入关键词加速器模块", allow_module_level=True)


class TestBusinessLogicProtectedKeywordAccelerator:
    """关键词加速器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.accelerator = BusinessLogicProtectedKeywordAccelerator()
        self.test_context = BusinessContext(
            current_state="IDLE",
            user_id="test_user_001",
            session_id="test_session_001",
            has_document=False,
            focus_points_completed=False,
            conversation_turn=1
        )
    
    @pytest.mark.asyncio
    async def test_greeting_keyword_match(self):
        """测试问候关键词匹配"""
        test_cases = [
            "你好",
            "hello",
            "hi",
            "早上好",
            "下午好",
            "晚上好"
        ]
        
        for message in test_cases:
            result = await self.accelerator.accelerated_match(message, self.test_context)
            
            assert not result.requires_fallback, f"问候语 '{message}' 应该被加速处理"
            assert result.intent == "greeting", f"问候语 '{message}' 应该识别为 greeting"
            assert result.confidence >= 0.9, f"问候语 '{message}' 置信度应该很高"
            assert result.processing_time < 0.01, f"问候语 '{message}' 处理时间应该很短"
    
    @pytest.mark.asyncio
    async def test_capabilities_keyword_match(self):
        """测试能力查询关键词匹配"""
        test_cases = [
            "你能做什么",
            "你会什么",
            "你的功能",
            "介绍自己"
        ]
        
        for message in test_cases:
            result = await self.accelerator.accelerated_match(message, self.test_context)
            
            assert not result.requires_fallback, f"能力查询 '{message}' 应该被加速处理"
            assert result.intent == "capabilities", f"能力查询 '{message}' 应该识别为 capabilities"
    
    @pytest.mark.asyncio
    async def test_restart_keyword_match(self):
        """测试重启关键词匹配"""
        test_cases = [
            "新聊天",
            "重新开始",
            "new chat",
            "新需求"
        ]
        
        for message in test_cases:
            result = await self.accelerator.accelerated_match(message, self.test_context)
            
            assert not result.requires_fallback, f"重启指令 '{message}' 应该被加速处理"
            assert result.intent == "restart", f"重启指令 '{message}' 应该识别为 restart"
    
    @pytest.mark.asyncio
    async def test_document_confirmation_state_restriction(self):
        """测试文档确认的状态限制"""
        # 在非DOCUMENTING状态下测试确认关键词
        idle_context = BusinessContext(
            current_state="IDLE",
            user_id="test_user_001",
            session_id="test_session_001",
            has_document=False
        )
        
        result = await self.accelerator.accelerated_match("确认", idle_context)
        assert result.requires_fallback, "在IDLE状态下的确认应该回退到原系统"
        
        # 在DOCUMENTING状态下测试确认关键词
        documenting_context = BusinessContext(
            current_state="DOCUMENTING",
            user_id="test_user_001",
            session_id="test_session_001",
            has_document=True
        )
        
        result = await self.accelerator.accelerated_match("确认", documenting_context)
        assert not result.requires_fallback, "在DOCUMENTING状态下的确认应该被加速处理"
        assert result.intent == "confirm", "应该识别为确认意图"
    
    @pytest.mark.asyncio
    async def test_keyword_priority(self):
        """测试关键词优先级"""
        documenting_context = BusinessContext(
            current_state="DOCUMENTING",
            user_id="test_user_001",
            session_id="test_session_001",
            has_document=True
        )
        
        # 测试混合关键词：修改词优先级高于确认词
        result = await self.accelerator.accelerated_match("好的，但是需要修改", documenting_context)
        
        assert not result.requires_fallback, "混合关键词应该被加速处理"
        assert result.intent == "modify", "修改词优先级应该高于确认词"
    
    @pytest.mark.asyncio
    async def test_business_logic_validation(self):
        """测试业务逻辑验证"""
        # 测试确认操作但没有文档的情况
        no_doc_context = BusinessContext(
            current_state="DOCUMENTING",
            user_id="test_user_001",
            session_id="test_session_001",
            has_document=False  # 没有文档
        )
        
        result = await self.accelerator.accelerated_match("确认", no_doc_context)
        assert result.requires_fallback, "没有文档时的确认操作应该回退"
        assert "没有待确认的文档" in str(result.fallback_reason) or result.requires_fallback
    
    @pytest.mark.asyncio
    async def test_input_validation(self):
        """测试输入验证"""
        # 测试空输入
        result = await self.accelerator.accelerated_match("", self.test_context)
        assert result.requires_fallback, "空输入应该回退"
        
        # 测试超长输入
        long_message = "x" * 10001
        result = await self.accelerator.accelerated_match(long_message, self.test_context)
        assert result.requires_fallback, "超长输入应该回退"
        
        # 测试无效上下文
        invalid_context = BusinessContext(
            current_state="IDLE",
            user_id="",  # 无效用户ID
            session_id="test_session_001"
        )
        result = await self.accelerator.accelerated_match("你好", invalid_context)
        assert result.requires_fallback, "无效上下文应该回退"
    
    @pytest.mark.asyncio
    async def test_performance_improvement(self):
        """测试性能提升"""
        # 重置统计
        self.accelerator.reset_performance_stats()
        
        # 执行多次匹配
        test_messages = ["你好", "你能做什么", "新聊天"] * 10
        
        start_time = time.time()
        for message in test_messages:
            await self.accelerator.accelerated_match(message, self.test_context)
        total_time = time.time() - start_time
        
        # 验证性能
        stats = self.accelerator.get_performance_stats()
        assert stats["total_requests"] == 30, "应该处理30个请求"
        assert stats["keyword_matches"] > 0, "应该有关键词匹配成功"
        assert stats["avg_processing_time_ms"] < 10, "平均处理时间应该小于10ms"
        assert total_time < 1.0, "总处理时间应该小于1秒"
    
    @pytest.mark.asyncio
    async def test_fallback_for_complex_input(self):
        """测试复杂输入的回退机制"""
        complex_messages = [
            "我想开发一个电商平台，需要用户注册、商品管理、订单处理等功能",
            "请帮我分析一下这个项目的技术架构",
            "我的需求比较复杂，涉及多个模块"
        ]
        
        for message in complex_messages:
            result = await self.accelerator.accelerated_match(message, self.test_context)
            assert result.requires_fallback, f"复杂输入 '{message[:30]}...' 应该回退到原系统"


class TestAcceleratedConversationFlow:
    """加速会话流程测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建模拟的原始会话流程
        self.mock_original_flow = Mock()
        self.mock_original_flow.process_message = AsyncMock()
        
        # 创建加速会话流程
        self.accelerated_flow = AcceleratedConversationFlow(self.mock_original_flow)
    
    @pytest.mark.asyncio
    async def test_accelerated_processing(self):
        """测试加速处理"""
        # 测试简单问候
        result = await self.accelerated_flow.process_message(
            "你好", "test_session", "test_user", "IDLE"
        )
        
        assert result["processing_method"] == "keyword_accelerated", "应该使用关键词加速"
        assert result["intent"] == "greeting", "应该识别为问候"
        assert "您好" in result["content"], "应该包含问候响应"
        
        # 验证原系统没有被调用
        self.mock_original_flow.process_message.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_fallback_to_original(self):
        """测试回退到原系统"""
        # 设置原系统的返回值
        self.mock_original_flow.process_message.return_value = {
            "content": "原系统处理结果",
            "action_type": "complex_processing"
        }
        
        # 测试复杂输入
        complex_message = "我想开发一个复杂的企业级应用系统"
        result = await self.accelerated_flow.process_message(
            complex_message, "test_session", "test_user", "IDLE"
        )
        
        # 验证回退到原系统
        self.mock_original_flow.process_message.assert_called_once()
        assert result["content"] == "原系统处理结果", "应该返回原系统的结果"
    
    @pytest.mark.asyncio
    async def test_performance_statistics(self):
        """测试性能统计"""
        # 执行一些加速处理和回退处理
        await self.accelerated_flow.process_message("你好", "test_session", "test_user", "IDLE")
        await self.accelerated_flow.process_message("你能做什么", "test_session", "test_user", "IDLE")
        
        # 设置原系统返回值并执行回退处理
        self.mock_original_flow.process_message.return_value = {"content": "原系统结果"}
        await self.accelerated_flow.process_message("复杂需求", "test_session", "test_user", "IDLE")
        
        # 检查统计
        stats = self.accelerated_flow.get_performance_stats()
        assert stats["total_requests"] == 3, "应该有3个总请求"
        assert stats["accelerated_requests"] == 2, "应该有2个加速请求"
        assert stats["fallback_requests"] == 1, "应该有1个回退请求"
        assert stats["acceleration_rate"] > 60, "加速率应该大于60%"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
