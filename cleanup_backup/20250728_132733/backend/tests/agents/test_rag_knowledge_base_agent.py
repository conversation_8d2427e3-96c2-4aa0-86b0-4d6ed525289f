"""
RAG知识库代理单元测试
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import tempfile

from backend.agents.rag_knowledge_base_agent import RAGKnowledgeBaseAgent, RAGQueryResult
from backend.config.knowledge_base_config import KnowledgeBaseConfig


class TestRAGKnowledgeBaseAgent(unittest.TestCase):
    """测试RAG知识库代理"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的LLM服务
        self.mock_llm_service = AsyncMock()
        self.mock_llm_service.call_llm = AsyncMock(return_value={
            "content": "这是一个测试回答",
            "model": "test-model",
            "duration": 0.5
        })
        
        # 创建临时目录用于ChromaDB
        self.temp_dir = tempfile.mkdtemp()
        
        # 模拟配置
        self.mock_config = KnowledgeBaseConfig(
            enabled=True,
            features={
                "rag_query": True,
                "intent_enhancement": False,
                "mode_switching": False,
                "document_ingestion": False
            },
            chroma_db={
                "path": self.temp_dir,
                "collection_name": "test_collection",
                "embedding_model": "test-model"
            },
            retrieval={
                "top_k": 3,
                "similarity_threshold": 0.7,
                "max_context_length": 2000
            },
            safety={
                "timeout_seconds": 5,
                "max_retry_attempts": 2
            },
            role_filters={
                "enabled": True,
                "available_roles": ["company", "developer"]
            }
        )
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_initialization_success(self, mock_chroma_client, mock_config_manager):
        """测试成功初始化"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        # 创建代理
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 验证初始化
        self.assertEqual(agent.llm_service, self.mock_llm_service)
        self.assertEqual(agent.agent_name, "rag_knowledge_base_agent")
        self.assertEqual(agent.top_k, 3)
        self.assertEqual(agent.similarity_threshold, 0.7)
        
        # 验证ChromaDB客户端初始化
        mock_chroma_client.assert_called_once()
        mock_client.get_collection.assert_called_once_with(name="test_collection")
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    def test_initialization_chroma_failure(self, mock_config_manager):
        """测试ChromaDB初始化失败"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_config_manager.return_value = mock_manager
        
        # 模拟ChromaDB连接失败
        with patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient') as mock_client:
            mock_client.side_effect = Exception("连接失败")
            
            agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
            
            # 验证失败处理
            self.assertIsNone(agent._chroma_client)
            self.assertIsNone(agent._collection)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_query_feature_disabled(self, mock_chroma_client, mock_config_manager):
        """测试功能禁用时的查询"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = False  # 功能禁用
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 执行查询
        result = asyncio.run(agent.query("测试问题"))
        
        # 验证结果
        self.assertIsInstance(result, RAGQueryResult)
        self.assertFalse(result.success)
        self.assertEqual(result.error, "feature_disabled")
        self.assertIn("未启用", result.answer)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_query_no_results(self, mock_chroma_client, mock_config_manager):
        """测试查询无结果"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.query.return_value = {
            'documents': [[]],  # 空结果
            'metadatas': [[]],
            'distances': [[]]
        }
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 执行查询
        result = asyncio.run(agent.query("测试问题"))
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertEqual(len(result.sources), 0)
        self.assertIn("没有找到相关信息", result.answer)
        self.assertEqual(result.processing_info["retrieved_docs"], 0)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_query_with_results(self, mock_chroma_client, mock_config_manager):
        """测试查询有结果"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.query.return_value = {
            'documents': [["这是测试文档内容"]],
            'metadatas': [[{"role": "company", "source_file": "test.md"}]],
            'distances': [[50.0]]  # 低距离表示高相似度
        }
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 执行查询
        result = asyncio.run(agent.query("测试问题"))
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertEqual(len(result.sources), 1)
        self.assertEqual(result.sources[0]["role"], "company")
        self.assertEqual(result.sources[0]["document"], "company/test.md")
        self.assertGreater(result.sources[0]["relevance_score"], 0.7)
        self.assertEqual(result.processing_info["retrieved_docs"], 1)
        
        # 验证LLM调用
        self.mock_llm_service.call_llm.assert_called_once()
        call_args = self.mock_llm_service.call_llm.call_args
        self.assertEqual(call_args[1]["agent_name"], "rag_knowledge_base_agent")
        self.assertEqual(call_args[1]["scenario"], "knowledge_base_rag")
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_query_with_role_filter(self, mock_chroma_client, mock_config_manager):
        """测试带角色过滤的查询"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.query.return_value = {
            'documents': [["公司相关文档内容"]],
            'metadatas': [[{"role": "company", "source_file": "company.md"}]],
            'distances': [[30.0]]
        }
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 执行带角色过滤的查询
        context = {"role_filter": "company"}
        result = asyncio.run(agent.query("测试问题", context))
        
        # 验证结果
        self.assertTrue(result.success)
        
        # 验证查询参数包含角色过滤
        mock_collection.query.assert_called_once()
        call_args = mock_collection.query.call_args[1]
        self.assertIn("where", call_args)
        self.assertEqual(call_args["where"]["role"]["$eq"], "company")
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_query_llm_failure(self, mock_chroma_client, mock_config_manager):
        """测试LLM调用失败的降级处理"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.query.return_value = {
            'documents': [["测试文档内容"]],
            'metadatas': [[{"role": "company", "source_file": "test.md"}]],
            'distances': [[40.0]]
        }
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        # 模拟LLM调用失败
        self.mock_llm_service.call_llm.side_effect = Exception("LLM调用失败")
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 执行查询
        result = asyncio.run(agent.query("测试问题"))
        
        # 验证降级处理
        self.assertTrue(result.success)
        self.assertIn("根据知识库中的相关信息", result.answer)
        self.assertIn("测试文档内容", result.answer)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_get_collection_stats(self, mock_chroma_client, mock_config_manager):
        """测试获取集合统计信息"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.count.return_value = 10
        mock_collection.get.return_value = {
            'metadatas': [
                {"role": "company"},
                {"role": "company"},
                {"role": "developer"}
            ]
        }
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 获取统计信息
        stats = agent.get_collection_stats()
        
        # 验证结果
        self.assertEqual(stats["total_documents"], 10)
        self.assertEqual(stats["collection_name"], "test_collection")
        self.assertEqual(stats["role_distribution"]["company"], 2)
        self.assertEqual(stats["role_distribution"]["developer"], 1)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    @patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient')
    def test_health_check(self, mock_chroma_client, mock_config_manager):
        """测试健康检查"""
        # 设置模拟
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.count.return_value = 5
        mock_client.get_collection.return_value = mock_collection
        mock_chroma_client.return_value = mock_client
        
        agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
        
        # 执行健康检查
        health = agent.health_check()
        
        # 验证结果
        self.assertTrue(health["healthy"])
        self.assertEqual(health["checks"]["config"]["status"], "ok")
        self.assertEqual(health["checks"]["chromadb"]["status"], "ok")
        self.assertEqual(health["checks"]["llm_service"]["status"], "ok")
        self.assertEqual(health["checks"]["chromadb"]["document_count"], 5)
    
    @patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager')
    def test_health_check_unhealthy(self, mock_config_manager):
        """测试不健康状态的健康检查"""
        # 设置模拟 - 功能禁用
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_feature_enabled.return_value = False
        mock_config_manager.return_value = mock_manager
        
        # 模拟ChromaDB连接失败
        with patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient') as mock_client:
            mock_client.side_effect = Exception("连接失败")
            
            agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
            
            # 执行健康检查
            health = agent.health_check()
            
            # 验证结果
            self.assertFalse(health["healthy"])
            self.assertEqual(health["checks"]["config"]["status"], "disabled")
            self.assertEqual(health["checks"]["chromadb"]["status"], "error")
    
    def test_build_context_text(self):
        """测试构建上下文文本"""
        with patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager') as mock_config_manager:
            mock_manager = Mock()
            mock_manager.get_config.return_value = self.mock_config
            mock_config_manager.return_value = mock_manager
            
            with patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient'):
                agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
                
                # 测试数据
                relevant_docs = [
                    {
                        "content": "这是第一个文档的内容",
                        "metadata": {"source_file": "doc1.md", "role": "company"}
                    },
                    {
                        "content": "这是第二个文档的内容",
                        "metadata": {"source_file": "doc2.md", "role": "developer"}
                    }
                ]
                
                # 构建上下文
                context_text = agent._build_context_text(relevant_docs)
                
                # 验证结果
                self.assertIn("doc1.md", context_text)
                self.assertIn("doc2.md", context_text)
                self.assertIn("company", context_text)
                self.assertIn("developer", context_text)
                self.assertIn("这是第一个文档的内容", context_text)
                self.assertIn("这是第二个文档的内容", context_text)
    
    def test_build_rag_prompt(self):
        """测试构建RAG提示词"""
        with patch('backend.agents.rag_knowledge_base_agent.get_knowledge_base_config_manager') as mock_config_manager:
            mock_manager = Mock()
            mock_manager.get_config.return_value = self.mock_config
            mock_config_manager.return_value = mock_manager
            
            with patch('backend.agents.rag_knowledge_base_agent.chromadb.PersistentClient'):
                agent = RAGKnowledgeBaseAgent(self.mock_llm_service)
                
                # 测试不带角色过滤的提示词
                prompt = agent._build_rag_prompt("测试问题", "测试上下文", None)
                self.assertIn("测试问题", prompt)
                self.assertIn("测试上下文", prompt)
                self.assertNotIn("雇主", prompt)
                
                # 测试带角色过滤的提示词
                context = {"role_filter": "company"}
                prompt = agent._build_rag_prompt("测试问题", "测试上下文", context)
                self.assertIn("雇主", prompt)


if __name__ == '__main__':
    unittest.main()