"""
测试HybridConversationRouter的动作映射修复
验证handle_domain_knowledge_question动作能正确路由到knowledge_base模式
"""

import unittest
from unittest.mock import Mock, patch

from backend.agents.hybrid_conversation_router import HybridConversationRouter, ConversationMode


class TestHybridConversationRouterFix(unittest.TestCase):
    """测试HybridConversationRouter的动作映射修复"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的组件
        self.mock_rag_agent = Mock()
        self.mock_requirement_flow = Mock()
        self.mock_intent_engine = Mock()
        
        # 创建路由器实例
        with patch('backend.agents.hybrid_conversation_router.get_knowledge_base_config_manager'):
            self.router = HybridConversationRouter(
                rag_agent=self.mock_rag_agent,
                requirement_flow=self.mock_requirement_flow,
                intent_engine=self.mock_intent_engine
            )
    
    def test_determine_target_mode_handle_domain_knowledge_question(self):
        """测试handle_domain_knowledge_question动作的路由"""
        # 模拟意图识别结果
        intent_result = {
            "knowledge_base_query": False,  # 不通过knowledge_base_query标志
            "decision": {
                "action": "handle_domain_knowledge_question",
                "priority": 5
            },
            "intent": "domain_knowledge_question",
            "confidence": 0.8
        }
        
        # 测试路由决策
        target_mode = self.router._determine_target_mode(intent_result)
        
        # 验证路由到知识库模式
        self.assertEqual(target_mode, ConversationMode.KNOWLEDGE_BASE)
    
    def test_determine_target_mode_clarify_domain_knowledge_question(self):
        """测试clarify_domain_knowledge_question动作的路由"""
        intent_result = {
            "knowledge_base_query": False,
            "decision": {
                "action": "clarify_domain_knowledge_question",
                "priority": 6
            },
            "intent": "domain_knowledge_question",
            "confidence": 0.7
        }
        
        target_mode = self.router._determine_target_mode(intent_result)
        self.assertEqual(target_mode, ConversationMode.KNOWLEDGE_BASE)
    
    def test_determine_target_mode_knowledge_base_query_flag(self):
        """测试通过knowledge_base_query标志的路由"""
        intent_result = {
            "knowledge_base_query": True,  # 通过HybridIntentRecognitionEngine的判断
            "decision": {
                "action": "some_other_action",
                "priority": 5
            },
            "intent": "操作指导",
            "confidence": 0.9
        }
        
        target_mode = self.router._determine_target_mode(intent_result)
        self.assertEqual(target_mode, ConversationMode.KNOWLEDGE_BASE)
    
    def test_determine_target_mode_requirement_actions(self):
        """测试需求采集动作的路由"""
        requirement_actions = [
            "start_requirement_gathering",
            "continue_requirement_gathering",
            "domain_classification",
            "category_selection",
            "focus_point_tracking"
        ]
        
        for action in requirement_actions:
            with self.subTest(action=action):
                intent_result = {
                    "knowledge_base_query": False,
                    "decision": {
                        "action": action,
                        "priority": 5
                    }
                }
                
                target_mode = self.router._determine_target_mode(intent_result)
                self.assertEqual(target_mode, ConversationMode.REQUIREMENT_COLLECTION)
    
    def test_determine_target_mode_clarification_actions(self):
        """测试澄清动作的路由"""
        clarification_actions = [
            "show_empathy_and_clarify",
            "ask_for_clarification",
            "respond_to_general_chat"
        ]
        
        for action in clarification_actions:
            with self.subTest(action=action):
                intent_result = {
                    "knowledge_base_query": False,
                    "decision": {
                        "action": action,
                        "priority": 5
                    }
                }
                
                target_mode = self.router._determine_target_mode(intent_result)
                self.assertEqual(target_mode, ConversationMode.CLARIFICATION)
    
    def test_determine_target_mode_unknown_action(self):
        """测试未知动作的默认路由"""
        intent_result = {
            "knowledge_base_query": False,
            "decision": {
                "action": "unknown_action",
                "priority": 5
            }
        }
        
        target_mode = self.router._determine_target_mode(intent_result)
        # 修复后，未知动作应该路由到澄清模式而不是需求采集
        self.assertEqual(target_mode, ConversationMode.CLARIFICATION)
    
    def test_determine_target_mode_empty_action(self):
        """测试空动作的默认路由"""
        intent_result = {
            "knowledge_base_query": False,
            "decision": {
                "action": "",
                "priority": 5
            }
        }
        
        target_mode = self.router._determine_target_mode(intent_result)
        self.assertEqual(target_mode, ConversationMode.CLARIFICATION)
    
    def test_determine_target_mode_no_decision(self):
        """测试没有decision字段的情况"""
        intent_result = {
            "knowledge_base_query": False,
            "intent": "some_intent",
            "confidence": 0.8
        }
        
        target_mode = self.router._determine_target_mode(intent_result)
        self.assertEqual(target_mode, ConversationMode.CLARIFICATION)
    
    def test_knowledge_base_actions_completeness(self):
        """测试知识库动作列表的完整性"""
        # 这些是应该路由到知识库模式的所有动作
        expected_kb_actions = [
            "handle_domain_knowledge_question",
            "clarify_domain_knowledge_question",
            "provide_knowledge_base_answer",
            "search_knowledge_base",
            "answer_question"
        ]
        
        for action in expected_kb_actions:
            with self.subTest(action=action):
                intent_result = {
                    "knowledge_base_query": False,
                    "decision": {
                        "action": action,
                        "priority": 5
                    }
                }
                
                target_mode = self.router._determine_target_mode(intent_result)
                self.assertEqual(target_mode, ConversationMode.KNOWLEDGE_BASE,
                               f"动作 '{action}' 应该路由到知识库模式")


def run_tests():
    """运行测试"""
    unittest.main(verbosity=2)


if __name__ == '__main__':
    run_tests()
