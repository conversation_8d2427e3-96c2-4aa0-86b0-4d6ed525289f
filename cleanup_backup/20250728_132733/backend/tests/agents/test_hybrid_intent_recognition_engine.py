"""
增强型意图识别引擎单元测试（雇主专用简化版）
"""

import unittest
from unittest.mock import Mock, patch, AsyncMock

from backend.agents.hybrid_intent_recognition_engine import HybridIntentRecognitionEngine
from backend.config.knowledge_base_config import KnowledgeBaseConfig


class TestHybridIntentRecognitionEngine(unittest.TestCase):
    """测试增强型意图识别引擎（雇主专用简化版）"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的LLM服务
        self.mock_llm_service = AsyncMock()

        # 模拟配置（雇主专用简化版）
        self.mock_config = KnowledgeBaseConfig(
            enabled=True,
            features={
                "rag_query": True,
                "intent_enhancement": True,
                "mode_switching": False,
                "document_ingestion": False
            },
            role_filters={
                "enabled": False,  # 雇主专用系统禁用角色过滤
                "available_roles": []
            }
        )
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    def test_initialization(self, mock_config_manager):
        """测试初始化（雇主专用简化版）"""
        mock_manager = Mock()
        mock_manager.get_config.return_value = self.mock_config
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_config_manager.return_value = mock_manager

        engine = HybridIntentRecognitionEngine(self.mock_llm_service)

        self.assertEqual(engine.llm_client, self.mock_llm_service)
        self.assertIsNotNone(engine.kb_config_manager)
        # 验证意图映射配置
        self.assertIn("操作指导", engine.knowledge_base_intent_mapping)
        self.assertTrue(engine.knowledge_base_intent_mapping["操作指导"])
        self.assertFalse(engine.knowledge_base_intent_mapping["需求描述"])
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_analyze_knowledge_base_disabled(self, mock_config_manager):
        """测试知识库功能禁用时的分析"""
        # 设置配置为禁用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = False
        mock_config_manager.return_value = mock_manager
        
        # 模拟父类方法
        with patch.object(HybridIntentRecognitionEngine, '__bases__', (Mock,)):
            engine = HybridIntentRecognitionEngine(self.mock_llm_service)
            
            # 模拟父类的analyze方法
            original_result = {
                "intent": "需求描述",
                "confidence": 0.8,
                "emotion": "neutral",
                "entities": {}
            }
            
            with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze', 
                      return_value=original_result) as mock_super_analyze:
                
                result = await engine.analyze("我想开发一个网站")
                
                # 验证调用了父类方法
                mock_super_analyze.assert_called_once()
                
                # 验证结果格式
                self.assertFalse(result["knowledge_base_query"])
                self.assertEqual(result["processing_mode"], "requirement_collection")
                self.assertEqual(result["intent"], "需求描述")
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_analyze_knowledge_base_enabled_query(self, mock_config_manager):
        """测试知识库功能启用时的查询分析（雇主专用简化版）"""
        # 设置配置为启用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager

        engine = HybridIntentRecognitionEngine(self.mock_llm_service)

        # 模拟三层识别返回操作指导意图
        original_result = {
            "intent": "操作指导",
            "sub_intent": "账号注册",
            "confidence": 0.9,
            "emotion": "neutral",
            "entities": {}
        }

        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze',
                  return_value=original_result) as mock_super_analyze:

            result = await engine.analyze("如何注册雇主账号？")

            # 验证调用了三层识别
            mock_super_analyze.assert_called_once()

            # 验证结果（基于意图映射判断为知识库查询）
            self.assertTrue(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "knowledge_base")
            self.assertEqual(result["intent"], "操作指导")
            self.assertEqual(result["sub_intent"], "账号注册")
            # 雇主专用版不再有role_filter
            self.assertNotIn("role_filter", result)
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_analyze_knowledge_base_enabled_non_query(self, mock_config_manager):
        """测试知识库功能启用时的非查询分析（雇主专用简化版）"""
        # 设置配置为启用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_config_manager.return_value = mock_manager

        engine = HybridIntentRecognitionEngine(self.mock_llm_service)

        # 模拟三层识别返回需求描述意图
        original_result = {
            "intent": "需求描述",
            "confidence": 0.8,
            "emotion": "neutral",
            "entities": {}
        }

        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze',
                  return_value=original_result) as mock_super_analyze:

            # 测试项目需求描述（应该路由到需求采集）
            result = await engine.analyze("我想做一个电商网站")

            # 验证调用了三层识别
            mock_super_analyze.assert_called_once()

            # 验证结果（基于意图映射判断为需求采集）
            self.assertFalse(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "requirement_collection")
            self.assertEqual(result["intent"], "需求描述")
    
    def test_heuristic_knowledge_base_check(self):
        """测试启发式知识库查询检查（雇主专用简化版）"""
        with patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager'):
            engine = HybridIntentRecognitionEngine(self.mock_llm_service)

            # 测试包含查询指示词的意图
            self.assertTrue(engine._heuristic_knowledge_base_check("信息查询", "平台功能"))
            self.assertTrue(engine._heuristic_knowledge_base_check("用户询问", "如何操作"))

            # 测试短消息 + 疑问句
            self.assertTrue(engine._heuristic_knowledge_base_check("未知意图", "这是什么？"))
            self.assertTrue(engine._heuristic_knowledge_base_check("未知意图", "怎么办?"))

            # 测试不符合条件的情况
            self.assertFalse(engine._heuristic_knowledge_base_check("需求描述", "我想开发一个网站"))
            self.assertFalse(engine._heuristic_knowledge_base_check("项目规划", "这是一个很长的消息，不包含疑问句，也不包含查询指示词"))

    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_employer_specific_test_cases(self, mock_config_manager):
        """测试雇主专用测试用例（根据重构文档）"""
        # 设置配置为启用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_config_manager.return_value = mock_manager

        engine = HybridIntentRecognitionEngine(self.mock_llm_service)

        # 测试用例1：发布任务咨询
        original_result = {
            "intent": "操作指导",
            "confidence": 0.9,
            "emotion": "neutral",
            "entities": {}
        }

        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze',
                  return_value=original_result):
            result = await engine.analyze("如何发布开发任务？")
            self.assertTrue(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "knowledge_base")

        # 测试用例2：用工流程咨询
        original_result["intent"] = "流程咨询"
        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze',
                  return_value=original_result):
            result = await engine.analyze("专家用工的流程是什么？")
            self.assertTrue(result["knowledge_base_query"])

        # 测试用例3：项目需求描述
        original_result["intent"] = "需求描述"
        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze',
                  return_value=original_result):
            result = await engine.analyze("我想做一个电商网站")
            self.assertFalse(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "requirement_collection")

        # 测试用例4：平台功能咨询
        original_result["intent"] = "功能介绍"
        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze',
                  return_value=original_result):
            result = await engine.analyze("平台有哪些功能？")
            self.assertTrue(result["knowledge_base_query"])
    
    def test_convert_to_hybrid_result(self):
        """测试结果格式转换（雇主专用简化版）"""
        with patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager'):
            engine = HybridIntentRecognitionEngine(self.mock_llm_service)

            original_result = {
                "intent": "需求描述",
                "confidence": 0.8,
                "emotion": "neutral",
                "entities": {"type": "website"}
            }

            # 测试非知识库查询转换
            result = engine._convert_to_hybrid_result(original_result, knowledge_base_query=False)
            self.assertEqual(result["intent"], "需求描述")
            self.assertFalse(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "requirement_collection")
            # 雇主专用版不再有role_filter字段
            self.assertNotIn("role_filter", result)

            # 测试知识库查询转换
            result = engine._convert_to_hybrid_result(original_result, knowledge_base_query=True)
            self.assertTrue(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "knowledge_base")
            # 雇主专用版不再有role_filter字段
            self.assertNotIn("role_filter", result)
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    def test_get_supported_intents(self, mock_config_manager):
        """测试获取支持的意图类型"""
        mock_manager = Mock()
        mock_config_manager.return_value = mock_manager
        
        engine = HybridIntentRecognitionEngine(self.mock_llm_service)
        
        intents = engine.get_supported_intents()
        
        # 验证包含知识库相关意图
        self.assertIn("操作指导", intents)
        self.assertIn("信息查询", intents)
        self.assertIn("问题解答", intents)
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    def test_get_engine_info(self, mock_config_manager):
        """测试获取引擎信息（雇主专用简化版）"""
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_manager.is_feature_enabled.return_value = True
        mock_config_manager.return_value = mock_manager

        engine = HybridIntentRecognitionEngine(self.mock_llm_service)

        info = engine.get_engine_info()

        self.assertEqual(info["engine_type"], "HybridIntentRecognitionEngine")
        self.assertTrue(info["knowledge_base_enabled"])
        self.assertTrue(info["intent_enhancement_enabled"])
        # 雇主专用版不再有supported_roles字段
        self.assertNotIn("supported_roles", info)
        self.assertEqual(info["version"], "2.0.0")  # 雇主专用简化版
        self.assertEqual(info["features"], "employer_simplified_version")
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_analyze_with_exception_handling(self, mock_config_manager):
        """测试异常处理"""
        # 设置配置为启用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        # 模拟LLM调用失败
        self.mock_llm_service.call_llm.side_effect = Exception("LLM调用失败")
        
        # 模拟父类方法
        original_result = {
            "intent": "需求描述",
            "confidence": 0.8,
            "emotion": "neutral",
            "entities": {}
        }
        
        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze', 
                  return_value=original_result) as mock_super_analyze:
            
            engine = HybridIntentRecognitionEngine(self.mock_llm_service)
            
            # 测试异常时的降级处理
            result = await engine.analyze("如何注册账号？")
            
            # 验证降级到原有逻辑
            mock_super_analyze.assert_called_once()
            self.assertFalse(result["knowledge_base_query"])
            self.assertEqual(result["processing_mode"], "requirement_collection")


if __name__ == '__main__':
    unittest.main()