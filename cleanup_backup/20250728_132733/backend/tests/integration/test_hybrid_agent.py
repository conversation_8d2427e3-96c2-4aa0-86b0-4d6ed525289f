"""
混合AI代理集成测试

测试混合AI代理系统的端到端功能，包括：
1. 配置开关的正确性
2. 知识库问答和需求采集的切换
3. 意图识别的准确性
4. 错误处理和回退机制
"""

import pytest
import os
import sys
from unittest.mock import Mock, AsyncMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from backend.agents.factory import agent_factory
from backend.config.knowledge_base_config import get_knowledge_base_config_manager
from backend.agents.hybrid_conversation_router import ConversationMode


class TestHybridAgentIntegration:
    """混合AI代理集成测试类"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        mock_service = AsyncMock()
        mock_service.call_llm.return_value = {
            "content": "这是一个测试回复",
            "usage": {"total_tokens": 100}
        }
        return mock_service
    
    @pytest.fixture
    def mock_chroma_client(self):
        """模拟ChromaDB客户端"""
        mock_client = Mock()
        mock_collection = Mock()
        mock_collection.query.return_value = {
            "documents": [["测试文档内容"]],
            "metadatas": [[{"source": "test.md"}]],
            "distances": [[0.1]]
        }
        mock_client.get_collection.return_value = mock_collection
        return mock_client
    
    @pytest.fixture
    async def hybrid_agent_setup(self, mock_llm_service, mock_chroma_client):
        """设置混合代理测试环境"""
        # 模拟配置管理器
        get_knowledge_base_config_manager()
        
        # 创建测试会话
        session_id = "test_session_hybrid"
        
        # 获取混合代理组件
        try:
            kb_config_manager = agent_factory.get_knowledge_base_config_manager()
            rag_agent = agent_factory.get_rag_knowledge_base_agent()
            intent_engine = agent_factory.get_hybrid_intent_recognition_engine()
            router = agent_factory.get_hybrid_conversation_router()
            
            return {
                "session_id": session_id,
                "kb_config_manager": kb_config_manager,
                "rag_agent": rag_agent,
                "intent_engine": intent_engine,
                "router": router,
                "mock_llm": mock_llm_service,
                "mock_chroma": mock_chroma_client
            }
        except Exception as e:
            pytest.skip(f"混合代理组件初始化失败: {e}")
    
    @pytest.mark.asyncio
    async def test_knowledge_base_disabled_by_default(self, hybrid_agent_setup):
        """测试知识库功能默认禁用"""
        setup = hybrid_agent_setup
        kb_config_manager = setup["kb_config_manager"]
        
        # 验证默认配置
        assert not kb_config_manager.is_knowledge_base_enabled()
        
        # 验证功能级别配置
        config = kb_config_manager.get_config()
        assert not config.enabled
        assert not config.features.get("rag_query", True)
    
    @pytest.mark.asyncio
    async def test_intent_recognition_with_kb_disabled(self, hybrid_agent_setup):
        """测试知识库禁用时的意图识别"""
        setup = hybrid_agent_setup
        intent_engine = setup["intent_engine"]
        
        # 测试消息
        test_messages = [
            "你好，我想开发一个网站",
            "如何注册账号？",
            "我需要帮助"
        ]
        
        for message in test_messages:
            result = await intent_engine.analyze(message)
            
            # 验证结果格式
            assert isinstance(result, dict)
            assert "knowledge_base_query" in result
            assert not result["knowledge_base_query"]  # 应该为False，因为功能禁用
            assert "decision" in result
    
    @pytest.mark.asyncio
    async def test_conversation_flow_with_hybrid_router_disabled(self, hybrid_agent_setup):
        """测试混合路由器禁用时的对话流程"""
        setup = hybrid_agent_setup
        session_id = setup["session_id"]
        
        # 创建对话流程Agent
        conversation_agent = agent_factory.get_conversation_flow_agent(session_id)
        
        # 测试消息处理
        test_message = "我想开发一个电商网站"
        result = await conversation_agent.process_message_async(test_message)
        
        # 验证结果
        assert result["success"]
        assert "reply" in result
        assert isinstance(result["reply"], str)
        assert len(result["reply"]) > 0
    
    @pytest.mark.asyncio
    async def test_enable_knowledge_base_functionality(self, hybrid_agent_setup):
        """测试启用知识库功能"""
        setup = hybrid_agent_setup
        kb_config_manager = setup["kb_config_manager"]
        
        # 启用知识库功能
        success = kb_config_manager.enable_knowledge_base()
        assert success
        assert kb_config_manager.is_knowledge_base_enabled()
        
        # 验证功能启用
        config = kb_config_manager.get_config()
        assert config.enabled
        
        # 清理：禁用功能
        kb_config_manager.disable_knowledge_base()
    
    @pytest.mark.asyncio
    async def test_hybrid_router_with_knowledge_base_enabled(self, hybrid_agent_setup):
        """测试启用知识库时的混合路由器"""
        setup = hybrid_agent_setup
        router = setup["router"]
        kb_config_manager = setup["kb_config_manager"]
        
        if not router:
            pytest.skip("混合路由器不可用")
        
        # 启用知识库功能
        kb_config_manager.enable_knowledge_base()
        
        try:
            # 构建测试上下文
            session_context = {
                "session_id": "test_session",
                "user_id": "test_user",
                "current_mode": None,
                "conversation_history": [],
                "current_state": {}
            }
            
            # 测试知识库查询
            kb_message = "如何注册账号？"
            result = await router.route_message(kb_message, session_context)
            
            # 验证结果
            assert isinstance(result.success, bool)
            assert isinstance(result.content, str)
            assert result.mode in [ConversationMode.KNOWLEDGE_BASE, ConversationMode.REQUIREMENT_COLLECTION, ConversationMode.CLARIFICATION]
            
        finally:
            # 清理：禁用功能
            kb_config_manager.disable_knowledge_base()
    
    @pytest.mark.asyncio
    async def test_mode_switching(self, hybrid_agent_setup):
        """测试模式切换功能"""
        setup = hybrid_agent_setup
        router = setup["router"]
        kb_config_manager = setup["kb_config_manager"]
        
        if not router:
            pytest.skip("混合路由器不可用")
        
        # 启用知识库功能
        kb_config_manager.enable_knowledge_base()
        
        try:
            session_context = {
                "session_id": "test_session",
                "user_id": "test_user",
                "current_mode": ConversationMode.CLARIFICATION.value,
                "conversation_history": [],
                "current_state": {}
            }
            
            # 测试从澄清模式切换到需求采集模式
            requirement_message = "我想开发一个社交应用"
            result = await router.route_message(requirement_message, session_context)
            
            # 验证模式切换
            assert result.mode in [ConversationMode.REQUIREMENT_COLLECTION, ConversationMode.KNOWLEDGE_BASE]
            assert "mode_switch" in result.processing_info
            
        finally:
            kb_config_manager.disable_knowledge_base()
    
    @pytest.mark.asyncio
    async def test_error_handling_and_fallback(self, hybrid_agent_setup):
        """测试错误处理和回退机制"""
        setup = hybrid_agent_setup
        router = setup["router"]
        
        if not router:
            pytest.skip("混合路由器不可用")
        
        # 测试异常情况
        invalid_context = None  # 无效上下文
        
        result = await router.route_message("测试消息", invalid_context)
        
        # 验证错误处理
        assert not result.success
        assert result.error is not None
        assert isinstance(result.content, str)
        assert result.mode == ConversationMode.UNKNOWN
    
    @pytest.mark.asyncio
    async def test_routing_statistics(self, hybrid_agent_setup):
        """测试路由统计功能"""
        setup = hybrid_agent_setup
        router = setup["router"]
        
        if not router:
            pytest.skip("混合路由器不可用")
        
        # 重置统计
        router.reset_routing_stats()
        initial_stats = router.get_routing_stats()
        
        assert initial_stats["total_requests"] == 0
        assert initial_stats["knowledge_base_requests"] == 0
        assert initial_stats["requirement_requests"] == 0
        
        # 执行一些路由操作
        session_context = {
            "session_id": "test_session",
            "user_id": "test_user",
            "current_mode": None,
            "conversation_history": [],
            "current_state": {}
        }
        
        await router.route_message("测试消息1", session_context)
        await router.route_message("测试消息2", session_context)
        
        # 验证统计更新
        updated_stats = router.get_routing_stats()
        assert updated_stats["total_requests"] == 2
    
    def test_factory_integration(self):
        """测试工厂集成"""
        # 验证混合代理服务已注册
        factory_info = agent_factory.get_factory_info()
        available_agents = factory_info["available_agents"]
        
        hybrid_agents = [
            "knowledge_base_config_manager",
            "rag_knowledge_base_agent", 
            "hybrid_intent_recognition_engine",
            "hybrid_conversation_router"
        ]
        
        for agent_name in hybrid_agents:
            assert agent_name in available_agents, f"混合代理 {agent_name} 未在工厂中注册"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
