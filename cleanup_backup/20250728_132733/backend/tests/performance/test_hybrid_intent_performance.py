"""
HybridIntentRecognitionEngine性能测试
验证雇主专用简化版的性能改进
"""

import time
import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock
from statistics import mean

from backend.agents.hybrid_intent_recognition_engine import HybridIntentRecognitionEngine
from backend.config.knowledge_base_config import KnowledgeBaseConfig


class TestHybridIntentPerformance(unittest.TestCase):
    """测试HybridIntentRecognitionEngine性能"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的LLM服务
        self.mock_llm_service = AsyncMock()
        
        # 模拟配置（雇主专用简化版）
        self.mock_config = KnowledgeBaseConfig(
            enabled=True,
            features={
                "rag_query": True,
                "intent_enhancement": True,
                "mode_switching": False,
                "document_ingestion": False
            },
            role_filters={
                "enabled": False,  # 雇主专用系统禁用角色过滤
                "available_roles": []
            }
        )
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_performance_knowledge_base_query(self, mock_config_manager):
        """测试知识库查询的性能"""
        # 设置配置为启用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        engine = HybridIntentRecognitionEngine(self.mock_llm_service)
        
        # 模拟三层识别返回操作指导意图
        original_result = {
            "intent": "操作指导",
            "sub_intent": "账号注册",
            "confidence": 0.9,
            "emotion": "neutral",
            "entities": {}
        }
        
        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze', 
                  return_value=original_result):
            
            # 测试多次调用的平均响应时间
            times = []
            test_message = "如何注册雇主账号？"
            
            for _ in range(10):
                start_time = time.time()
                result = await engine.analyze(test_message)
                end_time = time.time()
                
                times.append((end_time - start_time) * 1000)  # 转换为毫秒
                
                # 验证结果正确性
                self.assertTrue(result["knowledge_base_query"])
                self.assertEqual(result["processing_mode"], "knowledge_base")
                self.assertEqual(result["intent"], "操作指导")
            
            avg_time = mean(times)
            print(f"知识库查询平均响应时间: {avg_time:.2f}ms")
            
            # 根据重构文档，期望响应时间大幅提升
            # 这里我们验证平均响应时间应该在合理范围内（< 100ms，因为没有复杂的LLM调用）
            self.assertLess(avg_time, 100, f"响应时间过长: {avg_time:.2f}ms")
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    async def test_performance_requirement_collection(self, mock_config_manager):
        """测试需求采集的性能"""
        # 设置配置为启用
        mock_manager = Mock()
        mock_manager.is_knowledge_base_enabled.return_value = True
        mock_config_manager.return_value = mock_manager
        
        engine = HybridIntentRecognitionEngine(self.mock_llm_service)
        
        # 模拟三层识别返回需求描述意图
        original_result = {
            "intent": "需求描述",
            "confidence": 0.8,
            "emotion": "neutral",
            "entities": {}
        }
        
        with patch('backend.agents.accelerated_intent_decision_engine.AcceleratedIntentDecisionEngine.analyze', 
                  return_value=original_result):
            
            # 测试多次调用的平均响应时间
            times = []
            test_message = "我想做一个电商网站"
            
            for _ in range(10):
                start_time = time.time()
                result = await engine.analyze(test_message)
                end_time = time.time()
                
                times.append((end_time - start_time) * 1000)  # 转换为毫秒
                
                # 验证结果正确性
                self.assertFalse(result["knowledge_base_query"])
                self.assertEqual(result["processing_mode"], "requirement_collection")
                self.assertEqual(result["intent"], "需求描述")
            
            avg_time = mean(times)
            print(f"需求采集平均响应时间: {avg_time:.2f}ms")
            
            # 验证响应时间在合理范围内
            self.assertLess(avg_time, 50, f"响应时间过长: {avg_time:.2f}ms")
    
    @patch('backend.agents.hybrid_intent_recognition_engine.get_knowledge_base_config_manager')
    def test_heuristic_check_performance(self, mock_config_manager):
        """测试启发式检查的性能"""
        mock_manager = Mock()
        mock_config_manager.return_value = mock_manager
        
        engine = HybridIntentRecognitionEngine(self.mock_llm_service)
        
        # 测试启发式检查的性能
        times = []
        test_cases = [
            ("信息查询", "平台功能"),
            ("用户询问", "如何操作"),
            ("未知意图", "这是什么？"),
            ("需求描述", "我想开发一个网站"),
            ("项目规划", "这是一个很长的消息，不包含疑问句")
        ]
        
        for intent, message in test_cases:
            for _ in range(100):  # 大量测试以获得准确的性能数据
                start_time = time.time()
                result = engine._heuristic_knowledge_base_check(intent, message)
                end_time = time.time()
                
                times.append((end_time - start_time) * 1000000)  # 转换为微秒
        
        avg_time = mean(times)
        print(f"启发式检查平均响应时间: {avg_time:.2f}μs")
        
        # 启发式检查应该非常快（< 1ms）
        self.assertLess(avg_time, 1000, f"启发式检查响应时间过长: {avg_time:.2f}μs")
    
    def test_performance_summary(self):
        """性能测试总结"""
        print("\n=== 性能测试总结 ===")
        print("雇主专用简化版HybridIntentRecognitionEngine性能特点：")
        print("1. 删除了重复的查询关键词匹配，减少了处理步骤")
        print("2. 删除了角色检测功能，简化了判断逻辑")
        print("3. 直接使用三层识别结果，避免了额外的LLM调用")
        print("4. 基于意图映射的快速判断，提高了响应速度")
        print("5. 启发式检查作为补充，保持了准确性")


async def run_async_tests():
    """运行异步测试"""
    test_instance = TestHybridIntentPerformance()
    test_instance.setUp()
    
    print("开始性能测试...")
    
    # 运行异步测试
    await test_instance.test_performance_knowledge_base_query()
    await test_instance.test_performance_requirement_collection()
    
    # 运行同步测试
    test_instance.test_heuristic_check_performance()
    test_instance.test_performance_summary()
    
    print("性能测试完成！")


if __name__ == '__main__':
    # 运行异步测试
    asyncio.run(run_async_tests())
