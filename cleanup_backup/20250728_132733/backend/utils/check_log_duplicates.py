#!/usr/bin/env python3
"""
日志重复输出检查工具

检查日志系统是否存在重复输出问题，包括：
1. 同一条日志是否在多个文件中重复出现
2. 日志处理器是否重复添加
3. 日志配置是否存在问题
"""

import sys
import os
import json
import time
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
import re

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)


def parse_json_log_line(line: str) -> Dict:
    """解析JSON格式的日志行"""
    try:
        return json.loads(line.strip())
    except json.JSONDecodeError:
        return None


def parse_text_log_line(line: str) -> Dict:
    """解析文本格式的日志行"""
    # 简单的文本日志解析
    parts = line.strip().split(' ', 4)
    if len(parts) >= 5:
        return {
            "timestamp": f"{parts[0]} {parts[1]}",
            "logger": parts[2],
            "level": parts[3],
            "message": parts[4]
        }
    return None


def analyze_log_file(file_path: Path) -> Dict:
    """分析单个日志文件"""
    print(f"分析日志文件: {file_path}")

    if not file_path.exists():
        print(f"  文件不存在: {file_path}")
        return {"error": "文件不存在", "entries": []}

    entries = []
    line_count = 0
    json_count = 0
    text_count = 0
    error_count = 0

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line_count += 1

                if not line.strip():
                    continue

                # 尝试解析为JSON
                log_entry = parse_json_log_line(line)
                if log_entry:
                    json_count += 1
                    entries.append({
                        "line_num": line_num,
                        "format": "json",
                        "data": log_entry
                    })
                else:
                    # 尝试解析为文本
                    log_entry = parse_text_log_line(line)
                    if log_entry:
                        text_count += 1
                        entries.append({
                            "line_num": line_num,
                            "format": "text",
                            "data": log_entry
                        })
                    else:
                        error_count += 1
                        if error_count <= 5:  # 只显示前5个解析错误
                            print(f"  解析错误 (行{line_num}): {line[:100]}...")

    except Exception as e:
        print(f"  读取文件失败: {e}")
        return {"error": str(e), "entries": []}

    print(f"  总行数: {line_count}")
    print(f"  JSON格式: {json_count}")
    print(f"  文本格式: {text_count}")
    print(f"  解析错误: {error_count}")
    print(f"  有效条目: {len(entries)}")

    return {
        "file_path": str(file_path),
        "total_lines": line_count,
        "json_count": json_count,
        "text_count": text_count,
        "error_count": error_count,
        "entries": entries
    }


def find_duplicate_messages(log_files_data: List[Dict]) -> Dict:
    """查找重复的日志消息"""
    print("\n检查重复日志消息...")

    # 按消息内容分组
    message_groups = defaultdict(list)

    for file_data in log_files_data:
        if "error" in file_data:
            continue

        file_path = file_data["file_path"]
        for entry in file_data["entries"]:
            log_data = entry["data"]
            message = log_data.get("message", "")
            timestamp = log_data.get("timestamp", "")
            logger = log_data.get("logger", "")

            # 创建消息的唯一标识
            message_key = f"{message}|{logger}"

            message_groups[message_key].append({
                "file": file_path,
                "line_num": entry["line_num"],
                "timestamp": timestamp,
                "logger": logger,
                "message": message
            })

    # 找出重复的消息
    duplicates = {}
    for message_key, occurrences in message_groups.items():
        if len(occurrences) > 1:
            # 检查是否在不同文件中重复
            files = set(occ["file"] for occ in occurrences)
            if len(files) > 1:
                duplicates[message_key] = {
                    "message": occurrences[0]["message"],
                    "logger": occurrences[0]["logger"],
                    "occurrences": occurrences,
                    "file_count": len(files),
                    "total_count": len(occurrences)
                }

    print(f"发现 {len(duplicates)} 个跨文件重复的消息")

    return duplicates


def analyze_log_patterns(log_files_data: List[Dict]) -> Dict:
    """分析日志模式"""
    print("\n分析日志模式...")

    logger_stats = Counter()
    level_stats = Counter()
    message_patterns = Counter()

    for file_data in log_files_data:
        if "error" in file_data:
            continue

        for entry in file_data["entries"]:
            log_data = entry["data"]

            logger = log_data.get("logger", "unknown")
            level = log_data.get("level", "unknown")
            message = log_data.get("message", "")

            logger_stats[logger] += 1
            level_stats[level] += 1

            # 提取消息模式（去除变量部分）
            pattern = re.sub(r'\d+', 'N', message)  # 数字替换为N
            pattern = re.sub(r'[a-f0-9-]{8,}', 'ID', pattern)  # UUID等替换为ID
            message_patterns[pattern] += 1

    return {
        "logger_stats": dict(logger_stats.most_common(20)),
        "level_stats": dict(level_stats),
        "message_patterns": dict(message_patterns.most_common(20))
    }


def check_log_configuration():
    """检查日志配置"""
    print("\n检查日志配置...")

    try:
        # 避免循环导入问题，直接检查配置文件
        import logging

        # 获取根日志记录器
        root_logger = logging.getLogger()

        print(f"根日志记录器级别: {logging.getLevelName(root_logger.level)}")
        print(f"根日志记录器处理器数量: {len(root_logger.handlers)}")

        handler_info = []
        for i, handler in enumerate(root_logger.handlers):
            handler_type = type(handler).__name__
            handler_level = logging.getLevelName(handler.level)
            formatter_type = type(handler.formatter).__name__ if handler.formatter else "None"
            filter_count = len(handler.filters)

            handler_info.append({
                "index": i,
                "type": handler_type,
                "level": handler_level,
                "formatter": formatter_type,
                "filter_count": filter_count
            })

            print(f"  处理器 {i}: {handler_type} (级别: {handler_level}, 格式化器: {formatter_type}, 过滤器: {filter_count})")

        # 检查是否有重复的处理器类型
        handler_types = [info["type"] for info in handler_info]
        type_counts = Counter(handler_types)

        duplicated_types = {t: count for t, count in type_counts.items() if count > 1}
        if duplicated_types:
            print(f"⚠️  发现重复的处理器类型: {duplicated_types}")
        else:
            print("✓ 没有发现重复的处理器类型")

        return {
            "root_level": logging.getLevelName(root_logger.level),
            "handler_count": len(root_logger.handlers),
            "handlers": handler_info,
            "duplicated_types": duplicated_types
        }

    except Exception as e:
        print(f"检查日志配置失败: {e}")
        return {"error": str(e)}


def main():
    """主函数"""
    print("="*80)
    print("日志重复输出检查工具")
    print("="*80)
    print(f"项目根目录: {project_root}")

    # 检查日志目录
    logs_dir = Path(project_root) / "logs"
    if not logs_dir.exists():
        print(f"日志目录不存在: {logs_dir}")
        return

    print(f"日志目录: {logs_dir}")

    # 要检查的日志文件
    log_files = [
        logs_dir / "app.log",
        logs_dir / "error.log",
        logs_dir / "session.log"
    ]

    # 分析每个日志文件
    log_files_data = []
    for log_file in log_files:
        file_data = analyze_log_file(log_file)
        log_files_data.append(file_data)

    # 查找重复消息
    duplicates = find_duplicate_messages(log_files_data)

    # 分析日志模式
    patterns = analyze_log_patterns(log_files_data)

    # 检查日志配置
    config_info = check_log_configuration()

    # 生成报告
    print("\n" + "="*80)
    print("日志重复检查报告")
    print("="*80)

    # 重复消息报告
    if duplicates:
        print(f"\n🔍 发现 {len(duplicates)} 个跨文件重复的日志消息:")
        for i, (message_key, dup_info) in enumerate(list(duplicates.items())[:10], 1):
            print(f"\n{i}. 重复消息 (出现在{dup_info['file_count']}个文件中，共{dup_info['total_count']}次):")
            print(f"   Logger: {dup_info['logger']}")
            print(f"   Message: {dup_info['message'][:100]}...")

            files = set(occ["file"] for occ in dup_info["occurrences"])
            print(f"   文件: {', '.join(Path(f).name for f in files)}")

        if len(duplicates) > 10:
            print(f"   ... 还有 {len(duplicates) - 10} 个重复消息")
    else:
        print("\n✓ 没有发现跨文件重复的日志消息")

    # 日志配置报告
    print(f"\n📊 日志配置状态:")
    if "error" not in config_info:
        print(f"   根日志级别: {config_info['root_level']}")
        print(f"   处理器数量: {config_info['handler_count']}")

        if config_info["duplicated_types"]:
            print(f"   ⚠️  重复处理器类型: {config_info['duplicated_types']}")
        else:
            print(f"   ✓ 处理器类型正常")
    else:
        print(f"   ❌ 配置检查失败: {config_info['error']}")

    # 日志统计报告
    print(f"\n📈 日志统计:")
    print(f"   最活跃的Logger: {list(patterns['logger_stats'].items())[:5]}")
    print(f"   日志级别分布: {patterns['level_stats']}")

    # 总结和建议
    print(f"\n💡 建议:")

    if duplicates:
        print("   ⚠️  发现重复日志，可能的原因:")
        print("      - 多个处理器处理同一条日志")
        print("      - 日志记录器配置不当")
        print("      - 缺少适当的过滤器")

    if config_info.get("duplicated_types"):
        print("   ⚠️  发现重复的处理器类型，建议检查日志配置")

    if not duplicates and not config_info.get("duplicated_types"):
        print("   ✅ 日志系统配置正常，没有发现明显的重复问题")

    print(f"\n检查完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()