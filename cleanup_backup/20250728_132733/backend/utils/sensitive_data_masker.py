"""敏感数据检测和脱敏工具

此模块提供了敏感数据检测和脱敏的功能，包括：
1. 敏感数据模式管理
2. 敏感数据检测
3. 敏感数据脱敏
4. 脱敏规则测试
"""

import re
from typing import Dict, List, Tuple

class SensitiveDataMasker:
    """敏感数据检测和脱敏工具类"""
    
    def __init__(self, patterns: List[Tuple[str, str]] = None):
        """初始化敏感数据检测和脱敏工具
        
        Args:
            patterns: 敏感数据模式列表，每项为 (pattern, replacement) 元组
        """
        self.patterns = patterns or []
        self._compiled_patterns = []
        self._compile_patterns()
        
    def _compile_patterns(self):
        """编译正则表达式模式"""
        self._compiled_patterns = [
            (re.compile(pattern), replacement)
            for pattern, replacement in self.patterns
        ]
        
    def add_pattern(self, pattern: str, replacement: str):
        """添加新的敏感数据模式
        
        Args:
            pattern: 正则表达式模式
            replacement: 替换模板
        """
        self.patterns.append((pattern, replacement))
        self._compile_patterns()
        
    def remove_pattern(self, pattern: str):
        """移除敏感数据模式
        
        Args:
            pattern: 要移除的正则表达式模式
        """
        self.patterns = [(p, r) for p, r in self.patterns if p != pattern]
        self._compile_patterns()
        
    def mask_text(self, text: str) -> str:
        """对文本进行脱敏
        
        Args:
            text: 需要脱敏的文本
            
        Returns:
            脱敏后的文本
        """
        if not text:
            return text
            
        masked_text = text
        for pattern, replacement in self._compiled_patterns:
            masked_text = pattern.sub(replacement, masked_text)
        return masked_text
        
    def test_pattern(self, pattern: str, replacement: str, test_cases: List[str]) -> Dict[str, bool]:
        """测试敏感数据模式
        
        Args:
            pattern: 正则表达式模式
            replacement: 替换模板
            test_cases: 测试用例列表
            
        Returns:
            测试结果字典，键为测试用例，值为是否匹配
        """
        try:
            compiled_pattern = re.compile(pattern)
            results = {}
            for test_case in test_cases:
                masked = compiled_pattern.sub(replacement, test_case)
                results[test_case] = masked != test_case
            return results
        except re.error as e:
            raise ValueError(f"无效的正则表达式模式: {str(e)}")
            
    def scan_text(self, text: str) -> List[Tuple[str, str, str]]:
        """扫描文本中的敏感数据
        
        Args:
            text: 要扫描的文本
            
        Returns:
            匹配结果列表，每项为 (匹配文本, 模式名称, 建议替换) 元组
        """
        if not text:
            return []
            
        results = []
        for pattern, replacement in self._compiled_patterns:
            for match in pattern.finditer(text):
                masked = pattern.sub(replacement, match.group())
                results.append((
                    match.group(),
                    pattern.pattern,
                    masked
                ))
        return results
        
    def mask_json(self, obj: Dict) -> Dict:
        """对JSON对象进行脱敏
        
        Args:
            obj: JSON对象
            
        Returns:
            脱敏后的JSON对象
        """
        if isinstance(obj, dict):
            return {k: self.mask_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.mask_json(item) for item in obj]
        elif isinstance(obj, str):
            return self.mask_text(obj)
        else:
            return obj
            
    def to_dict(self) -> Dict:
        """将配置导出为字典
        
        Returns:
            包含所有模式的字典
        """
        return {
            "patterns": self.patterns
        }
        
    @classmethod
    def from_dict(cls, config: Dict) -> "SensitiveDataMasker":
        """从字典创建实例
        
        Args:
            config: 包含模式的字典
            
        Returns:
            SensitiveDataMasker实例
        """
        return cls(patterns=config.get("patterns", []))
