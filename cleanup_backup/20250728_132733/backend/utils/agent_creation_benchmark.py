#!/usr/bin/env python3
"""
ConversationFlowAgent创建性能基准测试

用于测量和分析当前系统中Agent创建的性能瓶颈，
为组件池化优化提供基准数据。
"""

import asyncio
import sys
import os
import time
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from backend.utils.performance_analyzer import performance_analyzer
from backend.agents.factory import agent_factory


async def benchmark_agent_creation(num_iterations: int = 5) -> Dict[str, Any]:
    """
    基准测试Agent创建性能

    Args:
        num_iterations: 测试迭代次数

    Returns:
        Dict[str, Any]: 基准测试结果
    """
    print(f"开始ConversationFlowAgent创建性能基准测试 (迭代次数: {num_iterations})")
    print("="*80)

    results = []

    for i in range(num_iterations):
        session_id = f"benchmark_session_{i}_{int(time.time())}"
        print(f"\n第 {i+1}/{num_iterations} 次测试 - Session ID: {session_id}")

        # 测量整个Agent创建过程
        with performance_analyzer.measure_component("AgentFactory", "get_conversation_flow_agent",
                                                   {"session_id": session_id, "iteration": i}):

            # 测量依赖服务获取
            with performance_analyzer.measure_component("DependencyContainer", "get_all_services"):
                dependencies = {
                    "llm_service": agent_factory.container.get_service("llm_service"),
                    "document_generator": agent_factory.container.get_service("document_generator"),
                    "information_extractor_agent": agent_factory.container.get_service("information_extractor_agent"),
                    "knowledge_base_agent": agent_factory.container.get_service("knowledge_base_agent"),
                    "message_manager": agent_factory.container.get_service("message_manager"),
                    "document_manager": agent_factory.container.get_service("document_manager"),
                    "focus_point_manager": agent_factory.container.get_service("focus_point_manager"),
                    "database_manager": agent_factory.container.get_service("database_manager"),
                    "intent_decision_engine": agent_factory.container.get_service("intent_decision_engine"),
                    "state_manager": agent_factory.container.get_service("state_manager"),
                    "session_manager": agent_factory.container.get_service("session_manager"),
                    "config_service": agent_factory.container.get_service("config_service"),
                    "prompt_loader": agent_factory.container.get_service("prompt_loader"),
                    "integrated_reply_system": agent_factory.container.get_service("integrated_reply_system"),
                    "domain_classifier_agent": agent_factory.container.get_service("domain_classifier_agent"),
                    "category_classifier_agent": agent_factory.container.get_service("category_classifier_agent"),
                    "knowledge_base_config_manager": agent_factory.container.get_service("knowledge_base_config_manager"),
                }

            # 测量Agent实例创建
            with performance_analyzer.measure_component("ConversationFlowAgent", "instantiation"):
                from backend.agents.conversation_flow.core_refactored import AutoGenConversationFlowAgent
                agent = AutoGenConversationFlowAgent(session_id=session_id, **dependencies)

            # 测量MessageProcessor创建
            with performance_analyzer.measure_component("MessageProcessor", "creation"):
                message_processor = agent_factory._create_message_processor_for_agent(agent)
                agent.message_processor = message_processor

        print(f"第 {i+1} 次测试完成")

    # 生成分析报告
    print("\n" + "="*80)
    print("性能分析完成，生成报告...")

    performance_analyzer.print_summary()
    report_path = performance_analyzer.save_report()

    return {
        "report_path": report_path,
        "summary": performance_analyzer.get_summary(),
        "iterations": num_iterations
    }


async def analyze_individual_components():
    """分析各个组件的创建成本"""
    print("\n" + "="*80)
    print("分析各个组件的创建成本")
    print("="*80)

    # 测试各个服务的获取时间
    services_to_test = [
        "llm_service",
        "document_generator",
        "information_extractor_agent",
        "knowledge_base_agent",
        "message_manager",
        "document_manager",
        "focus_point_manager",
        "database_manager",
        "intent_decision_engine",
        "state_manager",
        "session_manager",
        "config_service",
        "prompt_loader",
        "integrated_reply_system",
        "domain_classifier_agent",
        "category_classifier_agent",
        "knowledge_base_config_manager"
    ]

    for service_name in services_to_test:
        try:
            with performance_analyzer.measure_component("ServiceContainer", f"get_{service_name}"):
                service = agent_factory.container.get_service(service_name)
                print(f"✓ {service_name}: {type(service).__name__}")
        except Exception as e:
            print(f"✗ {service_name}: 获取失败 - {e}")


if __name__ == "__main__":
    async def main():
        try:
            # 分析各个组件的创建成本
            await analyze_individual_components()

            # 运行基准测试
            results = await benchmark_agent_creation(num_iterations=3)

            print(f"\n基准测试完成！")
            print(f"详细报告已保存到: {results['report_path']}")

        except Exception as e:
            print(f"基准测试失败: {e}")
            import traceback
            traceback.print_exc()

    asyncio.run(main())