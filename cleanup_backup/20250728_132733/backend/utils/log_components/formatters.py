#!/usr/bin/env python3
"""
日志格式化器模块

包含各种专用的日志格式化器，用于格式化不同类型的日志输出
"""

import logging
import json
import re
from datetime import datetime
from typing import Dict, Any, List


class MaskingFormatter(logging.Formatter):
    """数据脱敏格式化器"""
    
    def __init__(self, fmt: str = None, datefmt: str = None, style: str = '%'):
        super().__init__(fmt, datefmt, style)
        
        # 敏感数据模式
        self.sensitive_patterns = [
            # API密钥
            (re.compile(r'api[_-]?key["\'\s]*[:=]["\'\s]*([a-zA-Z0-9_-]{20,})', re.IGNORECASE), 'api_key=***'),
            # 密码
            (re.compile(r'password["\'\s]*[:=]["\'\s]*([^\s"\']{6,})', re.IGNORECASE), 'password=***'),
            # 邮箱
            (re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'), '***@***.***'),
            # 手机号
            (re.compile(r'\b1[3-9]\d{9}\b'), '1****5678'),
            # 身份证号
            (re.compile(r'\b\d{15}|\d{17}[\dXx]\b'), '***************'),
            # Token
            (re.compile(r'token["\'\s]*[:=]["\'\s]*([a-zA-Z0-9._-]{20,})', re.IGNORECASE), 'token=***'),
            # 银行卡号
            (re.compile(r'\b\d{16,19}\b'), '****-****-****-1234'),
        ]
    
    def _mask_sensitive_data(self, message: str) -> str:
        """脱敏敏感数据"""
        masked_message = message
        
        for pattern, replacement in self.sensitive_patterns:
            masked_message = pattern.sub(replacement, masked_message)
        
        return masked_message
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录并脱敏"""
        # 先执行标准格式化
        formatted = super().format(record)
        
        # 然后脱敏
        return self._mask_sensitive_data(formatted)


class ColoredFormatter(MaskingFormatter):
    """彩色终端格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def __init__(self, fmt: str = None, datefmt: str = None, use_colors: bool = True):
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors
        
        # 默认彩色格式
        if fmt is None:
            self.colored_fmt = (
                '{color}%(levelname)-8s{reset} '
                '\033[90m%(asctime)s\033[0m '
                '\033[94m%(name)s\033[0m:'
                '\033[96m%(lineno)d\033[0m '
                '%(message)s'
            )
        else:
            self.colored_fmt = fmt
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化彩色日志"""
        if not self.use_colors:
            return super().format(record)
        
        # 获取颜色
        color = self.COLORS.get(record.levelname, '')
        reset = self.COLORS['RESET']
        
        # 临时修改格式字符串
        original_fmt = self._style._fmt
        self._style._fmt = self.colored_fmt.format(color=color, reset=reset)
        
        try:
            formatted = super().format(record)
        finally:
            # 恢复原始格式
            self._style._fmt = original_fmt
        
        return formatted


class EnhancedJsonFormatter(logging.Formatter):
    """增强的JSON格式化器"""
    
    def __init__(self, fmt: str = None, datefmt: str = None, 
                 extra_fields: Dict[str, Any] = None,
                 exclude_fields: List[str] = None):
        super().__init__(fmt, datefmt)
        self.extra_fields = extra_fields or {}
        self.exclude_fields = set(exclude_fields or [])
        
        # 标准字段映射
        self.field_mapping = {
            'name': 'logger_name',
            'levelname': 'level',
            'levelno': 'level_number',
            'pathname': 'file_path',
            'filename': 'file_name',
            'module': 'module_name',
            'funcName': 'function_name',
            'lineno': 'line_number',
            'process': 'process_id',
            'thread': 'thread_id',
            'threadName': 'thread_name',
        }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化为JSON"""
        # 基础日志数据
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'message': record.getMessage(),
            'level': record.levelname,
            'logger_name': record.name,
            'module_name': record.module,
            'file_name': record.filename,
            'line_number': record.lineno,
            'function_name': record.funcName,
        }
        
        # 添加额外字段
        log_data.update(self.extra_fields)
        
        # 添加记录中的额外属性
        for key, value in record.__dict__.items():
            if (key not in self.exclude_fields and 
                key not in log_data and 
                not key.startswith('_') and
                key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                           'pathname', 'filename', 'module', 'exc_info', 
                           'exc_text', 'stack_info', 'lineno', 'funcName',
                           'created', 'msecs', 'relativeCreated', 'thread',
                           'threadName', 'processName', 'process', 'message']):
                try:
                    # 确保值可以JSON序列化
                    json.dumps(value)
                    log_data[key] = value
                except (TypeError, ValueError):
                    log_data[key] = str(value)
        
        # 处理异常信息
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': self.formatException(record.exc_info) if record.exc_info else None
            }
        
        # 处理栈信息
        if record.stack_info:
            log_data['stack_info'] = record.stack_info
        
        # 移除排除的字段
        for field in self.exclude_fields:
            log_data.pop(field, None)
        
        try:
            return json.dumps(log_data, ensure_ascii=False, default=str)
        except Exception as e:
            # 如果JSON序列化失败，返回基础信息
            fallback_data = {
                'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                'level': record.levelname,
                'logger_name': record.name,
                'message': record.getMessage(),
                'serialization_error': str(e)
            }
            return json.dumps(fallback_data, ensure_ascii=False)


class ErrorJsonFormatter(EnhancedJsonFormatter):
    """错误专用JSON格式化器"""
    
    def __init__(self):
        super().__init__(
            extra_fields={
                'log_type': 'error',
                'environment': 'production',  # 可以从配置获取
            },
            exclude_fields=['process', 'thread', 'threadName']
        )
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化错误日志"""
        formatted = super().format(record)
        
        try:
            log_data = json.loads(formatted)
            
            # 添加错误特定信息
            log_data.update({
                'severity': self._get_severity(record.levelno),
                'error_category': self._categorize_error(record.getMessage()),
                'needs_attention': record.levelno >= logging.ERROR,
            })
            
            return json.dumps(log_data, ensure_ascii=False, default=str)
        except Exception:
            return formatted
    
    def _get_severity(self, level: int) -> str:
        """获取错误严重程度"""
        if level >= logging.CRITICAL:
            return 'critical'
        elif level >= logging.ERROR:
            return 'high'
        elif level >= logging.WARNING:
            return 'medium'
        else:
            return 'low'
    
    def _categorize_error(self, message: str) -> str:
        """错误分类"""
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in ['database', 'db', 'sql']):
            return 'database'
        elif any(keyword in message_lower for keyword in ['network', 'connection', 'timeout']):
            return 'network'
        elif any(keyword in message_lower for keyword in ['auth', 'permission', 'unauthorized']):
            return 'security'
        elif any(keyword in message_lower for keyword in ['config', 'configuration', 'setting']):
            return 'configuration'
        elif any(keyword in message_lower for keyword in ['llm', 'model', 'ai']):
            return 'ai_service'
        else:
            return 'general'


class SessionJsonFormatter(EnhancedJsonFormatter):
    """会话专用JSON格式化器"""
    
    def __init__(self):
        super().__init__(
            extra_fields={
                'log_type': 'session',
                'component': 'conversation_system',
            },
            exclude_fields=['process', 'thread']
        )
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化会话日志"""
        formatted = super().format(record)
        
        try:
            log_data = json.loads(formatted)
            
            # 提取会话相关信息
            message = record.getMessage()
            session_info = self._extract_session_info(message)
            
            if session_info:
                log_data.update(session_info)
            
            return json.dumps(log_data, ensure_ascii=False, default=str)
        except Exception:
            return formatted
    
    def _extract_session_info(self, message: str) -> Dict[str, Any]:
        """从消息中提取会话信息"""
        session_info = {}
        
        # 提取会话ID
        session_id_match = re.search(r'session[_\s]*id[:\s]*([a-zA-Z0-9_-]+)', message, re.IGNORECASE)
        if session_id_match:
            session_info['session_id'] = session_id_match.group(1)
        
        # 提取用户ID
        user_id_match = re.search(r'user[_\s]*id[:\s]*([a-zA-Z0-9_-]+)', message, re.IGNORECASE)
        if user_id_match:
            session_info['user_id'] = user_id_match.group(1)
        
        # 提取消息类型
        if any(keyword in message.lower() for keyword in ['request', '请求']):
            session_info['message_type'] = 'request'
        elif any(keyword in message.lower() for keyword in ['response', '响应', '回复']):
            session_info['message_type'] = 'response'
        elif any(keyword in message.lower() for keyword in ['error', '错误']):
            session_info['message_type'] = 'error'
        
        return session_info


class CompactFormatter(logging.Formatter):
    """紧凑格式化器（用于调试）"""
    
    def __init__(self):
        super().__init__(
            fmt='%(levelname).1s %(name)s:%(lineno)d %(message)s',
            datefmt='%H:%M:%S'
        )


class DetailedFormatter(logging.Formatter):
    """详细格式化器（用于生产环境）"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s [%(levelname)s] %(name)s.%(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


# 导出所有格式化器
__all__ = [
    'MaskingFormatter',
    'ColoredFormatter',
    'EnhancedJsonFormatter',
    'ErrorJsonFormatter',
    'SessionJsonFormatter',
    'CompactFormatter',
    'DetailedFormatter',
]