#!/usr/bin/env python3
"""
日志过滤器模块

包含各种专用的日志过滤器，用于分类和过滤日志消息
"""

import logging
import re
from typing import Dict, Set
from datetime import datetime, timedelta
from collections import defaultdict, deque


class SessionLogFilter(logging.Filter):
    """会话日志过滤器"""
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        self.session_keywords = {
            'session', 'conversation', 'user', 'message', 'dialog',
            'chat', 'interaction', 'reply', 'response', '会话', '对话',
            '用户', '消息', '回复', '响应', '交互'
        }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """过滤会话相关日志"""
        message = record.getMessage().lower()
        return any(keyword in message for keyword in self.session_keywords)


class TokenUsageFilter(logging.Filter):
    """Token使用量过滤器"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """过滤Token使用相关日志"""
        return 'token' in record.getMessage().lower()


class IntelligentAppLogFilter(logging.Filter):
    """智能应用日志过滤器"""
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        # 排除的模式
        self.excluded_patterns = [
            r'session.*(?:info|debug)',
            r'token.*usage',
            r'database.*query.*debug',
            r'http.*request.*debug',
            r'cache.*hit.*debug',
        ]
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) 
                                for pattern in self.excluded_patterns]
        
        # 包含的关键词
        self.include_keywords = {
            'error', 'warning', 'critical', 'exception', 'failed',
            'success', 'completed', 'started', 'initialized',
            '错误', '警告', '失败', '成功', '完成', '开始', '初始化'
        }
        
        # 重要的业务关键词
        self.business_keywords = {
            'agent', 'llm', 'config', 'document', 'generation',
            'conversation_flow', 'intent', 'decision', 'processing',
            'Agent', 'LLM', '配置', '文档', '生成', '对话流程', '意图', '决策', '处理'
        }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """智能过滤应用日志"""
        message = record.getMessage()
        message_lower = message.lower()
        
        # 排除特定模式
        for pattern in self.compiled_patterns:
            if pattern.search(message):
                return False
        
        # 包含重要关键词
        if any(keyword in message_lower for keyword in self.include_keywords):
            return True
            
        # 包含业务关键词
        if any(keyword in message for keyword in self.business_keywords):
            return True
            
        # 错误级别及以上总是包含
        if record.levelno >= logging.ERROR:
            return True
            
        # 其他情况根据日志级别决定
        return record.levelno >= logging.INFO


class EnhancedDeduplicationLogFilter(logging.Filter):
    """增强的去重日志过滤器"""
    
    def __init__(self, name: str = "", window_size: int = 100, time_window: int = 60):
        super().__init__(name)
        self.window_size = window_size
        self.time_window = timedelta(seconds=time_window)
        
        # 使用deque限制内存使用
        self.recent_messages: deque = deque(maxlen=window_size)
        self.message_counts: Dict[str, int] = defaultdict(int)
        self.last_seen: Dict[str, datetime] = {}
        
        # 重要消息的关键词（不去重）
        self.important_keywords = {
            'error', 'critical', 'exception', 'failed', 'timeout',
            'connection', 'database', 'security', 'authentication',
            '错误', '严重', '异常', '失败', '超时', '连接', '数据库', '安全', '认证'
        }
    
    def _clean_old_entries(self) -> None:
        """清理过期的条目"""
        current_time = datetime.now()
        expired_keys = [
            key for key, timestamp in self.last_seen.items()
            if current_time - timestamp > self.time_window
        ]
        
        for key in expired_keys:
            del self.last_seen[key]
            if key in self.message_counts:
                del self.message_counts[key]
    
    def _get_message_signature(self, record: logging.LogRecord) -> str:
        """生成消息签名"""
        # 基于日志级别、模块名和消息内容的前50个字符创建签名
        message = record.getMessage()
        signature_content = message[:50] if len(message) > 50 else message
        
        # 移除时间戳和数字，保留核心内容
        signature_content = re.sub(r'\d{4}-\d{2}-\d{2}[\s\d:.-]*', '', signature_content)
        signature_content = re.sub(r'\b\d+\b', 'N', signature_content)
        
        return f"{record.levelname}:{record.module}:{signature_content}"
    
    def filter(self, record: logging.LogRecord) -> bool:
        """去重过滤"""
        current_time = datetime.now()
        message = record.getMessage()
        message_lower = message.lower()
        
        # 重要消息不去重
        if any(keyword in message_lower for keyword in self.important_keywords):
            return True
            
        # 错误级别及以上不去重
        if record.levelno >= logging.ERROR:
            return True
        
        # 清理过期条目
        self._clean_old_entries()
        
        # 生成消息签名
        signature = self._get_message_signature(record)
        
        # 检查是否是重复消息
        if signature in self.last_seen:
            time_diff = current_time - self.last_seen[signature]
            if time_diff < self.time_window:
                self.message_counts[signature] += 1
                # 频繁重复的消息进行限流
                if self.message_counts[signature] > 5:
                    return False
        
        # 更新记录
        self.last_seen[signature] = current_time
        self.message_counts[signature] = self.message_counts.get(signature, 0) + 1
        self.recent_messages.append(signature)
        
        return True


class PerformanceFilter(logging.Filter):
    """性能相关日志过滤器"""
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        self.performance_keywords = {
            'duration', 'elapsed', 'time', 'performance', 'latency',
            'response_time', 'execution_time', 'processing_time',
            '耗时', '延迟', '性能', '响应时间', '执行时间', '处理时间'
        }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """过滤性能相关日志"""
        message = record.getMessage().lower()
        return any(keyword in message for keyword in self.performance_keywords)


class SecurityFilter(logging.Filter):
    """安全相关日志过滤器"""
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        self.security_keywords = {
            'auth', 'login', 'password', 'token', 'security', 'permission',
            'unauthorized', 'forbidden', 'access', 'credential',
            '认证', '登录', '密码', '令牌', '安全', '权限', '未授权', '禁止', '访问', '凭证'
        }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """过滤安全相关日志"""
        message = record.getMessage().lower()
        return any(keyword in message for keyword in self.security_keywords)


class LevelRangeFilter(logging.Filter):
    """日志级别范围过滤器"""
    
    def __init__(self, min_level: int = logging.DEBUG, max_level: int = logging.CRITICAL):
        super().__init__()
        self.min_level = min_level
        self.max_level = max_level
    
    def filter(self, record: logging.LogRecord) -> bool:
        """根据级别范围过滤"""
        return self.min_level <= record.levelno <= self.max_level


class ModuleFilter(logging.Filter):
    """模块过滤器"""
    
    def __init__(self, included_modules: Set[str] = None, excluded_modules: Set[str] = None):
        super().__init__()
        self.included_modules = included_modules or set()
        self.excluded_modules = excluded_modules or set()
    
    def filter(self, record: logging.LogRecord) -> bool:
        """根据模块名过滤"""
        module_name = record.module
        
        # 如果有排除列表，检查是否在排除列表中
        if self.excluded_modules and module_name in self.excluded_modules:
            return False
            
        # 如果有包含列表，只允许包含列表中的模块
        if self.included_modules:
            return module_name in self.included_modules
            
        return True


# 导出所有过滤器类
__all__ = [
    'SessionLogFilter',
    'TokenUsageFilter', 
    'IntelligentAppLogFilter',
    'EnhancedDeduplicationLogFilter',
    'PerformanceFilter',
    'SecurityFilter',
    'LevelRangeFilter',
    'ModuleFilter',
]