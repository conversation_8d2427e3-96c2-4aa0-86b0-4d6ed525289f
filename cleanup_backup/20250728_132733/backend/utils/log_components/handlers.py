#!/usr/bin/env python3
"""
日志处理器模块

包含各种专用的日志处理器，用于将日志输出到不同的目标
"""

import logging
import logging.handlers
import queue
import threading
import atexit
import time
from pathlib import Path
from typing import Dict, List
from datetime import datetime


class AsyncFileHandler(logging.handlers.QueueHandler):
    """异步文件处理器
    
    使用队列和后台线程异步写入文件，避免阻塞主线程
    """
    
    def __init__(self, filename: str, maxBytes: int = 10*1024*1024, 
                 backupCount: int = 5, encoding: str = 'utf-8'):
        # 创建队列
        self.queue = queue.Queue(-1)
        super().__init__(self.queue)
        
        # 创建实际的文件处理器
        self.file_handler = logging.handlers.RotatingFileHandler(
            filename=filename,
            maxBytes=maxBytes,
            backupCount=backupCount,
            encoding=encoding
        )
        
        # 创建并启动监听线程
        self.listener = logging.handlers.QueueListener(
            self.queue, 
            self.file_handler,
            respect_handler_level=True
        )
        self.listener.start()
        
        # 注册清理函数
        atexit.register(self.cleanup)
        
        self._stopped = False
    
    def setFormatter(self, formatter: logging.Formatter) -> None:
        """设置格式化器"""
        self.file_handler.setFormatter(formatter)
    
    def setLevel(self, level) -> None:
        """设置日志级别"""
        super().setLevel(level)
        self.file_handler.setLevel(level)
    
    def cleanup(self) -> None:
        """清理资源"""
        if not self._stopped:
            self.listener.stop()
            self._stopped = True
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


class SmartFileHandler(logging.FileHandler):
    """智能文件处理器
    
    自动创建目录，支持路径变量替换
    """
    
    def __init__(self, filename: str, mode: str = 'a', encoding: str = 'utf-8',
                 delay: bool = False, auto_create_dir: bool = True):
        
        # 处理路径
        filename = self._process_filename(filename)
        
        # 自动创建目录
        if auto_create_dir:
            Path(filename).parent.mkdir(parents=True, exist_ok=True)
        
        super().__init__(filename, mode, encoding, delay)
    
    def _process_filename(self, filename: str) -> str:
        """处理文件名，支持变量替换"""
        # 替换时间变量
        from datetime import datetime
        now = datetime.now()
        
        replacements = {
            '{date}': now.strftime('%Y-%m-%d'),
            '{time}': now.strftime('%H-%M-%S'),
            '{year}': now.strftime('%Y'),
            '{month}': now.strftime('%m'),
            '{day}': now.strftime('%d'),
        }
        
        processed = filename
        for placeholder, value in replacements.items():
            processed = processed.replace(placeholder, value)
        
        return processed


class BufferedHandler(logging.Handler):
    """缓冲处理器
    
    收集日志到缓冲区，达到阈值或定时刷新
    """
    
    def __init__(self, target_handler: logging.Handler, 
                 buffer_size: int = 100, flush_interval: float = 30.0):
        super().__init__()
        
        self.target_handler = target_handler
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        
        self.buffer = []
        self.last_flush = time.time()
        self.lock = threading.RLock()
        
        # 启动定时刷新线程
        self._start_flush_timer()
    
    def emit(self, record: logging.LogRecord) -> None:
        """发送日志记录"""
        with self.lock:
            self.buffer.append(record)
            
            # 检查是否需要刷新
            current_time = time.time()
            if (len(self.buffer) >= self.buffer_size or 
                current_time - self.last_flush >= self.flush_interval):
                self.flush()
    
    def flush(self) -> None:
        """刷新缓冲区"""
        with self.lock:
            if self.buffer:
                for record in self.buffer:
                    self.target_handler.emit(record)
                
                self.buffer.clear()
                self.last_flush = time.time()
                
                # 刷新目标处理器
                if hasattr(self.target_handler, 'flush'):
                    self.target_handler.flush()
    
    def _start_flush_timer(self) -> None:
        """启动定时刷新"""
        def timer_flush():
            while True:
                time.sleep(self.flush_interval)
                if self.buffer:
                    self.flush()
        
        timer_thread = threading.Thread(target=timer_flush, daemon=True)
        timer_thread.start()
    
    def close(self) -> None:
        """关闭处理器"""
        self.flush()
        if hasattr(self.target_handler, 'close'):
            self.target_handler.close()
        super().close()


class MultiFileHandler(logging.Handler):
    """多文件处理器
    
    根据日志级别将日志写入不同的文件
    """
    
    def __init__(self, file_mapping: Dict[int, str], 
                 maxBytes: int = 10*1024*1024, backupCount: int = 5):
        super().__init__()
        
        self.handlers = {}
        
        # 为每个级别创建处理器
        for level, filename in file_mapping.items():
            handler = logging.handlers.RotatingFileHandler(
                filename=filename,
                maxBytes=maxBytes,
                backupCount=backupCount,
                encoding='utf-8'
            )
            handler.setLevel(level)
            self.handlers[level] = handler
    
    def emit(self, record: logging.LogRecord) -> None:
        """发送日志记录"""
        # 找到合适的处理器
        for level, handler in self.handlers.items():
            if record.levelno >= level:
                handler.emit(record)
                break
    
    def setFormatter(self, formatter: logging.Formatter) -> None:
        """设置格式化器"""
        for handler in self.handlers.values():
            handler.setFormatter(formatter)
    
    def flush(self) -> None:
        """刷新所有处理器"""
        for handler in self.handlers.values():
            if hasattr(handler, 'flush'):
                handler.flush()
    
    def close(self) -> None:
        """关闭所有处理器"""
        for handler in self.handlers.values():
            if hasattr(handler, 'close'):
                handler.close()
        super().close()


class EmailHandler(logging.handlers.SMTPHandler):
    """增强的邮件处理器
    
    支持HTML格式和批量发送
    """
    
    def __init__(self, mailhost, fromaddr, toaddrs, subject,
                 credentials=None, secure=None, timeout=5.0,
                 batch_size: int = 10, batch_timeout: float = 300.0):
        super().__init__(mailhost, fromaddr, toaddrs, subject, 
                        credentials, secure, timeout)
        
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.email_buffer = []
        self.last_sent = time.time()
        self.lock = threading.RLock()
    
    def emit(self, record: logging.LogRecord) -> None:
        """发送日志记录"""
        with self.lock:
            self.email_buffer.append(record)
            
            current_time = time.time()
            if (len(self.email_buffer) >= self.batch_size or
                current_time - self.last_sent >= self.batch_timeout):
                self._send_batch()
    
    def _send_batch(self) -> None:
        """批量发送邮件"""
        if not self.email_buffer:
            return
        
        try:
            # 组合多条日志记录
            combined_message = self._format_batch_message(self.email_buffer)
            
            # 创建一个临时记录用于发送
            batch_record = logging.LogRecord(
                name="BatchEmail",
                level=max(r.levelno for r in self.email_buffer),
                pathname="",
                lineno=0,
                msg=combined_message,
                args=(),
                exc_info=None
            )
            
            super().emit(batch_record)
            
            self.email_buffer.clear()
            self.last_sent = time.time()
            
        except Exception as e:
            # 如果发送失败，记录错误但不抛出异常
            print(f"发送邮件失败: {e}")
    
    def _format_batch_message(self, records: List[logging.LogRecord]) -> str:
        """格式化批量消息"""
        messages = []
        for record in records:
            formatted = self.format(record)
            messages.append(formatted)
        
        return "\n\n".join(messages)


class DatabaseHandler(logging.Handler):
    """数据库处理器
    
    将日志写入数据库
    """
    
    def __init__(self, db_connection, table_name: str = "logs"):
        super().__init__()
        self.db_connection = db_connection
        self.table_name = table_name
        self._ensure_table_exists()
    
    def _ensure_table_exists(self) -> None:
        """确保日志表存在"""
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {self.table_name} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            level VARCHAR(10),
            logger_name VARCHAR(100),
            module_name VARCHAR(100),
            function_name VARCHAR(100),
            line_number INTEGER,
            message TEXT,
            exception_info TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        try:
            cursor = self.db_connection.cursor()
            cursor.execute(create_table_sql)
            self.db_connection.commit()
        except Exception as e:
            print(f"创建日志表失败: {e}")
    
    def emit(self, record: logging.LogRecord) -> None:
        """发送日志记录到数据库"""
        try:
            insert_sql = f"""
            INSERT INTO {self.table_name} 
            (timestamp, level, logger_name, module_name, function_name, 
             line_number, message, exception_info)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # 处理异常信息
            exc_info = None
            if record.exc_info:
                exc_info = self.formatException(record.exc_info)
            
            cursor = self.db_connection.cursor()
            cursor.execute(insert_sql, (
                datetime.fromtimestamp(record.created).isoformat(),
                record.levelname,
                record.name,
                record.module,
                record.funcName,
                record.lineno,
                record.getMessage(),
                exc_info
            ))
            self.db_connection.commit()
            
        except Exception as e:
            # 数据库写入失败不应该影响应用程序
            print(f"写入数据库日志失败: {e}")


# 导出所有处理器
__all__ = [
    'AsyncFileHandler',
    'SmartFileHandler', 
    'BufferedHandler',
    'MultiFileHandler',
    'EmailHandler',
    'DatabaseHandler',
]