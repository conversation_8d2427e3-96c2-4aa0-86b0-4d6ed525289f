#!/usr/bin/env python3
"""
最小化性能基准测试 - 避免所有依赖问题

直接分析Agent创建的关键步骤，不依赖任何日志系统。
"""

import time
import sys
import os
import json
from typing import Dict, Any, List

def measure_time(func, *args, **kwargs):
    """测量函数执行时间"""
    start_time = time.time()
    try:
        result = func(*args, **kwargs)
        success = True
        error = None
    except Exception as e:
        result = None
        success = False
        error = str(e)
    end_time = time.time()

    return {
        'result': result,
        'duration': end_time - start_time,
        'success': success,
        'error': error
    }

def analyze_current_system():
    """分析当前系统的性能瓶颈"""
    print("="*80)
    print("ConversationFlowAgent 创建性能瓶颈分析")
    print("="*80)

    measurements = []

    # 添加项目根目录到Python路径
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    print(f"项目根目录: {project_root}")

    # 1. 测量基础导入
    print("\n1. 测量基础模块导入...")

    basic_imports = [
        ("time", None),
        ("json", None),
        ("typing", "Dict"),
        ("dataclasses", "dataclass"),
        ("pathlib", "Path"),
    ]

    for module_name, attr_name in basic_imports:
        def import_module():
            if attr_name:
                module = __import__(module_name, fromlist=[attr_name])
                return getattr(module, attr_name)
            else:
                return __import__(module_name)

        result = measure_time(import_module)
        measurements.append({
            'component': f'BasicImport.{module_name}',
            'operation': 'import',
            'duration': result['duration'],
            'success': result['success'],
            'error': result['error']
        })

        status = "✓" if result['success'] else "✗"
        print(f"  {status} {module_name:<30} {result['duration']:.4f}s")

    # 2. 测量项目核心模块导入（避免日志相关）
    print("\n2. 测量项目核心模块导入...")

    # 先尝试导入不依赖日志的模块
    safe_imports = [
        ("backend.config.settings", "DATABASE_PATH"),
    ]

    for module_name, attr_name in safe_imports:
        def import_module():
            try:
                module = __import__(module_name, fromlist=[attr_name])
                return getattr(module, attr_name)
            except Exception as e:
                print(f"    导入 {module_name} 失败: {e}")
                raise

        result = measure_time(import_module)
        measurements.append({
            'component': f'ProjectImport.{module_name}',
            'operation': 'import',
            'duration': result['duration'],
            'success': result['success'],
            'error': result['error']
        })

        status = "✓" if result['success'] else "✗"
        print(f"  {status} {module_name:<40} {result['duration']:.4f}s")
        if result['error']:
            print(f"    错误: {result['error']}")

    # 3. 分析配置文件加载
    print("\n3. 分析配置文件加载...")

    config_files = [
        "backend/config/business_rules.yaml",
        "backend/config/strategies.yaml",
        "backend/config/message_config.yaml",
        "backend/config/unified_config.yaml",
    ]

    for config_file in config_files:
        config_path = os.path.join(project_root, config_file)

        def load_config():
            if os.path.exists(config_path):
                import yaml
                with open(config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                raise FileNotFoundError(f"配置文件不存在: {config_path}")

        result = measure_time(load_config)
        measurements.append({
            'component': f'ConfigLoad.{os.path.basename(config_file)}',
            'operation': 'load_yaml',
            'duration': result['duration'],
            'success': result['success'],
            'error': result['error']
        })

        status = "✓" if result['success'] else "✗"
        file_size = os.path.getsize(config_path) if os.path.exists(config_path) else 0
        print(f"  {status} {os.path.basename(config_file):<30} {result['duration']:.4f}s ({file_size} bytes)")
        if result['error']:
            print(f"    错误: {result['error']}")

    return measurements


def analyze_database_operations():
    """分析数据库操作性能"""
    print("\n4. 分析数据库操作...")

    measurements = []

    # 测量SQLite连接创建
    def create_sqlite_connection():
        import sqlite3
        import tempfile

        # 创建临时数据库文件
        temp_db = tempfile.mktemp(suffix='.db')
        conn = sqlite3.connect(temp_db)

        # 创建一个简单的表
        conn.execute('''
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY,
                name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 插入一些测试数据
        for i in range(100):
            conn.execute('INSERT INTO test_table (name) VALUES (?)', (f'test_{i}',))

        conn.commit()

        # 执行查询
        cursor = conn.execute('SELECT COUNT(*) FROM test_table')
        result = cursor.fetchone()

        conn.close()
        os.unlink(temp_db)  # 清理临时文件

        return result[0]

    result = measure_time(create_sqlite_connection)
    measurements.append({
        'component': 'Database.SQLite',
        'operation': 'create_and_query',
        'duration': result['duration'],
        'success': result['success'],
        'error': result['error']
    })

    status = "✓" if result['success'] else "✗"
    print(f"  {status} SQLite连接和查询测试        {result['duration']:.4f}s")
    if result['error']:
        print(f"    错误: {result['error']}")

    return measurements


def analyze_yaml_processing():
    """分析YAML处理性能"""
    print("\n5. 分析YAML处理性能...")

    measurements = []

    # 创建测试YAML数据
    test_yaml_data = {
        'business_rules': {
            'keyword_rules': {
                'greeting': {
                    'keywords': ['你好', '您好', 'hello', 'hi'] * 10,
                    'response_template': '您好！我是AI需求采集助手' * 5
                },
                'capability_inquiry': {
                    'keywords': ['能做什么', '功能', '帮助'] * 10,
                    'response_template': '我可以帮助您整理和分析业务需求' * 5
                }
            },
            'action_handlers': {
                'handler_classes': {
                    f'handler_{i}': f'backend.handlers.handler_{i}' for i in range(20)
                }
            }
        },
        'strategies': {
            'intent_recognition': {
                'models': [f'model_{i}' for i in range(50)],
                'thresholds': {f'threshold_{i}': 0.8 for i in range(30)}
            }
        },
        'message_templates': {
            'system': {
                f'template_{i}': f'这是模板消息 {i}' * 10 for i in range(100)
            }
        }
    }

    # 测量YAML序列化
    def serialize_yaml():
        import yaml
        return yaml.dump(test_yaml_data, default_flow_style=False, allow_unicode=True)

    result = measure_time(serialize_yaml)
    measurements.append({
        'component': 'YAML.Serialize',
        'operation': 'dump',
        'duration': result['duration'],
        'success': result['success'],
        'error': result['error']
    })

    status = "✓" if result['success'] else "✗"
    print(f"  {status} YAML序列化测试              {result['duration']:.4f}s")

    # 测量YAML反序列化
    if result['success']:
        yaml_content = result['result']

        def deserialize_yaml():
            import yaml
            return yaml.safe_load(yaml_content)

        result = measure_time(deserialize_yaml)
        measurements.append({
            'component': 'YAML.Deserialize',
            'operation': 'load',
            'duration': result['duration'],
            'success': result['success'],
            'error': result['error']
        })

        status = "✓" if result['success'] else "✗"
        print(f"  {status} YAML反序列化测试            {result['duration']:.4f}s")

    return measurements


def generate_performance_report(all_measurements):
    """生成性能分析报告"""
    print("\n" + "="*80)
    print("性能瓶颈分析报告")
    print("="*80)

    # 按组件分组
    component_stats = {}
    total_duration = 0
    success_count = 0

    for measurement in all_measurements:
        component = measurement['component']
        duration = measurement['duration']
        success = measurement['success']

        if component not in component_stats:
            component_stats[component] = {
                'total_duration': 0,
                'count': 0,
                'successes': 0,
                'max_duration': 0,
                'min_duration': float('inf')
            }

        stats = component_stats[component]
        stats['total_duration'] += duration
        stats['count'] += 1
        stats['max_duration'] = max(stats['max_duration'], duration)
        stats['min_duration'] = min(stats['min_duration'], duration)

        if success:
            stats['successes'] += 1
            success_count += 1

        total_duration += duration

    # 显示总体统计
    print(f"总测量次数: {len(all_measurements)}")
    print(f"总耗时: {total_duration:.4f}s")
    print(f"成功率: {success_count/len(all_measurements)*100:.1f}%")
    print(f"平均耗时: {total_duration/len(all_measurements):.4f}s")

    # 显示最耗时的组件
    print("\n最耗时的组件:")
    print("-" * 80)
    sorted_components = sorted(
        component_stats.items(),
        key=lambda x: x[1]['total_duration'],
        reverse=True
    )

    for component, stats in sorted_components:
        avg_duration = stats['total_duration'] / stats['count']
        success_rate = stats['successes'] / stats['count'] * 100
        print(f"{component:<40} {stats['total_duration']:.4f}s (avg: {avg_duration:.4f}s, {success_rate:.0f}% success)")

    return component_stats


if __name__ == "__main__":
    try:
        # 运行所有分析
        all_measurements = []

        # 基础系统分析
        measurements = analyze_current_system()
        all_measurements.extend(measurements)

        # 数据库操作分析
        db_measurements = analyze_database_operations()
        all_measurements.extend(db_measurements)

        # YAML处理分析
        yaml_measurements = analyze_yaml_processing()
        all_measurements.extend(yaml_measurements)

        # 生成报告
        component_stats = generate_performance_report(all_measurements)

        # 保存结果到文件
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = f"logs/performance/minimal_benchmark_{timestamp}.json"

        os.makedirs(os.path.dirname(report_file), exist_ok=True)

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'measurements': all_measurements,
                'component_stats': component_stats,
                'summary': {
                    'total_measurements': len(all_measurements),
                    'total_duration': sum(m['duration'] for m in all_measurements),
                    'success_rate': sum(1 for m in all_measurements if m['success']) / len(all_measurements) * 100
                }
            }, f, indent=2, ensure_ascii=False)

        print(f"\n基准测试完成！共测量 {len(all_measurements)} 个指标")
        print(f"详细报告已保存到: {report_file}")

        # 输出关键发现
        print("\n" + "="*80)
        print("关键发现和建议")
        print("="*80)

        # 分析配置加载性能
        config_measurements = [m for m in all_measurements if 'ConfigLoad' in m['component']]
        if config_measurements:
            total_config_time = sum(m['duration'] for m in config_measurements)
            print(f"配置文件加载总时间: {total_config_time:.4f}s")
            if total_config_time > 0.1:
                print("⚠️  配置文件加载时间较长，建议实现配置缓存")
            else:
                print("✓ 配置文件加载时间合理")

        # 分析数据库性能
        db_measurements = [m for m in all_measurements if 'Database' in m['component']]
        if db_measurements:
            total_db_time = sum(m['duration'] for m in db_measurements)
            print(f"数据库操作总时间: {total_db_time:.4f}s")
            if total_db_time > 0.5:
                print("⚠️  数据库操作时间较长，建议实现连接池")
            else:
                print("✓ 数据库操作时间合理")

        # 分析YAML处理性能
        yaml_measurements = [m for m in all_measurements if 'YAML' in m['component']]
        if yaml_measurements:
            total_yaml_time = sum(m['duration'] for m in yaml_measurements)
            print(f"YAML处理总时间: {total_yaml_time:.4f}s")
            if total_yaml_time > 0.2:
                print("⚠️  YAML处理时间较长，建议预加载和缓存")
            else:
                print("✓ YAML处理时间合理")

        print("\n推荐的优化策略:")
        print("1. 实现组件池化管理，避免重复创建重量级对象")
        print("2. 配置文件预加载和智能缓存")
        print("3. 数据库连接池管理")
        print("4. 延迟初始化非关键组件")
        print("5. 实现服务单例模式")

    except Exception as e:
        print(f"基准测试失败: {e}")
        import traceback
        traceback.print_exc()