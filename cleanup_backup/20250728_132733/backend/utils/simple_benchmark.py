#!/usr/bin/env python3
"""
简化版性能基准测试 - 避免循环导入问题

直接测量Agent创建的关键步骤，分析性能瓶颈。
"""

import time
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def measure_time(func, *args, **kwargs):
    """测量函数执行时间"""
    start_time = time.time()
    try:
        result = func(*args, **kwargs)
        success = True
        error = None
    except Exception as e:
        result = None
        success = False
        error = str(e)
    end_time = time.time()

    return {
        'result': result,
        'duration': end_time - start_time,
        'success': success,
        'error': error
    }

def analyze_agent_factory_performance():
    """分析AgentFactory的性能"""
    print("="*80)
    print("ConversationFlowAgent 创建性能分析")
    print("="*80)

    measurements = []

    # 1. 测量导入时间
    print("\n1. 测量关键模块导入时间...")

    import_tests = [
        ("backend.agents.factory", "agent_factory"),
        ("backend.config.unified_config_loader", "get_unified_config"),
        ("backend.data.db.database_manager", "DatabaseManager"),
        ("backend.agents.llm_service", "AutoGenLLMServiceAgent"),
        ("backend.handlers.action_executor", "ActionExecutor"),
    ]

    for module_name, attr_name in import_tests:
        def import_module():
            module = __import__(module_name, fromlist=[attr_name])
            return getattr(module, attr_name)

        result = measure_time(import_module)
        measurements.append({
            'component': f'Import.{module_name}',
            'operation': 'import',
            'duration': result['duration'],
            'success': result['success'],
            'error': result['error']
        })

        status = "✓" if result['success'] else "✗"
        print(f"  {status} {module_name:<40} {result['duration']:.4f}s")
        if result['error']:
            print(f"    错误: {result['error']}")

    # 2. 测量AgentFactory初始化
    print("\n2. 测量AgentFactory组件获取时间...")

    try:
        from backend.agents.factory import agent_factory

        services_to_test = [
            "config_service",
            "database_manager",
            "llm_service",
            "message_manager",
            "document_manager",
            "focus_point_manager",
            "knowledge_base_agent",
            "document_generator",
            "information_extractor_agent",
            "intent_decision_engine",
            "state_manager",
            "session_manager",
            "prompt_loader",
            "integrated_reply_system",
            "domain_classifier_agent",
            "category_classifier_agent",
        ]

        for service_name in services_to_test:
            def get_service():
                return agent_factory.container.get_service(service_name)

            result = measure_time(get_service)
            measurements.append({
                'component': f'ServiceContainer.{service_name}',
                'operation': 'get_service',
                'duration': result['duration'],
                'success': result['success'],
                'error': result['error']
            })

            status = "✓" if result['success'] else "✗"
            print(f"  {status} {service_name:<35} {result['duration']:.4f}s")
            if result['error']:
                print(f"    错误: {result['error']}")

    except Exception as e:
        print(f"AgentFactory初始化失败: {e}")
        return measurements

    # 3. 测量完整Agent创建
    print("\n3. 测量完整Agent创建时间...")

    for i in range(3):
        session_id = f"benchmark_session_{i}_{int(time.time())}"

        def create_agent():
            return agent_factory.get_conversation_flow_agent(session_id)

        result = measure_time(create_agent)
        measurements.append({
            'component': 'AgentFactory',
            'operation': 'get_conversation_flow_agent',
            'duration': result['duration'],
            'success': result['success'],
            'error': result['error']
        })

        status = "✓" if result['success'] else "✗"
        print(f"  {status} 第{i+1}次创建 {session_id:<25} {result['duration']:.4f}s")
        if result['error']:
            print(f"    错误: {result['error']}")

    return measurements


def analyze_results(measurements: List[Dict[str, Any]]):
    """分析测量结果"""
    print("\n" + "="*80)
    print("性能分析结果")
    print("="*80)

    # 按组件分组
    component_stats = {}
    total_duration = 0
    success_count = 0

    for measurement in measurements:
        component = measurement['component']
        duration = measurement['duration']
        success = measurement['success']

        if component not in component_stats:
            component_stats[component] = {
                'total_duration': 0,
                'count': 0,
                'successes': 0,
                'max_duration': 0,
                'min_duration': float('inf')
            }

        stats = component_stats[component]
        stats['total_duration'] += duration
        stats['count'] += 1
        stats['max_duration'] = max(stats['max_duration'], duration)
        stats['min_duration'] = min(stats['min_duration'], duration)

        if success:
            stats['successes'] += 1
            success_count += 1

        total_duration += duration

    # 显示总体统计
    print(f"总测量次数: {len(measurements)}")
    print(f"总耗时: {total_duration:.4f}s")
    print(f"成功率: {success_count/len(measurements)*100:.1f}%")
    print(f"平均耗时: {total_duration/len(measurements):.4f}s")

    # 显示最耗时的组件
    print("\n最耗时的组件 (Top 10):")
    print("-" * 80)
    sorted_components = sorted(
        component_stats.items(),
        key=lambda x: x[1]['total_duration'],
        reverse=True
    )[:10]

    for component, stats in sorted_components:
        avg_duration = stats['total_duration'] / stats['count']
        success_rate = stats['successes'] / stats['count'] * 100
        print(f"{component:<50} {stats['total_duration']:.4f}s (avg: {avg_duration:.4f}s, {success_rate:.0f}% success)")

    # 识别性能瓶颈
    print("\n性能瓶颈分析:")
    print("-" * 80)

    # Agent创建相关的测量
    agent_creation_measurements = [m for m in measurements if 'get_conversation_flow_agent' in m['operation']]
    if agent_creation_measurements:
        avg_creation_time = sum(m['duration'] for m in agent_creation_measurements) / len(agent_creation_measurements)
        print(f"Agent创建平均时间: {avg_creation_time:.4f}s")

        if avg_creation_time > 1.0:
            print("⚠️  Agent创建时间过长 (>1s)，建议优化")
        elif avg_creation_time > 0.5:
            print("⚠️  Agent创建时间较长 (>0.5s)，可以优化")
        else:
            print("✓ Agent创建时间合理")

    # 服务获取相关的测量
    service_measurements = [m for m in measurements if 'get_service' in m['operation']]
    if service_measurements:
        total_service_time = sum(m['duration'] for m in service_measurements)
        print(f"服务获取总时间: {total_service_time:.4f}s")

        if total_service_time > 2.0:
            print("⚠️  服务获取时间过长，建议实现服务池化")
        elif total_service_time > 1.0:
            print("⚠️  服务获取时间较长，可以考虑缓存")
        else:
            print("✓ 服务获取时间合理")

    # 导入相关的测量
    import_measurements = [m for m in measurements if 'import' in m['operation']]
    if import_measurements:
        total_import_time = sum(m['duration'] for m in import_measurements)
        print(f"模块导入总时间: {total_import_time:.4f}s")

        if total_import_time > 1.0:
            print("⚠️  模块导入时间过长，建议预加载")
        else:
            print("✓ 模块导入时间合理")

    return component_stats


if __name__ == "__main__":
    try:
        measurements = analyze_agent_factory_performance()
        component_stats = analyze_results(measurements)

        print(f"\n基准测试完成！共测量 {len(measurements)} 个指标")

        # 保存结果到文件
        import json
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = f"logs/performance/simple_benchmark_{timestamp}.json"

        os.makedirs(os.path.dirname(report_file), exist_ok=True)

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'measurements': measurements,
                'component_stats': component_stats
            }, f, indent=2, ensure_ascii=False)

        print(f"详细报告已保存到: {report_file}")

    except Exception as e:
        print(f"基准测试失败: {e}")
        import traceback
        traceback.print_exc()