#!/usr/bin/env python3
"""
性能监控系统

为组件池化系统提供全面的性能监控和指标收集。
核心功能：
1. 实时性能指标收集
2. 组件池化效率监控
3. 内存使用情况跟踪
4. 响应时间分析
5. 监控仪表板和告警
"""

import time
import threading
import psutil
import os
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque, defaultdict
import json
from pathlib import Path


@dataclass
class PerformanceMetric:
    """性能指标数据点"""
    timestamp: float
    metric_name: str
    value: float
    tags: Dict[str, str] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp,
            "metric_name": self.metric_name,
            "value": self.value,
            "tags": self.tags,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat()
        }


@dataclass
class SystemResourceMetrics:
    """系统资源指标"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    process_memory_mb: float
    process_cpu_percent: float
    thread_count: int

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_used_mb": self.memory_used_mb,
            "memory_available_mb": self.memory_available_mb,
            "disk_usage_percent": self.disk_usage_percent,
            "process_memory_mb": self.process_memory_mb,
            "process_cpu_percent": self.process_cpu_percent,
            "thread_count": self.thread_count
        }


class PerformanceMonitor:
    """
    性能监控器

    收集和分析系统性能指标。
    """

    def __init__(self, max_metrics: int = 10000, retention_hours: int = 24):
        # 指标存储
        self.metrics: deque = deque(maxlen=max_metrics)
        self.retention_hours = retention_hours

        # 线程安全
        self.lock = threading.RLock()

        # 系统监控
        self.process = psutil.Process(os.getpid())

        # 指标聚合
        self.metric_aggregates: Dict[str, Dict[str, float]] = defaultdict(lambda: {
            "count": 0,
            "sum": 0.0,
            "min": float('inf'),
            "max": float('-inf'),
            "avg": 0.0
        })

        # 告警配置
        self.alert_thresholds: Dict[str, Dict[str, float]] = {}
        self.alert_callbacks: List[Callable] = []

        # 监控状态
        self.monitoring_active = False
        self.monitoring_thread: Optional[threading.Thread] = None

        print("[INFO] 性能监控器初始化完成")

    def record_metric(self, metric_name: str, value: float, tags: Dict[str, str] = None):
        """
        记录性能指标

        Args:
            metric_name: 指标名称
            value: 指标值
            tags: 标签字典
        """
        if tags is None:
            tags = {}

        timestamp = time.time()
        metric = PerformanceMetric(
            timestamp=timestamp,
            metric_name=metric_name,
            value=value,
            tags=tags
        )

        with self.lock:
            self.metrics.append(metric)
            self._update_aggregates(metric_name, value)
            self._check_alerts(metric_name, value, tags)

    def _update_aggregates(self, metric_name: str, value: float):
        """更新指标聚合"""
        agg = self.metric_aggregates[metric_name]
        agg["count"] += 1
        agg["sum"] += value
        agg["min"] = min(agg["min"], value)
        agg["max"] = max(agg["max"], value)
        agg["avg"] = agg["sum"] / agg["count"]

    def _check_alerts(self, metric_name: str, value: float, tags: Dict[str, str]):
        """检查告警条件"""
        if metric_name in self.alert_thresholds:
            thresholds = self.alert_thresholds[metric_name]

            alert_triggered = False
            alert_type = None

            if "max" in thresholds and value > thresholds["max"]:
                alert_triggered = True
                alert_type = "max_exceeded"
            elif "min" in thresholds and value < thresholds["min"]:
                alert_triggered = True
                alert_type = "min_exceeded"

            if alert_triggered:
                alert_data = {
                    "metric_name": metric_name,
                    "value": value,
                    "threshold": thresholds,
                    "alert_type": alert_type,
                    "tags": tags,
                    "timestamp": time.time()
                }

                for callback in self.alert_callbacks:
                    try:
                        callback(alert_data)
                    except Exception as e:
                        print(f"[ERROR] 告警回调执行失败: {e}")

    def get_system_metrics(self) -> SystemResourceMetrics:
        """获取系统资源指标"""
        try:
            # 系统级指标
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # 进程级指标
            process_memory = self.process.memory_info()
            process_cpu = self.process.cpu_percent()
            thread_count = self.process.num_threads()

            return SystemResourceMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk.percent,
                process_memory_mb=process_memory.rss / 1024 / 1024,
                process_cpu_percent=process_cpu,
                thread_count=thread_count
            )

        except Exception as e:
            print(f"[ERROR] 获取系统指标失败: {e}")
            return SystemResourceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

    def get_metrics_by_name(self, metric_name: str, hours: int = 1) -> List[PerformanceMetric]:
        """
        按名称获取指标

        Args:
            metric_name: 指标名称
            hours: 时间范围（小时）

        Returns:
            指标列表
        """
        cutoff_time = time.time() - (hours * 3600)

        with self.lock:
            return [
                metric for metric in self.metrics
                if metric.metric_name == metric_name and metric.timestamp >= cutoff_time
            ]

    def get_metric_summary(self, metric_name: str) -> Dict[str, Any]:
        """获取指标摘要"""
        with self.lock:
            if metric_name not in self.metric_aggregates:
                return {"error": f"指标 {metric_name} 不存在"}

            agg = self.metric_aggregates[metric_name]
            recent_metrics = self.get_metrics_by_name(metric_name, hours=1)

            return {
                "metric_name": metric_name,
                "total_count": agg["count"],
                "average": agg["avg"],
                "minimum": agg["min"],
                "maximum": agg["max"],
                "recent_count": len(recent_metrics),
                "recent_average": sum(m.value for m in recent_metrics) / len(recent_metrics) if recent_metrics else 0
            }

    def set_alert_threshold(self, metric_name: str, max_value: float = None, min_value: float = None):
        """设置告警阈值"""
        thresholds = {}
        if max_value is not None:
            thresholds["max"] = max_value
        if min_value is not None:
            thresholds["min"] = min_value

        self.alert_thresholds[metric_name] = thresholds
        print(f"[INFO] 设置告警阈值: {metric_name} = {thresholds}")

    def add_alert_callback(self, callback: Callable):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)

    def start_monitoring(self, interval: int = 30):
        """
        启动后台监控

        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring_active:
            print("[WARNING] 监控已经在运行")
            return

        self.monitoring_active = True

        def monitoring_loop():
            while self.monitoring_active:
                try:
                    # 收集系统指标
                    sys_metrics = self.get_system_metrics()

                    # 记录系统指标
                    self.record_metric("system.cpu_percent", sys_metrics.cpu_percent)
                    self.record_metric("system.memory_percent", sys_metrics.memory_percent)
                    self.record_metric("system.disk_usage_percent", sys_metrics.disk_usage_percent)
                    self.record_metric("process.memory_mb", sys_metrics.process_memory_mb)
                    self.record_metric("process.cpu_percent", sys_metrics.process_cpu_percent)
                    self.record_metric("process.thread_count", sys_metrics.thread_count)

                    # 清理过期指标
                    self._cleanup_old_metrics()

                except Exception as e:
                    print(f"[ERROR] 监控循环异常: {e}")

                time.sleep(interval)

        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()

        print(f"[INFO] 后台监控已启动，间隔: {interval}s")

    def stop_monitoring(self):
        """停止后台监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        print("[INFO] 后台监控已停止")

    def _cleanup_old_metrics(self):
        """清理过期指标"""
        cutoff_time = time.time() - (self.retention_hours * 3600)

        with self.lock:
            # 从左侧移除过期指标
            while self.metrics and self.metrics[0].timestamp < cutoff_time:
                self.metrics.popleft()

    def export_metrics(self, filepath: str = None) -> str:
        """
        导出指标数据

        Args:
            filepath: 导出文件路径

        Returns:
            导出文件路径
        """
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"logs/performance/metrics_export_{timestamp}.json"

        # 确保目录存在
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)

        with self.lock:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "total_metrics": len(self.metrics),
                "metric_aggregates": dict(self.metric_aggregates),
                "system_metrics": self.get_system_metrics().to_dict(),
                "recent_metrics": [m.to_dict() for m in list(self.metrics)[-1000:]]  # 最近1000条
            }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"[INFO] 指标数据已导出到: {filepath}")
        return filepath


class ComponentPoolMonitor:
    """
    组件池化专用监控器

    专门监控组件池化系统的性能指标。
    """

    def __init__(self, performance_monitor: PerformanceMonitor):
        self.perf_monitor = performance_monitor
        self.pool_stats: Dict[str, Dict[str, Any]] = {}

        # 设置组件池化相关的告警阈值
        self._setup_pool_alerts()

        print("[INFO] 组件池化监控器初始化完成")

    def _setup_pool_alerts(self):
        """设置池化相关告警"""
        # Agent创建时间告警
        self.perf_monitor.set_alert_threshold("agent.creation_time", max_value=2.0)

        # 缓存命中率告警
        self.perf_monitor.set_alert_threshold("pool.cache_hit_rate", min_value=0.5)

        # 内存使用告警
        self.perf_monitor.set_alert_threshold("process.memory_mb", max_value=1024)

        # 并发请求数告警
        self.perf_monitor.set_alert_threshold("pool.concurrent_requests", max_value=50)

    def record_agent_creation(self, session_id: str, creation_time: float, success: bool = True):
        """记录Agent创建指标"""
        tags = {
            "session_id": session_id,
            "success": str(success)
        }

        self.perf_monitor.record_metric("agent.creation_time", creation_time, tags)
        self.perf_monitor.record_metric("agent.creation_count", 1, tags)

        if success:
            self.perf_monitor.record_metric("agent.creation_success", 1, tags)
        else:
            self.perf_monitor.record_metric("agent.creation_failure", 1, tags)

    def record_pool_metrics(self, pool_name: str, cache_hit_rate: float, pool_size: int,
                          concurrent_requests: int = 0):
        """记录池化指标"""
        tags = {"pool_name": pool_name}

        self.perf_monitor.record_metric("pool.cache_hit_rate", cache_hit_rate, tags)
        self.perf_monitor.record_metric("pool.size", pool_size, tags)
        self.perf_monitor.record_metric("pool.concurrent_requests", concurrent_requests, tags)

    def record_component_metrics(self, component_name: str, creation_time: float,
                               cache_hit: bool = False):
        """记录组件指标"""
        tags = {
            "component_name": component_name,
            "cache_hit": str(cache_hit)
        }

        self.perf_monitor.record_metric("component.creation_time", creation_time, tags)

        if cache_hit:
            self.perf_monitor.record_metric("component.cache_hit", 1, tags)
        else:
            self.perf_monitor.record_metric("component.cache_miss", 1, tags)

    def record_config_metrics(self, config_name: str, load_time: float, cache_level: str):
        """记录配置加载指标"""
        tags = {
            "config_name": config_name,
            "cache_level": cache_level
        }

        self.perf_monitor.record_metric("config.load_time", load_time, tags)
        self.perf_monitor.record_metric("config.access_count", 1, tags)

    def get_pool_dashboard_data(self) -> Dict[str, Any]:
        """获取池化仪表板数据"""
        # 获取最近1小时的指标
        recent_agent_creations = self.perf_monitor.get_metrics_by_name("agent.creation_time", hours=1)
        recent_pool_hits = self.perf_monitor.get_metrics_by_name("pool.cache_hit_rate", hours=1)
        recent_component_creations = self.perf_monitor.get_metrics_by_name("component.creation_time", hours=1)

        # 计算统计数据
        avg_creation_time = sum(m.value for m in recent_agent_creations) / len(recent_agent_creations) if recent_agent_creations else 0
        avg_hit_rate = sum(m.value for m in recent_pool_hits) / len(recent_pool_hits) if recent_pool_hits else 0

        # 成功率计算
        success_metrics = self.perf_monitor.get_metrics_by_name("agent.creation_success", hours=1)
        failure_metrics = self.perf_monitor.get_metrics_by_name("agent.creation_failure", hours=1)
        total_attempts = len(success_metrics) + len(failure_metrics)
        success_rate = len(success_metrics) / total_attempts if total_attempts > 0 else 0

        return {
            "performance": {
                "avg_agent_creation_time": avg_creation_time,
                "avg_cache_hit_rate": avg_hit_rate,
                "agent_creation_success_rate": success_rate,
                "total_agent_creations": len(recent_agent_creations),
                "total_component_creations": len(recent_component_creations)
            },
            "system": self.perf_monitor.get_system_metrics().to_dict(),
            "alerts": self._get_recent_alerts(),
            "timestamp": time.time()
        }

    def _get_recent_alerts(self) -> List[Dict[str, Any]]:
        """获取最近的告警（模拟实现）"""
        # 这里可以实现告警历史记录
        return []

    def generate_performance_report(self) -> str:
        """生成性能报告"""
        dashboard_data = self.get_pool_dashboard_data()

        report = []
        report.append("="*80)
        report.append("组件池化性能监控报告")
        report.append("="*80)
        report.append(f"报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        perf = dashboard_data["performance"]
        report.append(f"\n性能指标:")
        report.append(f"  平均Agent创建时间: {perf['avg_agent_creation_time']:.3f}s")
        report.append(f"  平均缓存命中率: {perf['avg_cache_hit_rate']:.2%}")
        report.append(f"  Agent创建成功率: {perf['agent_creation_success_rate']:.2%}")
        report.append(f"  总Agent创建次数: {perf['total_agent_creations']}")
        report.append(f"  总组件创建次数: {perf['total_component_creations']}")

        sys = dashboard_data["system"]
        report.append(f"\n系统资源:")
        report.append(f"  CPU使用率: {sys['cpu_percent']:.1f}%")
        report.append(f"  内存使用率: {sys['memory_percent']:.1f}%")
        report.append(f"  进程内存: {sys['process_memory_mb']:.1f}MB")
        report.append(f"  线程数: {sys['thread_count']}")

        # 性能评估
        report.append(f"\n性能评估:")
        if perf['avg_agent_creation_time'] < 0.5:
            report.append("✅ Agent创建性能优秀")
        elif perf['avg_agent_creation_time'] < 1.0:
            report.append("⚠️  Agent创建性能良好")
        else:
            report.append("❌ Agent创建性能需要优化")

        if perf['avg_cache_hit_rate'] > 0.8:
            report.append("✅ 缓存命中率优秀")
        elif perf['avg_cache_hit_rate'] > 0.5:
            report.append("⚠️  缓存命中率良好")
        else:
            report.append("❌ 缓存命中率需要优化")

        return "\n".join(report)


# ==================== 全局实例 ====================

# 创建全局监控实例
_global_performance_monitor: Optional[PerformanceMonitor] = None
_global_pool_monitor: Optional[ComponentPoolMonitor] = None
_monitor_lock = threading.Lock()


def get_performance_monitor() -> PerformanceMonitor:
    """
    获取全局性能监控器实例（单例模式）

    Returns:
        PerformanceMonitor实例
    """
    global _global_performance_monitor

    if _global_performance_monitor is None:
        with _monitor_lock:
            if _global_performance_monitor is None:
                _global_performance_monitor = PerformanceMonitor()
                print("[INFO] 全局性能监控器创建完成")

    return _global_performance_monitor


def get_component_pool_monitor() -> ComponentPoolMonitor:
    """
    获取全局组件池化监控器实例（单例模式）

    Returns:
        ComponentPoolMonitor实例
    """
    global _global_pool_monitor

    if _global_pool_monitor is None:
        with _monitor_lock:
            if _global_pool_monitor is None:
                perf_monitor = get_performance_monitor()
                _global_pool_monitor = ComponentPoolMonitor(perf_monitor)
                print("[INFO] 全局组件池化监控器创建完成")

    return _global_pool_monitor


# ==================== 便捷函数 ====================

def record_agent_creation_metric(session_id: str, creation_time: float, success: bool = True):
    """记录Agent创建指标的便捷函数"""
    monitor = get_component_pool_monitor()
    monitor.record_agent_creation(session_id, creation_time, success)


def record_pool_performance_metric(pool_name: str, cache_hit_rate: float, pool_size: int,
                                 concurrent_requests: int = 0):
    """记录池性能指标的便捷函数"""
    monitor = get_component_pool_monitor()
    monitor.record_pool_metrics(pool_name, cache_hit_rate, pool_size, concurrent_requests)


def record_component_performance_metric(component_name: str, creation_time: float,
                                      cache_hit: bool = False):
    """记录组件性能指标的便捷函数"""
    monitor = get_component_pool_monitor()
    monitor.record_component_metrics(component_name, creation_time, cache_hit)


def get_performance_dashboard_data() -> Dict[str, Any]:
    """获取性能仪表板数据的便捷函数"""
    monitor = get_component_pool_monitor()
    return monitor.get_pool_dashboard_data()


def generate_performance_report() -> str:
    """生成性能报告的便捷函数"""
    monitor = get_component_pool_monitor()
    return monitor.generate_performance_report()


def start_background_monitoring(interval: int = 30):
    """启动后台监控的便捷函数"""
    monitor = get_performance_monitor()
    monitor.start_monitoring(interval)


def export_performance_metrics(filepath: str = None) -> str:
    """导出性能指标的便捷函数"""
    monitor = get_performance_monitor()
    return monitor.export_metrics(filepath)


# ==================== 告警回调示例 ====================

def default_alert_callback(alert_data: Dict[str, Any]):
    """默认告警回调函数"""
    metric_name = alert_data["metric_name"]
    value = alert_data["value"]
    alert_type = alert_data["alert_type"]
    threshold = alert_data["threshold"]

    print(f"[ALERT] {metric_name}: {value} ({alert_type})")
    print(f"        阈值: {threshold}")
    print(f"        时间: {datetime.fromtimestamp(alert_data['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}")


# 注册默认告警回调
def setup_default_alerts():
    """设置默认告警"""
    monitor = get_performance_monitor()
    monitor.add_alert_callback(default_alert_callback)


# ==================== 导出接口 ====================

__all__ = [
    # 核心类
    'PerformanceMonitor',
    'ComponentPoolMonitor',
    'PerformanceMetric',
    'SystemResourceMetrics',

    # 全局实例获取函数
    'get_performance_monitor',
    'get_component_pool_monitor',

    # 便捷函数
    'record_agent_creation_metric',
    'record_pool_performance_metric',
    'record_component_performance_metric',
    'get_performance_dashboard_data',
    'generate_performance_report',
    'start_background_monitoring',
    'export_performance_metrics',

    # 告警相关
    'default_alert_callback',
    'setup_default_alerts',
]