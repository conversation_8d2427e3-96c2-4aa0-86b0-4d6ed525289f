import React, { useState, useEffect } from 'react';
import ChatArea from '../components/ChatArea';
import AnalysisResultArea from '../components/AnalysisResultArea';
import SessionTestPanel from '../components/SessionTestPanel';

import { Button } from '@/components/ui/button';
import { RefreshCw, PanelRightOpen, PanelRightClose } from 'lucide-react';
import { Message, DomainResult, CategoryResult, FocusPointStatus } from '../types';

const ConversationPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    { text: 'HI，您好！我是由己小助手，我擅长协助您梳理需求，形成准确的需求文档。', sender: 'ai' },
  ]);

  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [domainResult, setDomainResult] = useState<DomainResult | null>(null);
  const [categoryResult, setCategoryResult] = useState<CategoryResult | null>(null);
  const [focusPointsStatus, setFocusPointsStatus] = useState<FocusPointStatus[] | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isAnalysisPanelVisible, setIsAnalysisPanelVisible] = useState(false);
  const [userId, setUserId] = useState<string>('user_default');

  // 进度状态
  const [progressVisible, setProgressVisible] = useState(false);
  const [progressStage, setProgressStage] = useState('');
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');

  useEffect(() => {
    const newSessionId = crypto.randomUUID();
    setSessionId(newSessionId);
    console.log("Generated new session ID:", newSessionId);
  }, []);

  // 连接进度流
  const connectProgressStream = (currentSessionId: string) => {
    console.log('尝试连接进度流:', currentSessionId, userId);
    const eventSource = new EventSource(`/api/progress/${currentSessionId}?user_id=${userId}`);

    eventSource.onopen = () => {
      console.log('进度流连接已建立');
      setProgressVisible(true);
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('收到进度更新:', data);

        if (data.type === 'progress') {
          console.log('设置进度状态:', data.stage, data.percentage, data.message);
          setProgressStage(data.stage);
          setProgressPercentage(data.percentage);
          setProgressMessage(data.message);
          setProgressVisible(true); // 确保进度可见
        } else if (data.type === 'connected') {
          console.log('进度流连接确认:', data.session_id);
          setProgressVisible(true);
        }
      } catch (error) {
        console.error('解析进度数据失败:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('进度流连接错误:', error);
      eventSource.close();
      setProgressVisible(false);
    };

    return eventSource;
  };

  const handleSendMessageInternal = async (text: string, currentSessionId: string) => {
    let progressStream: EventSource | null = null;

    try {
      // 启动进度流连接
      progressStream = connectProgressStream(currentSessionId);

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': userId, // 添加多用户支持
        },
        body: JSON.stringify({ message: text, session_id: currentSessionId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Backend response data:', data);

      // 详细日志记录，用于调试领域ID映射问题
      if (data.domain_result) {
        console.log('Domain result details:', {
          domain_id: data.domain_result.domain_id,
          confidence: data.domain_result.confidence,
          reasoning: data.domain_result.reasoning,
          status: data.domain_result.status
        });
      }

      if (data.category_result) {
        console.log('Category result details:', {
          category_id: data.category_result.category_id,
          category_name: data.category_result.category_name,
          confidence: data.category_result.confidence,
          reasoning: data.category_result.reasoning,
          status: data.category_result.status
        });
      }

      let aiResponseText = 'AI 未提供文本回复。';
      if (data.response) {
        if (typeof data.response === 'string') {
          aiResponseText = data.response;
        } else if (typeof data.response === 'object') {
          aiResponseText = data.response.content || JSON.stringify(data.response);
        }
      }
      
      setIsLoading(false);
      setIsStreaming(true);
      
      const newAiMessage: Message = { text: aiResponseText, sender: 'ai' };
      setMessages((prevMessages) => [...prevMessages, newAiMessage]);

      setDomainResult(data.domain_result ?? null);
      setCategoryResult(data.category_result ?? null);
      setFocusPointsStatus(data.focus_points_status ?? null);
      
      setTimeout(() => {
        setIsStreaming(false);
      }, aiResponseText.length * 10);

    } catch (error) {
      console.error('向后端发送消息时出错:', error);
      const errorMessage: Message = { text: '与后端通信失败，请稍后再试。', sender: 'ai' };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
    } finally {
      setIsLoading(false);

      // 延迟关闭进度流，让用户看到完成状态
      if (progressStream) {
        setTimeout(() => {
          progressStream.close();
          setProgressVisible(false);
        }, 2000); // 2秒后关闭
      }
    }
  };

  const handleSendMessage = async (text: string) => {
    const newUserMessage: Message = { text, sender: 'user' };
    setMessages((prevMessages) => [...prevMessages, newUserMessage]);

    setIsLoading(true);

    if (sessionId) {
      await handleSendMessageInternal(text, sessionId);
    } else {
      console.error("Session ID not available when sending message.");
      const errorMessage: Message = { text: '错误：无法发送消息，会话未初始化。', sender: 'ai' };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
      setIsLoading(false);
    }
  };

  const handleNewChat = () => {
    const newId = crypto.randomUUID();
    setSessionId(newId);
    setMessages([{ text: '您好！请问有什么需求可以帮助您采集？', sender: 'ai' }]);

    // 强制清理所有分析结果状态
    setDomainResult(null);
    setCategoryResult(null);
    setFocusPointsStatus(null);
    setIsLoading(false);
    setIsStreaming(false);

    console.log("Started new chat with session ID:", newId);
    console.log("Cleared all analysis results for new chat");
  };

  const handleSessionIdChange = (newSessionId: string) => {
    setSessionId(newSessionId);
    console.log("Session ID changed to:", newSessionId);
  };

  const handleRecoveryTest = async () => {
    if (!sessionId) {
      console.error("No session ID available for recovery test");
      return;
    }

    console.log("Testing session recovery for:", sessionId);
    const testMessage = "请继续我们之前的对话";
    await handleSendMessage(testMessage);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200 px-6 py-3 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={handleNewChat}>
              <RefreshCw className="mr-2 h-4 w-4" /> 新聊天
            </Button>

            {/* 用户ID输入框 */}
            <div className="flex items-center space-x-2">
              <label htmlFor="userId" className="text-sm font-medium text-gray-700">
                用户ID:
              </label>
              <input
                id="userId"
                type="text"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入用户ID"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAnalysisPanelVisible(!isAnalysisPanelVisible)}
              className="flex items-center"
            >
              {isAnalysisPanelVisible ? (
                <>
                  <PanelRightClose className="mr-2 h-4 w-4" />
                  隐藏分析
                </>
              ) : (
                <>
                  <PanelRightOpen className="mr-2 h-4 w-4" />
                  显示分析
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-row flex-grow overflow-hidden">
        <div className={`flex flex-col h-full overflow-hidden transition-all duration-300 ${
          isAnalysisPanelVisible ? 'flex-grow' : 'w-full'
        }`}>
          <ChatArea
            messages={messages}
            isLoading={isLoading}
            isStreaming={isStreaming}
            onSendMessage={handleSendMessage}
            progressStage={progressStage}
            progressPercentage={progressPercentage}
          />
        </div>

        <div className={`transition-all duration-300 ease-in-out bg-gray-50 border-l border-gray-200 ${
          isAnalysisPanelVisible
            ? 'w-80 flex-shrink-0'
            : 'w-0 overflow-hidden'
        }`}>
          {isAnalysisPanelVisible && (
            <div className="h-full overflow-y-auto">
              <AnalysisResultArea
                domainResult={domainResult}
                categoryResult={categoryResult}
                focusPointsStatus={focusPointsStatus}
              />
            </div>
          )}
        </div>
      </div>

      <SessionTestPanel
        currentSessionId={sessionId || 'No Session ID'}
        onSessionIdChange={handleSessionIdChange}
        onRecoveryTest={handleRecoveryTest}
      />
    </div>
  );
};

export default ConversationPage;
