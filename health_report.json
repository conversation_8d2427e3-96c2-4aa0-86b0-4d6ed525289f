{"timestamp": "2025-07-28T13:38:00.659027", "health_score": 54, "total_issues": 6, "issues": {"consistency_issues": [{"type": "decision_engine_inconsistency", "severity": "medium", "message": "发现4种决策引擎实现", "engines": [{"file": "backend/agents/decision_engine.py", "engine": "class DecisionEngine"}, {"file": "backend/agents/simplified_decision_engine.py", "engine": "class SimplifiedDecisionEngine"}, {"file": "backend/agents/simplified_decision_engine.py", "engine": "class SimplifiedDecisionEngine"}, {"file": "backend/agents/hybrid_intent_recognition_engine.py", "engine": "class HybridIntentRecognitionEngine"}], "recommendation": "统一使用一种决策引擎"}, {"type": "llm_usage_inconsistency", "severity": "medium", "message": "发现多种LLM客户端创建方式", "patterns": {"OpenAI\\(": ["backend/agents/unified_llm_client_factory.py", "backend/agents/llm_service.py", ".venv_autogen/lib/python3.11/site-packages/huggingface_hub/inference/_client.py", ".venv_autogen/lib/python3.11/site-packages/huggingface_hub/inference/_generated/_async_client.py", ".venv_autogen/lib/python3.11/site-packages/chromadb/utils/embedding_functions/baseten_embedding_function.py", ".venv_autogen/lib/python3.11/site-packages/chromadb/utils/embedding_functions/openai_embedding_function.py", ".venv_autogen/lib/python3.11/site-packages/posthog/ai/openai/openai.py", ".venv_autogen/lib/python3.11/site-packages/posthog/ai/openai/openai_providers.py", ".venv_autogen/lib/python3.11/site-packages/posthog/ai/openai/openai_async.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agentchat/contrib/capabilities/generate_images.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agentchat/contrib/graph_rag/neo4j_graph_query_engine.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agentchat/contrib/rag/chromadb_query_engine.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agentchat/contrib/rag/llamaindex_query_engine.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agentchat/contrib/rag/mongodb_query_engine.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agentchat/realtime/experimental/clients/oai/base_client.py", ".venv_autogen/lib/python3.11/site-packages/autogen/interop/langchain/langchain_chat_model_factory.py", ".venv_autogen/lib/python3.11/site-packages/autogen/agents/experimental/document_agent/chroma_query_engine.py", ".venv_autogen/lib/python3.11/site-packages/autogen/oai/client.py", ".venv_autogen/lib/python3.11/site-packages/openai/_client.py", ".venv_autogen/lib/python3.11/site-packages/openai/resources/beta/chat/completions.py", ".venv_autogen/lib/python3.11/site-packages/openai/lib/azure.py"], "ChatOpenAI\\(": [".venv_autogen/lib/python3.11/site-packages/autogen/interop/langchain/langchain_chat_model_factory.py"], "LLMClient\\(": ["examples/dynamic_reply_generator_example.py", "examples/message_reply_integration_example.py", "examples/integrated_reply_system_example.py"]}, "recommendation": "统一使用一种LLM客户端创建方式"}], "dead_code": [{"type": "unused_imports", "severity": "low", "count": 19783, "items": [{"file": "run_api.py", "import": "import sys", "unused_name": "sys"}, {"file": "run_frontend.py", "import": "import sys", "unused_name": "sys"}, {"file": "frontend/node_modules/flatted/python/flatted.py", "import": "import json as _json", "unused_name": "json as _json"}, {"file": "admin-backend/admin_utils/file_handler.py", "import": "import os", "unused_name": "os"}, {"file": "admin-backend/admin_utils/yaml_handler.py", "import": "from typing import Any, Dict, Optional", "unused_name": "Optional"}, {"file": "admin-backend/admin_services/business_rules_service.py", "import": "from typing import Dict, Any, Optional", "unused_name": "Optional"}, {"file": "admin-backend/admin_services/database_service.py", "import": "import os", "unused_name": "os"}, {"file": "admin-backend/admin_services/database_service.py", "import": "from typing import Dict, List, Any, Optional, Tuple", "unused_name": "<PERSON><PERSON>"}, {"file": "admin-backend/admin_services/database_service.py", "import": "from admin_utils.exceptions import DatabaseError, FileOperationError", "unused_name": "FileOperationError"}, {"file": "admin-backend/admin_services/config_service.py", "import": "import asyncio", "unused_name": "asyncio"}], "recommendation": "删除未使用的导入语句"}, {"type": "deprecated_files", "severity": "medium", "count": 23, "files": ["cleanup_backup/20250728_132733/backend/tests/test_data_generator.py", "cleanup_backup/20250728_132733/backend/tests/business_logic_regression_tests.py", "cleanup_backup/20250728_132733/backend/tests/run_business_logic_tests.py", "cleanup_backup/20250728_132733/backend/tests/test_keyword_accelerator.py", "cleanup_backup/20250728_132733/backend/tests/test_component_pooling_integration.py", "cleanup_backup/20250728_132733/backend/tests/integration/test_hybrid_agent.py", "cleanup_backup/20250728_132733/backend/tests/agents/test_hybrid_conversation_router_fix.py", "cleanup_backup/20250728_132733/backend/tests/agents/test_rag_knowledge_base_agent.py", "cleanup_backup/20250728_132733/backend/tests/agents/test_hybrid_intent_recognition_engine.py", "cleanup_backup/20250728_132733/backend/tests/performance/test_hybrid_intent_performance.py", "cleanup_backup/20250728_132733/backend/tests/functional/test_core_modules.py", "cleanup_backup/20250728_132733/backend/tests/autogen_agents/test_llm_service.py", "cleanup_backup/20250728_132733/backend/utils/startup_optimizer.py", "cleanup_backup/20250728_132733/backend/utils/check_log_duplicates.py", "cleanup_backup/20250728_132733/backend/utils/simple_benchmark.py", "cleanup_backup/20250728_132733/backend/utils/minimal_benchmark.py", "cleanup_backup/20250728_132733/backend/utils/sensitive_data_masker.py", "cleanup_backup/20250728_132733/backend/utils/agent_creation_benchmark.py", "cleanup_backup/20250728_132733/backend/utils/log_components/formatters.py", "cleanup_backup/20250728_132733/backend/utils/log_components/handlers.py", "cleanup_backup/20250728_132733/backend/utils/log_components/filters.py", "cleanup_backup/20250728_132733/backend/scripts/init_focus_point_tables.py", "cleanup_backup/20250728_132733/backend/monitoring/performance_monitor.py"], "recommendation": "检查并删除废弃的文件"}], "documentation_issues": [{"type": "missing_components", "severity": "medium", "message": "文档中描述的组件在代码中不存在", "components": ["OpenRouter", "BusinessLogicEnhancedKeywordMatcher", "IDecisionEngine", "SequenceMatcher", "ConfigurableIntentMatcher", "EnhancedSessionManager", "LLMUnifiedConfigManager", "ThreeLevelIntentMatcher", "EnhancedConversationManager", "EnhancedKeywordMatcher", "ConfigManager", "ConcurrentSessionManager", "UnifiedConfigManager", "UnifiedEngine", "OriginalEngine", "PriorityManager", "LegacyConfigManager", "AsyncContextManager", "TransactionManager", "KeywordMatcher", "EnhancedSemanticMatcher", "TestIntentMatcher", "LLMIntentMatcher", "OptimizedUnifiedConfigManager", "BusinessLogicProtectedIntentMatcher", "DynamicConfigManager", "AcceleratedIntentDecisionEngine", "EmbeddingSemanticMatcher", "HybridDecisionEngine", "SynonymMatcher", "ConfigurableKeywordMatcher", "ClassifierMappingManager", "OptimizedConfigManager", "IntelligentKeywordMatcher", "HybridRouter", "ChineseSemanticMatcher", "IntentConfigManager", "DialogueManager", "BusinessStateManager", "ProgressiveMigrationManager", "FocusPointStatusManager", "BusinessRulesEngine", "IntentDecisionEngine", "StateAwareDecisionEngine", "LLMCallManager", "IntentEngine", "UnifiedDecisionEngine", "StaticTemplateManager", "<PERSON>zzyMatch<PERSON>", "DataConsistencyManager", "OldIntentEngine", "SafeCleanupManager"], "recommendation": "更新文档，移除不存在的组件描述"}, {"type": "undocumented_components", "severity": "low", "message": "代码中存在的组件未在文档中描述", "components": ["SwitchTransformersLayerFF", "PostgresBase", "ConstrainedFloat", "AbstractRelationshipLoader", "ActionMove", "TimeStampResp", "RoCBertTokenizer", "SamVisionEncoderOutput", "SystemProcessesStatusValues", "DefaultCredentialsError"], "recommendation": "为重要组件添加文档描述"}]}, "recommendations": [{"priority": "medium", "category": "code_cleanup", "issue": "deprecated_files", "action": "检查并删除废弃的文件", "estimated_effort": "1-2天"}, {"priority": "low", "category": "documentation_sync", "issue": "文档中描述的组件在代码中不存在", "action": "更新文档，移除不存在的组件描述", "estimated_effort": "0.5-1天"}, {"priority": "low", "category": "documentation_sync", "issue": "代码中存在的组件未在文档中描述", "action": "为重要组件添加文档描述", "estimated_effort": "0.5-1天"}]}