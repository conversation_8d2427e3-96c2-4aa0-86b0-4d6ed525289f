#!/usr/bin/env python3
"""
端到端集成测试：验证整个意图管理统一化系统

目标：验证从配置文件到模板到决策引擎的完整流程
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_complete_integration():
    """测试完整的集成流程"""
    print("🧪 开始端到端集成测试\n")
    
    try:
        # 1. 测试配置文件加载
        print("=== 步骤1: 配置文件加载 ===")
        from backend.utils.intent_manager import IntentManager
        
        intent_manager = IntentManager()
        config_info = intent_manager.get_config_info()
        
        print(f"✅ 配置文件加载成功")
        print(f"   版本: {config_info.get('version')}")
        print(f"   意图数量: {config_info.get('intent_count')}")
        
        # 2. 测试决策引擎集成
        print(f"\n=== 步骤2: 决策引擎集成 ===")
        from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
        
        decision_engine = SimplifiedDecisionEngine()
        
        if decision_engine.intent_manager:
            print("✅ 决策引擎成功集成 IntentManager")
            
            # 测试配置驱动的意图验证
            engine_intents = decision_engine.intent_manager.get_valid_intents()
            print(f"   决策引擎获取意图数量: {len(engine_intents)}")
            
            # 验证关键意图
            key_intents = ["greeting", "business_requirement", "domain_specific_query"]
            for intent in key_intents:
                is_valid = decision_engine.intent_manager.is_valid_intent(intent)
                status = "✅" if is_valid else "❌"
                print(f"   {status} 关键意图 '{intent}': {is_valid}")
        else:
            print("⚠️ 决策引擎使用备用模式")
        
        # 3. 测试模板同步
        print(f"\n=== 步骤3: 模板同步验证 ===")
        from backend.utils.template_synchronizer import TemplateSynchronizer
        
        synchronizer = TemplateSynchronizer(intent_manager)
        is_synced, differences = synchronizer.validate_template_sync()
        
        print(f"✅ 模板同步状态: {'同步' if is_synced else '不同步'}")
        
        if differences:
            print("   发现差异:")
            for diff in differences:
                print(f"   - {diff}")
        else:
            print("   ✅ 模板与配置完全同步")
        
        # 4. 测试意图一致性
        print(f"\n=== 步骤4: 意图一致性验证 ===")
        
        # 获取各个组件的意图列表
        config_intents = set(intent_manager.get_valid_intents())
        engine_intents = set(decision_engine.intent_manager.get_valid_intents() if decision_engine.intent_manager else [])
        
        # 从模板提取意图
        template_path = Path("backend/prompts/intent_recognition.md")
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            template_intents = set(synchronizer._extract_intents_from_template(template_content))
        else:
            template_intents = set()
        
        print(f"   配置文件意图数量: {len(config_intents)}")
        print(f"   决策引擎意图数量: {len(engine_intents)}")
        print(f"   模板文件意图数量: {len(template_intents)}")
        
        # 检查一致性
        all_consistent = (config_intents == engine_intents == template_intents)
        
        if all_consistent:
            print("✅ 所有组件的意图定义完全一致")
        else:
            print("❌ 发现意图定义不一致:")
            
            if config_intents != engine_intents:
                missing_in_engine = config_intents - engine_intents
                extra_in_engine = engine_intents - config_intents
                if missing_in_engine:
                    print(f"   决策引擎缺少: {sorted(missing_in_engine)}")
                if extra_in_engine:
                    print(f"   决策引擎多余: {sorted(extra_in_engine)}")
            
            if config_intents != template_intents:
                missing_in_template = config_intents - template_intents
                extra_in_template = template_intents - config_intents
                if missing_in_template:
                    print(f"   模板缺少: {sorted(missing_in_template)}")
                if extra_in_template:
                    print(f"   模板多余: {sorted(extra_in_template)}")
        
        # 5. 测试配置完整性检查
        print(f"\n=== 步骤5: 配置完整性检查 ===")
        
        # 检查关键意图是否都存在
        required_intents = [
            "greeting", "business_requirement", "unknown", 
            "general_chat", "request_clarification", "domain_specific_query"
        ]
        
        missing_required = []
        for intent in required_intents:
            if not intent_manager.is_valid_intent(intent):
                missing_required.append(intent)
        
        if missing_required:
            print(f"❌ 缺少关键意图: {missing_required}")
        else:
            print("✅ 所有关键意图都已正确配置")
        
        # 检查决策规则
        decision_rules = intent_manager.get_decision_rules()
        if decision_rules:
            print("✅ 决策规则配置正常")
            priority_order = intent_manager.get_priority_order()
            print(f"   优先级顺序包含 {len(priority_order)} 个意图")
        else:
            print("⚠️ 决策规则配置缺失")
        
        # 总结测试结果
        print(f"\n=== 测试结果总结 ===")
        
        success_conditions = [
            config_info.get('intent_count', 0) > 0,  # 配置加载成功
            decision_engine.intent_manager is not None,  # 决策引擎集成成功
            is_synced,  # 模板同步
            all_consistent,  # 意图一致性
            len(missing_required) == 0,  # 关键意图完整
            bool(decision_rules)  # 决策规则存在
        ]
        
        passed_count = sum(success_conditions)
        total_count = len(success_conditions)
        
        print(f"通过条件: {passed_count}/{total_count}")
        
        test_items = [
            "配置文件加载",
            "决策引擎集成", 
            "模板同步",
            "意图一致性",
            "关键意图完整性",
            "决策规则配置"
        ]
        
        for i, (item, passed) in enumerate(zip(test_items, success_conditions)):
            status = "✅" if passed else "❌"
            print(f"   {status} {item}")
        
        return passed_count == total_count
        
    except Exception as e:
        print(f"\n❌ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始意图管理统一化端到端测试\n")
    
    success = test_complete_integration()
    
    print("\n" + "="*60)
    
    if success:
        print("🎉 端到端集成测试完全通过！")
        print("\n📋 系统状态:")
        print("- [x] 配置文件正确加载")
        print("- [x] 决策引擎成功集成")
        print("- [x] 模板与配置同步")
        print("- [x] 所有组件意图一致")
        print("- [x] 关键意图完整")
        print("- [x] 决策规则正常")
        print("\n🎯 意图管理统一化实施成功！")
        print("✅ 系统已实现单一数据源的意图管理")
        print("✅ 模板与代码不再存在不同步问题")
        print("✅ 启动时自动检测配置完整性")
        print("✅ 保持了完整的向后兼容性")
    else:
        print("⚠️ 端到端测试存在问题")
        print("请检查失败的测试项并修复后重新测试")

if __name__ == "__main__":
    main()
