
# LLM客户端迁移指南

## 统一LLM客户端工厂的优势

1. **配置统一**: 所有LLM配置集中管理
2. **类型安全**: 预定义的配置类型，避免配置错误
3. **性能优化**: 客户端缓存，避免重复创建
4. **易于维护**: 统一的创建方式，便于调试和维护

## 迁移示例

### 原来的方式:
```python
from backend.agents.llm_service import LLMService
llm_service = LLMService()
```

### 新的方式:
```python
from backend.agents.unified_llm_client_factory import create_llm_client
llm_client = create_llm_client('default')
```

### 针对特定任务:
```python
# 领域分类
domain_llm = create_llm_client('domain_classification')

# 问题优化
question_llm = create_llm_client('question_optimization')

# 文档生成
doc_llm = create_llm_client('document_generation')
```

## 可用的配置类型

- `default`: 默认配置
- `domain_classification`: 领域分类（低温度，高准确性）
- `category_classification`: 类别分类
- `intent_recognition`: 意图识别
- `information_extraction`: 信息提取
- `question_optimization`: 问题优化（高温度，更有创意）
- `document_generation`: 文档生成（使用GPT-4）
- `conversation`: 对话交互

## 自定义配置

```python
# 使用自定义参数
custom_llm = create_llm_client('default', temperature=0.9, max_tokens=2000)
```
