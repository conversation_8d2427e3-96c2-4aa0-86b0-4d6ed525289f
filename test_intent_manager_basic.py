#!/usr/bin/env python3
"""
意图管理器基础功能测试

目标：验证阶段1的基础设施是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_intent_manager_basic():
    """测试意图管理器基础功能"""
    print("🧪 开始测试意图管理器基础功能\n")
    
    try:
        # 导入意图管理器
        from backend.utils.intent_manager import IntentManager, get_intent_manager
        print("✅ 成功导入 IntentManager")
        
        # 创建意图管理器实例
        manager = IntentManager()
        print("✅ 成功创建 IntentManager 实例")
        
        # 测试配置加载
        config_info = manager.get_config_info()
        print(f"✅ 配置信息加载成功:")
        print(f"   - 版本: {config_info.get('version')}")
        print(f"   - 意图数量: {config_info.get('intent_count')}")
        print(f"   - 配置路径: {config_info.get('config_path')}")
        
        # 测试获取有效意图列表
        valid_intents = manager.get_valid_intents()
        print(f"✅ 获取有效意图列表成功，共 {len(valid_intents)} 个意图")
        print(f"   前5个意图: {valid_intents[:5]}")
        
        # 测试意图验证
        test_cases = [
            ("greeting", True),
            ("business_requirement", True),
            ("domain_specific_query", True),
            ("invalid_intent", False),
            ("", False)
        ]
        
        print("\n🔍 测试意图验证功能:")
        for intent, expected in test_cases:
            result = manager.is_valid_intent(intent)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{intent}' -> {result} (期望: {expected})")
        
        # 测试获取意图配置
        print("\n🔍 测试获取意图配置:")
        greeting_config = manager.get_intent_config("greeting")
        if greeting_config:
            print("✅ 获取 greeting 意图配置成功:")
            print(f"   - 描述: {greeting_config.get('description')}")
            print(f"   - 动作: {greeting_config.get('action')}")
            print(f"   - 优先级: {greeting_config.get('priority')}")
        else:
            print("❌ 获取 greeting 意图配置失败")
        
        # 测试获取动作和优先级
        print("\n🔍 测试获取意图属性:")
        test_intents = ["greeting", "business_requirement", "domain_specific_query"]
        for intent in test_intents:
            action = manager.get_intent_action(intent)
            priority = manager.get_intent_priority(intent)
            states = manager.get_supported_states(intent)
            print(f"   {intent}:")
            print(f"     - 动作: {action}")
            print(f"     - 优先级: {priority}")
            print(f"     - 支持状态: {states}")
        
        # 测试状态转换规则
        print("\n🔍 测试状态转换规则:")
        idle_transitions = manager.get_state_transitions("IDLE")
        print(f"✅ IDLE 状态转换规则，共 {len(idle_transitions)} 条:")
        for intent, target_state in list(idle_transitions.items())[:5]:
            print(f"   {intent} -> {target_state}")
        
        # 测试决策规则
        print("\n🔍 测试决策规则:")
        decision_rules = manager.get_decision_rules()
        priority_order = manager.get_priority_order()
        default_action = manager.get_default_action()
        confidence_thresholds = manager.get_confidence_thresholds()
        
        print(f"✅ 决策规则加载成功:")
        print(f"   - 优先级顺序: {priority_order[:5]}... (共{len(priority_order)}个)")
        print(f"   - 默认动作: {default_action}")
        print(f"   - 置信度阈值: {confidence_thresholds}")
        
        # 测试单例模式
        print("\n🔍 测试单例模式:")
        singleton_manager = get_intent_manager()
        if singleton_manager is not None:
            print("✅ 单例模式工作正常")
        else:
            print("❌ 单例模式失败")
        
        print("\n🎉 所有基础功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_existence():
    """测试配置文件是否存在"""
    print("🧪 测试配置文件存在性\n")
    
    config_path = Path("backend/config/intent_definitions.yaml")
    if config_path.exists():
        print(f"✅ 配置文件存在: {config_path}")
        
        # 检查文件大小
        file_size = config_path.stat().st_size
        print(f"✅ 文件大小: {file_size} 字节")
        
        # 尝试读取文件
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"✅ 文件可读，内容长度: {len(content)} 字符")
            return True
        except Exception as e:
            print(f"❌ 文件读取失败: {e}")
            return False
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始阶段1基础设施测试\n")
    
    # 测试1: 配置文件存在性
    print("=== 测试1: 配置文件存在性 ===")
    test1_result = test_config_file_existence()
    
    print("\n" + "="*50 + "\n")
    
    # 测试2: 意图管理器基础功能
    print("=== 测试2: 意图管理器基础功能 ===")
    test2_result = test_intent_manager_basic()
    
    # 总结
    print("\n" + "="*50)
    print("🏁 阶段1基础设施测试总结")
    print(f"配置文件测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"意图管理器测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 阶段1基础设施搭建成功！")
        print("\n📋 阶段1完成状态:")
        print("- [x] 创建 intent_definitions.yaml 配置文件")
        print("- [x] 开发 IntentManager 工具类")
        print("- [x] 验证配置加载功能")
        print("\n🎯 准备进入阶段2: 核心集成")
    else:
        print("⚠️ 阶段1存在问题，需要修复后再继续")

if __name__ == "__main__":
    main()
