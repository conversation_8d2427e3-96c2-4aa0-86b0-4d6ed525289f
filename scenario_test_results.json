{"timestamp": "2025-07-26T22:25:31.869692", "scenarios": [{"scenario_id": 1, "scenario_name": "正常需求采集流程", "total_steps": 4, "passed_steps": 1, "failed_steps": 3, "step_results": [{"input": "我想做一个电商网站", "current_state": "IDLE", "status": "passed", "actual_result": {"input": "我想做一个电商网站", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "business_requirement", "matched_keyword": "我想做", "new_state": "COLLECTING_INFO"}, "expected_result": {}, "error": null, "validation_details": [{"type": "state", "expected": "COLLECTING_INFO", "actual": "COLLECTING_INFO", "passed": true}]}, {"input": "主要卖服装和配饰", "current_state": "IDLE", "status": "failed", "actual_result": {"input": "主要卖服装和配饰", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": false, "llm_calls": 1, "intent": "provide_information", "new_state": "IDLE"}, "expected_result": {}, "error": "状态不匹配: 期望 COLLECTING_INFO, 实际 IDLE", "validation_details": [{"type": "state", "expected": "COLLECTING_INFO", "actual": "IDLE", "passed": false}]}, {"input": "需要支付功能和用户管理", "current_state": "IDLE", "status": "failed", "actual_result": {"input": "需要支付功能和用户管理", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": false, "llm_calls": 1, "intent": "provide_information", "new_state": "IDLE"}, "expected_result": {}, "error": "状态不匹配: 期望 COLLECTING_INFO, 实际 IDLE", "validation_details": [{"type": "state", "expected": "COLLECTING_INFO", "actual": "IDLE", "passed": false}]}, {"input": "预算大概10万", "current_state": "IDLE", "status": "failed", "actual_result": {"input": "预算大概10万", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": false, "llm_calls": 1, "intent": "provide_information", "new_state": "IDLE"}, "expected_result": {}, "error": "状态不匹配: 期望 DOCUMENTING, 实际 IDLE", "validation_details": [{"type": "state", "expected": "DOCUMENTING", "actual": "IDLE", "passed": false}]}], "status": "failed", "error_messages": ["状态不匹配: 期望 COLLECTING_INFO, 实际 IDLE", "状态不匹配: 期望 COLLECTING_INFO, 实际 IDLE", "状态不匹配: 期望 DOCUMENTING, 实际 IDLE"]}, {"scenario_id": 2, "scenario_name": "用户回答处理", "total_steps": 3, "passed_steps": 3, "failed_steps": 0, "step_results": [{"input": "这是一个B2C电商平台", "current_state": "COLLECTING_INFO", "status": "passed", "actual_result": {"input": "这是一个B2C电商平台", "current_state": "COLLECTING_INFO", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "provide_information", "new_state": "COLLECTING_INFO"}, "expected_result": {}, "error": null, "validation_details": [{"type": "intent", "expected": "provide_information", "actual": "provide_information", "passed": true}]}, {"input": "主要面向年轻用户", "current_state": "COLLECTING_INFO", "status": "passed", "actual_result": {"input": "主要面向年轻用户", "current_state": "COLLECTING_INFO", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "provide_information", "new_state": "COLLECTING_INFO"}, "expected_result": {}, "error": null, "validation_details": [{"type": "intent", "expected": "provide_information", "actual": "provide_information", "passed": true}]}, {"input": "需要移动端适配", "current_state": "COLLECTING_INFO", "status": "passed", "actual_result": {"input": "需要移动端适配", "current_state": "COLLECTING_INFO", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "provide_information", "new_state": "COLLECTING_INFO"}, "expected_result": {}, "error": null, "validation_details": [{"type": "intent", "expected": "provide_information", "actual": "provide_information", "passed": true}]}], "status": "passed", "error_messages": []}, {"scenario_id": 3, "scenario_name": "状态转换验证", "total_steps": 3, "passed_steps": 3, "failed_steps": 0, "step_results": [{"input": "你好", "current_state": "IDLE", "status": "passed", "actual_result": {"input": "你好", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "greeting", "matched_keyword": "你好", "new_state": "IDLE"}, "expected_result": {}, "error": null, "validation_details": [{"type": "state", "expected": "IDLE", "actual": "IDLE", "passed": true}, {"type": "intent", "expected": "greeting", "actual": "greeting", "passed": true}]}, {"input": "我想做个网站", "current_state": "IDLE", "status": "passed", "actual_result": {"input": "我想做个网站", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "business_requirement", "matched_keyword": "我想做", "new_state": "COLLECTING_INFO"}, "expected_result": {}, "error": null, "validation_details": [{"type": "state", "expected": "COLLECTING_INFO", "actual": "COLLECTING_INFO", "passed": true}, {"type": "intent", "expected": "business_requirement", "actual": "business_requirement", "passed": true}]}, {"input": "确认", "current_state": "DOCUMENTING", "status": "passed", "actual_result": {"input": "确认", "current_state": "DOCUMENTING", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "confirm", "matched_keyword": "确认", "new_state": "IDLE"}, "expected_result": {}, "error": null, "validation_details": [{"type": "state", "expected": "IDLE", "actual": "IDLE", "passed": true}, {"type": "intent", "expected": "confirm", "actual": "confirm", "passed": true}]}], "status": "passed", "error_messages": []}, {"scenario_id": 4, "scenario_name": "错误处理测试", "total_steps": 3, "passed_steps": 2, "failed_steps": 1, "step_results": [{"input": "", "current_state": "IDLE", "status": "passed", "actual_result": {"input": "", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "empty_input", "new_state": "IDLE"}, "expected_result": {}, "error": null, "validation_details": [{"type": "error_handling", "expected": "empty_input", "actual": "empty_input", "passed": true}]}, {"input": "asdfghjkl", "current_state": "IDLE", "status": "failed", "actual_result": {"input": "asdfghjkl", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": false, "llm_calls": 1, "intent": "provide_information", "new_state": "IDLE"}, "expected_result": {}, "error": "错误处理不匹配: 期望 unknown_input, 实际 provide_information", "validation_details": [{"type": "error_handling", "expected": "unknown_input", "actual": "provide_information", "passed": false}]}, {"input": "重新开始", "current_state": "IDLE", "status": "passed", "actual_result": {"input": "重新开始", "current_state": "IDLE", "processing_time": 0.001, "used_fast_path": true, "llm_calls": 0, "intent": "restart", "matched_keyword": "重新开始", "new_state": "IDLE"}, "expected_result": {}, "error": null, "validation_details": [{"type": "error_handling", "expected": "restart_request", "actual": "restart", "passed": true}]}], "status": "failed", "error_messages": ["错误处理不匹配: 期望 unknown_input, 实际 provide_information"]}], "summary": {"total_scenarios": 4, "passed": 2, "failed": 2, "warnings": 0}}